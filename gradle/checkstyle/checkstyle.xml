<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE module PUBLIC "-//Puppy Crawl//DTD Check Configuration 1.3//EN"
        "http://www.puppycrawl.com/dtds/configuration_1_3.dtd">
<!--refer to http://checkstyle.sourceforge.net/availablechecks.html-->
<module name="Checker">
    <property name="charset" value="UTF-8"/>

    <module name="TreeWalker">
        <!--Checks the placement of left curly braces on types, methods and other blocks:-->
        <module name="LeftCurly"/>

        <!--Checks the placement of right curly braces.-->
        <module name="RightCurly"/>

        <!--Detects empty statements (standalone ';').-->
        <module name="EmptyStatement"/>

        <!--Checks that classes that override equals() also override hashCode().-->
        <module name="EqualsHashCode"/>

        <!--Checks that any combination of String literals with optional assignment is on the left side of an equals() comparison.-->
        <module name="EqualsAvoidNull"/>

        <!--Restricts the number of executable statements to a specified limit-->
        <module name="ExecutableStatementCount">
            <property name="max" value="50"/>
        </module>

        <!--Checks for long anonymous inner classes.-->
        <module name="AnonInnerLength">
            <property name="max" value="15"/>
        </module>

        <!--Checks for long methods.-->
        <module name="MethodLength">
            <property name="countEmpty" value="false"/>
            <property name="max" value="80"/>
        </module>

        <!--Checks for unused import statements.-->
        <module name="UnusedImports"/>

        <module name="AvoidStarImport"/>

        <!--Checks for imports that are redundant.-->
        <module name="RedundantImport"/>

        <!--Implements Bloch, Effective Java, Item 17 - Use Interfaces only to define types.-->
        <module name="InterfaceIsType"/>

        <!--Checks that the order of modifiers conforms to the suggestions in the Java Language specification, sections 8.1.1, 8.3.1 and 8.4.3.-->
        <module name="ModifierOrder"/>

        <!--Checks there is only one statement per line.-->
        <module name="OneStatementPerLine"/>

        <!--Checks line wrapping for operators.-->
        <module name="OperatorWrap"/>

        <!-- naming begin -->
        <module name="PackageName">
            <property name="format" value="^[a-z][a-z0-9\.]*$"/>
        </module>

        <module name="TypeName">
            <property name="format" value="^[A-Z][a-zA-Z0-9]*$"/>
        </module>

        <module name="MemberName">
            <property name="format" value="^[a-z][a-zA-Z0-9]*$"/>
        </module>

        <module name="MethodName">
            <property name="format" value="^[a-z][a-zA-Z0-9]*$"/>
        </module>

        <module name="ParameterName">
            <property name="format" value="^[a-z][a-zA-Z0-9]*$"/>
        </module>

        <module name="ConstantName"/>

        <module name="StaticVariableName"/>

        <module name="LocalVariableName"/>

        <module name="ClassTypeParameterName"/>

        <module name="MethodTypeParameterName"/>
        <!-- naming end -->

        <!--Checks the number of parameters that a method or constructor has.-->
        <module name="ParameterNumber">
            <property name="max" value="5"/>
        </module>

        <module name="Indentation"/>

        <!--Checks that there is no whitespace before a token.-->
        <module name="NoWhitespaceBefore">
            <property name="tokens" value="SEMI, DOT, POST_DEC, POST_INC"/>
            <property name="allowLineBreaks" value="true"/>
        </module>

        <!--Checks that there is no whitespace after a token.-->
        <module name="NoWhitespaceAfter">
            <property name="tokens" value="BNOT, DEC, DOT, INC, LNOT, UNARY_MINUS, UNARY_PLUS"/>
        </module>

        <!--Checks that a token is surrounded by whitespace. Empty constructor and method bodies (blocks) of the form-->
        <module name="WhitespaceAround">
            <property name="tokens"
                      value="ASSIGN, BAND, BAND_ASSIGN, BOR, BOR_ASSIGN, BSR, BSR_ASSIGN, BXOR, BXOR_ASSIGN, COLON, DIV, DIV_ASSIGN, EQUAL, GE, GT, LAND, LCURLY, LE, LITERAL_ASSERT, LITERAL_CATCH, LITERAL_DO, LITERAL_ELSE, LITERAL_FINALLY, LITERAL_FOR, LITERAL_IF, LITERAL_RETURN, LITERAL_SYNCHRONIZED, LITERAL_TRY, LITERAL_WHILE, LOR, LT, MINUS, MINUS_ASSIGN, MOD, MOD_ASSIGN, NOT_EQUAL, PLUS, PLUS_ASSIGN, QUESTION, SL, SLIST, SL_ASSIGN, SR, SR_ASSIGN, STAR, STAR_ASSIGN, TYPE_EXTENSION_AND"/>
        </module>

        <!--Checks that a token is followed by whitespace, with the exception that it does not check for whitespace after the semicolon of an empty for iterator.-->
        <module name="WhitespaceAfter"/>

        <!--Checks that the whitespace around the Generic tokens < and > are correct to the typical convention.-->
        <module name="GenericWhitespace"/>

        <!--Checks the padding between the identifier of a method definition, constructor definition, method call, or constructor invocation; and the left parenthesis of the parameter list. -->
        <module name="MethodParamPad"/>

        <!--Checks if unnecessary parentheses are used in a statement or expression.-->
        <module name="UnnecessaryParentheses"/>

        <!--Detects uncommented main methods.-->
        <module name="UncommentedMain"/>

        <!--Checks for overly complicated boolean expressions.-->
        <module name="SimplifyBooleanExpression"/>

        <!--Checks for overly complicated boolean return statements.-->
        <module name="SimplifyBooleanReturn"/>

        <!--Restricts nested for blocks to a specified depth (default = 1).-->
        <module name="NestedForDepth"/>

        <!--Restricts nested if-else blocks to a specified depth (default = 1).-->
        <module name="NestedIfDepth"/>

        <!--Restricts nested try-catch-finally blocks to a specified depth (default = 1).-->
        <module name="NestedTryDepth">
            <property name="max" value="0"/>
        </module>

        <!--Checks that each variable declaration is in its own statement and on its own line.-->
        <module name="MultipleVariableDeclarations"/>

        <!--Checks that switch statement has "default" clause.-->
        <module name="MissingSwitchDefault"/>
    </module>

    <!--Checks for long source files.-->
    <module name="FileLength">
        <property name="max" value="1500"/>
    </module>

    <!--Checks to see if a file contains a tab character.-->
    <module name="FileTabCharacter"/>

    <module name="SuppressionFilter">
        <property name="file" value="${configDir}/suppressions.xml"/>
    </module>
</module>
