<?xml version="1.0"?>
<ruleset name="Basic" xmlns="http://pmd.sourceforge.net/ruleset/2.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://pmd.sourceforge.net/ruleset/2.0.0 http://pmd.sourceforge.net/ruleset_2_0_0.xsd">
    <description>PMD rules</description>

    <rule ref="rulesets/java/basic.xml">
        <exclude name="AvoidUsingHardCodedIP"/>
    </rule>

    <rule ref="rulesets/java/strings.xml">
        <exclude name="AvoidDuplicateLiterals"/>
        <exclude name="UselessStringValueOf"/>
    </rule>

    <rule ref="rulesets/java/unusedcode.xml/UnusedPrivateField"/>
    <rule ref="rulesets/java/unusedcode.xml/UnusedLocalVariable"/>
    <rule ref="rulesets/java/unusedcode.xml/UnusedPrivateMethod"/>
    <rule ref="rulesets/java/unusedcode.xml/UnusedFormalParameter"/>
    <rule ref="rulesets/java/unusedcode.xml/UnusedModifier"/>

    <rule ref="rulesets/java/imports.xml/DuplicateImports"/>
    <rule ref="rulesets/java/imports.xml/DontImportJavaLang"/>
    <rule ref="rulesets/java/imports.xml/UnusedImports"/>
    <rule ref="rulesets/java/imports.xml/ImportFromSamePackage"/>

    <rule ref="rulesets/java/typeresolution.xml/LooseCoupling"/>
    <rule ref="rulesets/java/typeresolution.xml/CloneMethodMustImplementCloneable"/>
    <rule ref="rulesets/java/typeresolution.xml/UnusedImports"/>

    <rule ref="rulesets/java/controversial.xml/UnnecessaryConstructor"/>
    <rule ref="rulesets/java/controversial.xml/AssignmentInOperand"/>
    <rule ref="rulesets/java/controversial.xml/DontImportSun"/>
    <rule ref="rulesets/java/controversial.xml/UnnecessaryParentheses"/>
    <rule ref="rulesets/java/controversial.xml/DoNotCallGarbageCollectionExplicitly"/>

    <rule ref="rulesets/java/coupling.xml/LooseCoupling"/>

    <rule ref="rulesets/java/design.xml/SimplifyBooleanReturns"/>
    <rule ref="rulesets/java/design.xml/SimplifyBooleanExpressions"/>
    <rule ref="rulesets/java/design.xml/SwitchStmtsShouldHaveDefault"/>
    <rule ref="rulesets/java/design.xml/AvoidDeeplyNestedIfStmts"/>
    <rule ref="rulesets/java/design.xml/AvoidReassigningParameters"/>
    <rule ref="rulesets/java/design.xml/ConstructorCallsOverridableMethod"/>
    <rule ref="rulesets/java/design.xml/AccessorClassGeneration"/>
    <rule ref="rulesets/java/design.xml/FinalFieldCouldBeStatic"/>
    <rule ref="rulesets/java/design.xml/CloseResource"/>
    <rule ref="rulesets/java/design.xml/NonStaticInitializer"/>
    <rule ref="rulesets/java/design.xml/DefaultLabelNotLastInSwitchStmt"/>
    <rule ref="rulesets/java/design.xml/NonCaseLabelInSwitchStatement"/>
    <rule ref="rulesets/java/design.xml/OptimizableToArrayCall"/>
    <rule ref="rulesets/java/design.xml/BadComparison"/>
    <rule ref="rulesets/java/design.xml/EqualsNull"/>
    <rule ref="rulesets/java/design.xml/InstantiationToGetClass"/>
    <rule ref="rulesets/java/design.xml/IdempotentOperations"/>
    <rule ref="rulesets/java/design.xml/ImmutableField"/>
    <rule ref="rulesets/java/design.xml/AvoidProtectedFieldInFinalClass"/>
    <rule ref="rulesets/java/design.xml/MissingBreakInSwitch"/>
    <rule ref="rulesets/java/design.xml/UseNotifyAllInsteadOfNotify"/>
    <rule ref="rulesets/java/design.xml/AvoidInstanceofChecksInCatchClause"/>
    <rule ref="rulesets/java/design.xml/SimplifyConditional"/>
    <rule ref="rulesets/java/design.xml/CompareObjectsWithEquals"/>
    <rule ref="rulesets/java/design.xml/PositionLiteralsFirstInComparisons"/>
    <rule ref="rulesets/java/design.xml/UnnecessaryLocalBeforeReturn"/>
    <rule ref="rulesets/java/design.xml/AvoidConstantsInterface"/>
    <rule ref="rulesets/java/design.xml/UnsynchronizedStaticDateFormatter"/>
    <rule ref="rulesets/java/design.xml/PreserveStackTrace"/>
    <rule ref="rulesets/java/design.xml/UseCollectionIsEmpty"/>
    <rule ref="rulesets/java/design.xml/ClassWithOnlyPrivateConstructorsShouldBeFinal"/>
    <rule ref="rulesets/java/design.xml/SingularField"/>

    <rule ref="rulesets/java/finalizers.xml/AvoidCallingFinalize"/>

    <rule ref="rulesets/java/junit.xml/SimplifyBooleanAssertion"/>
    <rule ref="rulesets/java/junit.xml/UseAssertNullInsteadOfAssertTrue"/>
    <rule ref="rulesets/java/junit.xml/UseAssertSameInsteadOfAssertTrue"/>
    <rule ref="rulesets/java/junit.xml/UseAssertEqualsInsteadOfAssertTrue"/>
    <rule ref="rulesets/java/junit.xml/UnnecessaryBooleanAssertion"/>

    <rule ref="rulesets/java/strictexception.xml/ExceptionAsFlowControl"/>
    <rule ref="rulesets/java/strictexception.xml/AvoidCatchingNPE"/>
    <rule ref="rulesets/java/strictexception.xml/DoNotExtendJavaLangError"/>
    <rule ref="rulesets/java/strictexception.xml/DoNotThrowExceptionInFinally"/>
    <rule ref="rulesets/java/strictexception.xml/AvoidThrowingNewInstanceOfSameException"/>

    <exclude-pattern>.*/com/nsy/api/tms/logistics/fedex/.*</exclude-pattern>
    <exclude-pattern>.*/com/nsy/api/tms/logistics/ups/.*</exclude-pattern>
    <exclude-pattern>.*/com/nsy/api/tms/logistics/kts/.*</exclude-pattern>
    <exclude-pattern>.*/com/nsy/api/tms/logistics/cangsou/.*</exclude-pattern>
    <exclude-pattern>.*/com/nsy/api/tms/logistics/hzups/.*</exclude-pattern>
    <exclude-pattern>.*/com/nsy/api/tms/logistics/jiacheng/.*</exclude-pattern>
    <exclude-pattern>.*/com/nsy/api/tms/logistics/hlt/.*</exclude-pattern>
    <exclude-pattern>.*com/nsy/api/tms/service/external/refactor/winit/.*</exclude-pattern>
</ruleset>
