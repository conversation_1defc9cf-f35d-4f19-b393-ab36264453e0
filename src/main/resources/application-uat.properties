# Server settings (ServerProperties)
spring.profiles.active=stage
server.port=8080
server.servlet.context-path=/

# log
logging.config=classpath:logback-${spring.profiles.active}.xml
site.log.enableTraceLog=true
site.log.forceTraceLog=false

# jdbc
spring.datasource.url=****************************************************************************************************************************
spring.datasource.username=nsy_mysql
spring.datasource.password=t6DT6hKLSTbaI.
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.max-idle=3
spring.datasource.max-wait=10000
spring.datasource.min-idle=3
spring.datasource.initial-size=3
spring.datasource.maximum-pool-size=5
spring.datasource.hikari.maximum-pool-size=5
spring.jpa.hibernate.ddl-auto=none

# scheduler
scheduler.job.config=jobs-${spring.profiles.active}.json


# mail
spring.mail.protocol=smtp
spring.mail.host=smtp.exmail.qq.com
spring.mail.username=<EMAIL>
spring.mail.password=Bf9FEhxmUNNgPdMU
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.properties.mail.smtp.socketFactory.port=465
spring.mail.properties.mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory
spring.mail.properties.mail.smtp.connectiontimeout=30000

mail.receiver.errors=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

#us-east
transfer.service.url=http://*************:8080/
transfer.service.id=api-tms
transfer.service.secret=&##)(fas)#$@SFASf**(*#fa@234

#aliyun oss
aliyun.oss.accessKeyId=LTAI5tHmEg4bxfksAoZzMp7p
aliyun.oss.accessKeySecret=******************************
aliyun.oss.region=cn-hangzhou
aliyun.oss.bucketName=stage-nsy-erp-public

#logistics
label.folder=/mnt/data/tms/
oss.label.folder=label/

#YDH
ydh.server.url=http://customer.ydhex.com/webservice/PublicService.asmx/ServiceInterfaceUTF8

#EDL
edl.authentication.url=http://***************:8082/selectAuth.htm
edl.createorder.url=http://***************:8082/createOrderApi.htm
edl.printLabel.url=http://***************/order/FastRpt/PDF_NEW.aspx
edl.ip.url=http://***************
edl.track.url=http://***************:8082/selectTrack.htm
edl.trackNumber.url=http://***************:8082/getOrderTrackingNumber.htm

#XinShunYi
XinShunYi.createorder.url=http://xsy.kingtrans.net/PostInterfaceService?method=createOrder
XinShunYi.printLabel.url=http://xsy.kingtrans.net/PostInterfaceService?method=printOrderLabel
XinShunYi.printInvoice.url=http://xsy.kingtrans.net/PostInterfaceService?method=printOrderInvoice

#YunTu
YunTu.createorder.url=http://oms.api.yunexpress.com/api/WayBill/CreateOrder
YunTu.printLabel.url=http://oms.api.yunexpress.com/api/Label/Print
YunTu.trackNumber.url=http://oms.api.yunexpress.com/api/WayBill/GetTrackingNumber
YunTu.track.url=http://oms.api.yunexpress.com/api/Tracking/GetTrackInfo

#yikeda
yikeda.createorder.url=https://tms.hwcservice.com/V4/Api/LabelPrintService/PrintLabel
yikeda.printLabel.url=https://tms.hwcservice.com/V4/Api/LabelPrintService/PrintLabelByTrackingNumber

#JY
jy.authentication.url=http://www.jy-express.cn:8082/selectAuth.htm
jy.createorder.url=http://www.jy-express.cn:8082/createOrderApi.htm
jy.printLabel.url=http://www.jy-express.cn/order/FastRpt/PDF_NEW.aspx
jy.ip.url=http://www.jy-express.cn
jy.track.url=http://www.jy-express.cn:8082/selectTrack.htm
jy.trackNumber.url=http://www.jy-express.cn:8082/getOrderTrackingNumber.htm

#JETPLUS
jetplus.authentication.url=http://*************:8082/selectAuth.htm
jetplus.createorder.url=http://*************:8082/createOrderApi.htm
jetplus.printLabel.url=http://*************:8089/order/FastRpt/PDF_NEW.aspx
jetplus.ip.url=http://*************:8089
jetplus.track.url=http://*************:8082/selectTrack.htm
jetplus.trackNumber.url=http://*************:8082/getOrderTrackingNumber.htm

#JinMai
jinmai.authentication.url=http://***************:8082/selectAuth.htm
jinmai.createorder.url=http://***************:8082/createOrderApi.htm
jinmai.printLabel.url=http://***************:8089/order/FastRpt/PDF_NEW.aspx
jinmai.ip.url=http://***************:8089
jinmai.track.url=http://***************:8082/selectTrack.htm
jinmai.trackNumber.url=http://***************:8082/getOrderTrackingNumber.htm

#ups
ups.server.url=https://onlinetools.ups.com/api/shipments/v1/ship
ups.track.url=https://onlinetools.ups.com/rest/Track
ups.service.oauth=https://onlinetools.ups.com/security/v1/oauth/token
#Api ups production
ups.paperless.url=https://onlinetools.ups.com/api/paperlessdocuments/v1/upload
ups.paperless.match.document=https://onlinetools.ups.com/api/paperlessdocuments/v1/image
ups.paperless.accesskey=9CD0598DCEBF8946
ups.paperless.username=SHIYINGAPI
ups.paperless.password=123456Aa
ups.paperless.customerContext=SHIYING
#The Shipper's UPS Account Number
ups.paperless.shipperNumber=68R95E
ups.paperless.shipmentType=1
ups.paperless.userCreatedFormDocumentType=008
ups.paperless.userCreatedFormFileFormat=pdf

#HZUPS
hzups.server.url=http://dd10.rui-y.com:8401/lgtbws/eship/orderShip?wsdl

#cainiao
cainiao.server.url=http://link.cainiao.com/gateway/link.do

#TNT
tnt.ship.url=https://express.tnt.com/expressconnect/shipping/ship
tnt.track.url=https://express.tnt.com/expressconnect/track.do


#DHL
dhl.server.url=https://xmlpitest-ea.dhl.com/XMLShippingServlet
dhl.server.track.url=https://xmlpi-ea.dhl.com/XMLShippingServlet

#4px
4px.domain=http://open.4px.com/router/api/service
4px.track.domain=http://open.4px.com/router/api/service
#DHL\u4E0A\u4F20\u5355\u8BC1
dhl.dps.server.url=https://webhub.cndhl.com/dps

#Fedex
#fedex.wsdl.address=https://wsbeta.fedex.com:443/web-services/ship
fedex.wsdl.address=https://ws.fedex.com:443/web-services/ship
fedex.wsdl.track.address=https://ws.fedex.com:443/web-services/track
fedex.api.transfer=true

#sf
sf.wsdl.address=http://ibse.sf-express.com/CBTA/ws/sfexpressService?wsdl
sf.wsdl.track.address=http://ibse.sf-express.com/CBTA/ws/sfexpressService?wsdl

#epacket
epacket.order.url=http://shipping.ems.com.cn/partner/api/public/p/order/
epacket.label.url=http://labels.ems.com.cn/partner/api/public/p/static/label/download/%s/%s.pdf
epacket.track.url=http://shipping.ems.com.cn/partner/api/public/p/track/query/%s/%s

#cne
cne.order.url=https://api.cne.com/cgi-bin/EmsData.dll?DoApi
cne.label.url=http://label.cne.com/CnePrint?icID=%s&cNos=%s&ptemp=%s&signature=%s
cne.track.url=https://apitracking.cne.com/client/track

# erp api service
erp.api.service.url=http\://*************:8081

# user api service
user.api.service.url=http://api-user:8080

oms-publish.api.service.url=http://api-oms-publish.test-intranet:8080
oms.service.url= http://api-oms.test-intranet:8080
amazon.service.url = http://api-amazon:8080

wms.api.service.url=http://api-wms.test-intranet:8080

#aliexpress
aliexpress.server.url=http://*************/Aliexpress

#pfcexpress
pfcexpress.server.url=http://*************/webservice/APIWebService.asmx

#kts
kts.wsdl.address=http://sfapi.trackmeeasy.com/ruserver/webservice/sfexpressService?wsdl
kts.label.url=http://sfapi.trackmeeasy.com/ruserver/api/getLabelUrl.action?orderid=%s&mailno=%s&onepdf=%s&jianhuodan=%s&username=%s&signature=%s
kts.track.url=http://sfapi.trackmeeasy.com/ruserver/api/getRoutes.action

#cpam
cpam.server.url=https://my.ems.com.cn/pcpErp-web/a/pcp/orderService/OrderReceiveBack
cpam.label.url=https://my.ems.com.cn/pcpErp-web/a/pcp/surface/download

#\uFFFD\u0675\u0779\u073C\uFFFD
sudiguanjia.server.url=http://*************:8041/webservice/PublicService.asmx/ServiceInterfaceUTF8

# \u5FB7\u542F
deqi.createorder.url=http://www.tpdex.com/interface/order/create

# \u4F73\u6210
jiacheng.server.url=http://order.jcex.com:8080/JcexJson/api/notify/sendmsg
# Seko
seko.server.url=https://staging.omniparcel.com

# \u6210\u90FDE\u90AE\u5B9D
chengduEpacket.authentication.url=http://*************:8082/selectAuth.htm
chengduEpacket.order.url=http://*************:8082/createOrderApi.htm
chengduEpacket.printLabel.url=http://*************:8089/order/FastRpt/PDF_NEW.aspx
chengduEpacket.ip.url=http://*************:8089
chengduEpacket.track.url=http://*************:8082/selectTrack.htm
chengduEpacket.trackNumber.url=http://*************:8082/getOrderTrackingNumber.htm

proxyIps=***********;***********


#Fedex-V2
fedex.service.order.v2=https://apis-sandbox.fedex.com/ship/v1/shipments
fedex.service.auth.v2=https://apis-sandbox.fedex.com/oauth/token
fedex.service.upload.image.v2=https://documentapitest.prod.fedex.com/sandbox/documents/v1/lhsimages/upload

# ï¿½ï¿½ï¿½ÏµÍ³
dianCang.server.url=http://api.d-cang.com/api

# etower
eTower.server.url=http://qa.etowertech.com

#afmä»
dmxsmart.server.url=https://api.dmxsmart.com/api/openapi/v12/services

nsy.service.url.user=http://api-user:8080

jiuFangYunCang.server.url=http://************/default/svc/web-service