CREATE TABLE IF NOT EXISTS tms_config_group (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `key_group` VARCHAR(100) COMMENT 'key_group',
  `description` VARCHAR (50) COMMENT '描述'
) ENGINE = INNODB COMMENT 'tms 配置组' ;

INSERT INTO tms_config_group (`key_group`, `description`) VALUES ('FedEx', 'FedEx');
INSERT INTO tms_config_group (`key_group`, `description`) VALUES ('E邮宝', 'E邮宝');
INSERT INTO tms_config_group (`key_group`, `description`) VALUES ('顺丰国际', '顺丰国际');
INSERT INTO tms_config_group (`key_group`, `description`) VALUES ('菜鸟物流', '菜鸟物流');
INSERT INTO tms_config_group (`key_group`, `description`) VALUES ('UPS', 'UPS');
INSERT INTO tms_config_group (`key_group`, `description`) VALUES ('EDL', 'EDL');
INSERT INTO tms_config_group (`key_group`, `description`) VALUES ('CNE', 'CNE');
INSERT INTO tms_config_group (`key_group`, `description`) VALUES ('TNT', 'TNT');
INSERT INTO tms_config_group (`key_group`, `description`) VALUES ('DHL', 'DHL');
INSERT INTO tms_config_group (`key_group`, `description`) VALUES ('4PX', '4PX');
INSERT INTO tms_config_group (`key_group`, `description`) VALUES ('FedEx_Track', 'FedEx_Track');
INSERT INTO tms_config_group (`key_group`, `description`) VALUES ('E邮宝_Track', 'E邮宝_Track');
INSERT INTO tms_config_group (`key_group`, `description`) VALUES ('顺丰国际_Track', '顺丰国际_Track');
INSERT INTO tms_config_group (`key_group`, `description`) VALUES ('UPS_Track', 'UPS_Track');
INSERT INTO tms_config_group (`key_group`, `description`) VALUES ('TNT_Track', 'TNT_Track');
INSERT INTO tms_config_group (`key_group`, `description`) VALUES ('DHL_Track', 'DHL_Track');
INSERT INTO tms_config_group (`key_group`, `description`) VALUES ('4PX_Track', '4PX_Track');