/*
* =============================================================================
* Designer: Administrator@ZHENGMEIYUE-PC
* Description: nsy_380104
* Created: 2021/09/06 14:36:52
* ============================================================================= 
*/
ALTER TABLE tms_logistics_country_province_mapping DROP COLUMN logistics_country_id;

INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Afghanistan','阿富汗','AF','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Andorra','安道尔','AD','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Antigua','安提瓜','AG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Anguilla','安圭拉岛','AI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Albania','阿尔巴尼亚','AL','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Armenia','亚美尼亚','AM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Netherlands Antilles','荷属安的列斯群岛','AN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Angola','安哥拉','AO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Argentina','阿根廷','AR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','American Samoa','美国萨摩亚','AS','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Austria','奥地利','AT','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Australia','澳大利亚','AU','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Aruba','阿鲁巴岛','AW','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Azerbaijan','阿塞拜疆','AZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Bosnia and Herzegovina','波斯尼亚和黑塞哥维那','BA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Barbados','巴巴多斯','BB','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Bangladesh','孟加拉国','BD','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Belgium','比利时','BE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Burkina Faso','布基纳法索','BF','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Bulgaria','保加利亚','BG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Bahrain','巴林','BH','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Burundi','布隆迪','BI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Benin','贝宁','BJ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Bermuda','百慕大群岛','BM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Brunei','文莱','BN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Bolivia','玻利维亚','BO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Brazil','巴西','BR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Bahamas','巴哈马群岛','BS','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','United Arab Emirates','阿拉伯联合酋长国','AE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Bhutan','不丹','BT','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Botswana','博茨瓦纳','BW','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Belarus','白俄罗斯','BY','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Belize','伯利兹','BZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Congo, The Democratic Republic of','刚果民主共和国，','CD','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Central African Republic','中非共和国','CF','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Congo','刚果','CG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Switzerland','瑞士','CH','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Cote d＇Ivoire','科特迪瓦＇科特迪瓦','CI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Cook Islands','库克群岛','CK','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Chile','智利','CL','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Cameroon','喀麦隆','CM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','China, People＇s Republic','中国人民共和国，＇','CN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Colombia','哥伦比亚','CO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Costa Rica','哥斯达黎加','CR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Cuba','古巴','CU','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Cape Verde','佛得角','CV','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Cyprus','塞浦路斯','CY','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Czech Republic, The','捷克共和国的，','CZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Germany','德国','DE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Djibouti','吉布提','DJ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Denmark','丹麦','DK','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Dominica','多米尼加','DM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Dominican Rep.','多米尼加共和国','DO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Algeria','阿尔及利亚','DZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Ecuador','厄瓜多尔','EC','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Estonia','爱沙尼亚','EE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Egypt','埃及','EG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Eritrea','厄立特里亚','ER','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Spain','西班牙','ES','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Ethiopia','埃塞俄比亚','ET','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Finland','芬兰','FI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Fiji','斐济','FJ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Falkland Islands','福克兰群岛','FK','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','MICRONESIA, FEDERATED STATES OF','密克罗尼西亚，联邦国家','FM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Faroe Islands','法罗群岛','FO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','France','法国','FR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Gabon','加蓬','GA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','United Kingdom','联合王国','GB','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Grenada','格林纳达','GD','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Georgia','格鲁吉亚','GE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Israel','以色列','IL','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','India','印度','IN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Iraq','伊拉克','IQ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Iran (Islamic Republic of)','伊朗（伊斯兰共和国）','IR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Italy','意大利','IT','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Jersey','泽西','JE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Jamaica','牙买加','JM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Jordan','乔丹','JO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Japan','日本','JP','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Kenya','肯尼亚','KE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Kyrgyzstan','吉尔吉斯斯坦','KG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Cambodia','柬埔寨','KH','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Kiribati','基里巴斯','KI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Comoros','科摩罗','KM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','St. Kitts','圣基茨','KN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Korea, The D.P.R of','韩国，这个d.p.r的','KP','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Korea, Republic Of','韩国，共和国','KR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Kosovo','科索沃','KV','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Kuwait','科威特','KW','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Cayman Islands','开曼群岛','KY','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Kazakhstan','哈萨克斯坦','KZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Lao People＇s Democratic Republic','老挝人民民主共和国＇','LA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Lebanon','黎巴嫩','LB','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','St. Lucia','圣露西亚','LC','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Liechtenstein','列支敦士登','LI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Sri Lanka','斯里兰卡','LK','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Liberia','利比里亚','LR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Lesotho','莱索托','LS','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Lithuania','立陶宛','LT','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Luxembourg','卢森堡','LU','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Latvia','拉脱维亚','LV','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Libya','利比亚','LY','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Morocco','摩洛哥','MA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Monaco','摩纳哥','MC','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Moldova, Republic Of','摩尔多瓦，共和国','MD','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Montenegro, Republic of','黑山共和国，','ME','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Madagascar','马达加斯加','MG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Marshall Islands','马绍尔群岛','MH','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Macedonia, Rep. of (FYROM)','马其顿，众议员（马其顿）','MK','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Mali','马里','ML','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Myanmar','缅甸','MM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Mongolia','蒙古','MN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Macau','澳门','MO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Saipan','塞班岛','MP','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Martinique','马提尼克','MQ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Mauritania','毛里塔尼亚','MR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Montserrat','蒙特塞拉特岛','MS','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Malta','马耳他','MT','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Mauritius','毛里求斯','MU','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Maldives','马尔代夫','MV','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Malawi','马拉维','MW','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Mexico','墨西哥','MX','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Malaysia','马来西亚','MY','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Mozambique','莫桑比克','MZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Namibia','纳米比亚','NA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','New Caledonia','新喀里多尼亚','NC','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Niger','尼日尔','NE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Nigeria','尼日利亚','NG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Nicaragua','尼加拉瓜','NI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Netherlands, The','在荷兰，','NL','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Norway','挪威','NO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Nepal','尼泊尔','NP','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Nauru, Republic Of','瑙鲁，共和国','NR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Niue','纽埃','NU','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','New Zealand','新西兰','NZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Oman','阿曼','OM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Panama','巴拿马','PA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Peru','秘鲁','PE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Tahiti','塔希提','PF','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Papua New Guinea','巴布亚新几内亚','PG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Philippines, The','菲律宾，这个','PH','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Pakistan','巴基斯坦','PK','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Poland','波兰','PL','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Puerto Rico','波多黎各','PR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Portugal','葡萄牙','PT','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Palau','帕劳群岛','PW','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Paraguay','巴拉圭','PY','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Qatar','卡塔尔','QA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Reunion, Island Of','留尼旺岛，','RE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Romania','罗马尼亚','RO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Serbia, Republic of','塞尔维亚，共和国','RS','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Russian Federation, The','俄罗斯联邦，这','RU','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Rwanda','卢旺达','RW','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Saudi Arabia','沙乌地阿拉伯','SA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Solomon Islands','所罗门群岛','SB','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Seychelles','塞舌尔','SC','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Sudan','苏丹','SD','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Sweden','瑞典','SE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Singapore','新加坡','SG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','SAINT HELENA','圣海伦娜','SH','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Iceland','冰岛','IS','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Slovenia','斯洛文尼亚','SI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Slovakia','斯洛伐克','SK','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Sierra Leone','塞拉利昂','SL','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','San Marino','圣马力诺','SM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Senegal','塞内加尔','SN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Somalia','索马里','SO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Suriname','苏里南','SR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','SOUTH SUDAN','南苏丹','SS','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Sao Tome and Principe','圣多美和普林西比','ST','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','El Salvador','萨尔瓦多','SV','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Syria','叙利亚','SY','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Swaziland','斯威士兰','SZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Turks and Caicos Islands','特克斯和凯科斯群岛','TC','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Chad','乍得','TD','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Togo','多哥','TG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Thailand','泰国','TH','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Tajikistan','塔吉克斯坦','TJ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','East Timor','东帝汶','TL','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Tunisia','突尼斯','TN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Tonga','汤加','TO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Turkey','土耳其','TR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Trinidad and Tobago','特立尼达，多巴哥','TT','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Tuvalu','图瓦卢','TV','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Taiwan','台湾','TW','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Tanzania','坦桑尼亚','TZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Ukraine','乌克兰','UA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Uganda','乌干达','UG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','United States','美国美国','US','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Uruguay','乌拉圭','UY','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Uzbekistan','乌兹别克斯坦','UZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','St. Vincent','圣文森','VC','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Venezuela','委内瑞拉','VE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Virgin Islands (British)','维尔京群岛（英国）','VG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Virgin Islands (US)','维尔京群岛（美国）','VI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Vietnam','越南','VN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Vanuatu','瓦努阿图','VU','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Samoa','萨摩亚','WS','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Bonaire','博内尔岛','XB','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Curacao','库拉索岛','XC','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','St. Eustatius','圣尤斯达求斯','XE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','St. Maarten','圣马丁','XM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Nevis','尼维斯','XN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Somaliland, Rep of (North Somalia)','索马里兰，代表（北索马里）','XS','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','St. Barthelemy','圣巴泰勒米岛','XY','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Yemen, Republic of','也门，共和国','YE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Mayotte','马约特岛','YT','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','South Africa','南非','ZA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Zambia','赞比亚','ZM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Zimbabwe','津巴布韦','ZW','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','French Guyana','法国圭亚那','GF','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Guernsey','格恩西','GG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Ghana','加纳','GH','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Gibraltar','直布罗陀','GI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Greenland','格陵兰岛','GL','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Gambia','冈比亚','GM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Guinea Republic','几内亚共和国','GN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Guadeloupe','瓜德罗普岛','GP','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','GuineaEquatorial','赤道几内亚','GQ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Greece','希腊','GR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Guatemala','瓜地马拉','GT','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Guam','关岛','GU','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','GuineaBissau','几内亚比绍','GW','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Guyana (British)','圭亚那（英国）','GY','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Hong Kong','香港','HK','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Honduras','洪都拉斯','HN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Croatia','克罗地亚','HR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Haiti','海地','HT','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Hungary','匈牙利','HU','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Canary Islands, The','加那利群岛，这','IC','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Indonesia','印度尼西亚','ID','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Ireland, Republic Of','爱尔兰共和国，','IE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('DHL','Canada','加拿大','CA','zmy');

INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Canada','加拿大','CA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Albania','阿尔巴尼亚','AL','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Algeria','阿尔及利亚','DZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','American Samoa','美属萨摩亚','AS','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Andorra','安道尔','AD','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Angola','安哥拉','AO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Anguilla','安圭拉岛','AI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Antigua','安提瓜岛','AG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Argentina','阿根廷','AR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Armenia','亚美尼亚','AM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Aruba','阿鲁巴岛','AW','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Australia','澳大利亚','AU','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Austria','奥地利','AT','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Azerbaijan','阿塞拜疆','AZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Bahamas','巴哈马群岛','BS','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Bahrain','巴林','BH','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Bangladesh','孟加拉国','BD','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Barbados','巴巴多斯','BB','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Belarus','白俄罗斯','BY','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Belgium','比利时','BE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Belize','伯利兹','BZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Benin','贝宁','BJ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Bermuda','百慕大群岛','BM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Bhutan','不丹','BT','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Bolivia','玻利维亚','BO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Botswana','博茨瓦纳','BW','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Brazil','巴西','BR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','British Virgin Islands','英属维尔京群岛','VG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Brunei','文莱','BN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Bulgaria','保加利亚','BG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Burkino Faso','布基纳法索','BF','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Burundi','布隆迪','BI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Cambodia','柬埔寨','KH','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Cameroon','喀麦隆','CM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Cape Verde','佛得角','CV','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Cayman Islands','开曼群岛','KY','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Central African','中部非洲','CF','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Chad','乍得','TD','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Chile','智利','CL','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','China','中国','CN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Colombia','哥伦比亚','CO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Congo','刚果','CG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Congo, The Republic of','NULL','CD','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Cook Islands','库克群岛','CK','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Costa Rica','哥斯达黎加','CR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Ivory Coast','象牙海岸','CI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Croatia','克罗地亚','HR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Cyprus','塞浦路斯','CY','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Czech Republic','捷克共和国','CZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Denmark','丹麦','DK','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Djibouti','吉布提','DJ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Dominica','多米尼加','DM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Dominican Republic','多米尼加共和国','DO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Ecuador','厄瓜多尔','EC','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Egypt','埃及','EG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','El Salvador','萨尔瓦多','SV','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Equatorial Guinea','赤道几内亚','GQ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Eritrea','厄立特里亚','ER','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Estonia','爱沙尼亚','EE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Ethiopia','埃塞俄比亚','ET','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Faeroe Islands','法罗群岛','FO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Fiji','斐济','FJ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Finland','芬兰','FI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','France','法国','FR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','French Guiana','法属圭亚那','GF','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','French Polynesia','法属波利尼西亚','PF','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Gabon','加蓬','GA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Gambia','冈比亚','GM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Georgia, Republic?of','NULL','GE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Germany','德国','DE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Ghana','加纳','GH','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Gibraltar','直布罗陀','GI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Greece','希腊','GR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Greenland','格陵兰岛','GL','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Grenada','格林纳达','GD','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Guadeloupe','瓜德罗普岛','GP','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Guam','关岛','GU','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Guatemala','瓜地马拉','GT','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Guinea','几内亚','GN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Guinea-Bissau','几内亚比绍共和国','GW','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Guyana','圭亚那','GY','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Haiti','海地','HT','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Honduras','洪都拉斯','HN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Hong Kong','香港','HK','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Hungary','匈牙利','HU','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Iceland','冰岛','IS','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','India','印度','IN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Indonesia','印度尼西亚','ID','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Ireland','爱尔兰','IE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Israel','以色列','IL','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Italy','意大利','IT','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Jamaica','牙买加','JM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Japan','日本','JP','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Jordan','乔丹','JO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Kazakhstan','哈萨克斯坦','KZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Kenya','肯尼亚','KE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Kuwait','科威特','KW','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Kyrgyzstan','吉尔吉斯斯坦','KG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Latvia','拉脱维亚','LV','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Lebanon','黎巴嫩','LB','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Lesotho','莱索托','LS','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Liechtenstein','列支敦士登','LI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Lithuania','立陶宛','LT','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Luxembourg','卢森堡','LU','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Macau','澳门','MO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Macedonia','马其顿','MK','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Madagascar','马达加斯加','MG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Malawi','马拉维','MW','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Malaysia','马来西亚','MY','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Maldives','马尔代夫','MV','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Mali','马里','ML','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Malta','马耳他','MT','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Marshall Islands','马绍尔群岛','MH','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Martinique','马提尼克','MQ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Mauritania','毛里塔尼亚','MR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Mauritius','毛里求斯','MU','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Mexico','墨西哥','MX','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Micronesia','密克罗尼西亚','FM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Moldova','摩尔多瓦','MD','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Monaco','摩纳哥','MC','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Mongolia','蒙古','MN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Montserrat','蒙特塞拉特岛','MS','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Morocco','摩洛哥','MA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Mozambique','莫桑比克','MZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Burma','缅甸','MM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Namibia','纳米比亚','NA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Nepal','尼泊尔','NP','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Netherlands','荷兰','NL','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','New Caledonia','新喀里多尼亚','NC','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','New Zealand','新西兰','NZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Nicaragua','尼加拉瓜','NI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Niger','尼日尔','NE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Nigeria','尼日利亚','NG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Norway','挪威','NO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Oman','阿曼','OM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Pakistan','巴基斯坦','PK','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Palau','帕劳群岛','PW','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Panama','巴拿马','PA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Papua New Guinea','巴布亚新几内亚','PG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Paraguay','巴拉圭','PY','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Peru','珀鲁','PE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Philippines','菲律宾','PH','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Poland','波兰','PL','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Portugal','葡萄牙','PT','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','United States','美国','US','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Qatar','卡塔尔','QA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Reunion Island','留尼旺岛','RE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Romania','罗马尼亚','RO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Russia','俄罗斯','RU','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Rwanda','卢旺达','RW','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Saipan','塞班岛','MP','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','San Marino','圣马力诺','SM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Saudi Arabia','沙乌地阿拉伯','SA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Senegal','塞内加尔','SN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Seychelles','塞舌尔','SC','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Sierra Leone','塞拉利昂','SL','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Singapore','新加坡','SG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Slovak Republic','斯洛伐克共和国','SK','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Slovenia','斯洛文尼亚','SI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','South Africa','南非','ZA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','South Korea','韩国','KR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Spain','西班牙','ES','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Sri Lanka','斯里兰卡','LK','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','St. Kitts & Nevis','圣基茨和尼维斯','KN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','St. Lucia','圣露西亚','LC','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','St. Vincent','圣文森','VC','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Suriname','苏里南','SR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Swaziland','斯威士兰','SZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Sweden','瑞典','SE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Switzerland','瑞士','CH','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Syria','叙利亚','SY','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Taiwan','台湾','TW','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Tanzania','坦桑尼亚','TZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Thailand','泰国','TH','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Togo','NULL','TG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Trinidad & Tobago','NULL','TT','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Tunisia','突尼斯','TN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Turkey','土耳其','TR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Turkmenistan, Republic?of','NULL','TM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Turks & Caicos?Is.','NULL','TC','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','U.A.E.','阿拉伯联合酋长国','AE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','U.S. Virgin Islands','美属维尔京群岛','VI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Uganda','乌干达','UG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Ukraine','乌克兰','UA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','United Kingdom','（大不列颠）联合王国','GB','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Uruguay','乌拉圭','UY','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Uzbekistan','乌兹别克斯坦','UZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Vanuatu','瓦努阿图','VU','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Vatican City','梵蒂冈城','VA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Venezuela','委内瑞拉','VE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Vietnam','越南','VN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Wallis & Futuna Islands','沃利斯及富图纳群岛','WF','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Yemen','也门','YE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Zambia','赞比亚','ZM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Zimbabwe','津巴布韦','ZW','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('FedEx','Puerto Rico','波多黎各','PR','zmy');


INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','CA','ALBERTA','AB
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','CA','BRITISH COLUMBIA','BC
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','CA','MANITOBA','MB
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','CA','NEW BRUNSWICK','NB
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','CA','NEWFOUNDLAND','NF
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','CA','NORTHWEST TERR&NUNAVUT','NT
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','CA','NOVA SCOTIA','NS
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','CA','NUNAVUT','NU
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','CA','ONTARIO','ON
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','CA','PRINCE EDWARD ISLAND','PE
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','CA','QUEBEC','PQ
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','CA','SASKATCHEWAN','SK
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','CA','YUKON','YT
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','ALABAMA','AL
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','ALASKA','AK
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','ARIZONA','AZ
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','ARKANSAS','AR
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','CALIFORNIA','CA
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','COLORADO','CO
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','CONNECTICUT','CT
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','DELAWARE','DE
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','DISTRICT OF COLUMBIA','DC
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','FLORIDA','FL
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','GEORGIA','GA
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','HAWAII','HI
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','IDAHO','ID
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','ILLINOIS','IL
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','INDIANA','IN
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','IOWA','IA
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','KANSAS','KS
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','KENTUCKY','KY
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','LOUISIANA','LA
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','MAINE','ME
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','MARYLAND','MD
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','MASSACHUSETTS','MA
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','MICHIGAN','MI
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','MINNESOTA','MN
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','MISSISSIPPI','MS
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','MISSOURI','MO
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','MONTANA','MT
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','NEBRASKA','NE
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','NEVADA','NV
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','NEW HAMPSHIRE','NH
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','NEW JERSEY','NJ
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','NEW MEXICO','NM
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','NEW YORK','NY
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','NORTH CAROLINA','NC
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','NORTH DAKOTA','ND
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','OHIO','OH
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','OKLAHOMA','OK
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','OREGON','OR
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','PENNSYLVANIA','PA
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','RHODE ISLAND','RI
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','SOUTH CAROLINA','SC
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','SOUTH DAKOTA','SD
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','TENNESSEE','TN
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','TEXAS','TX
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','UTAH','UT
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','VERMONT','VT
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','VIRGINIA','VA
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','WASHINGTON','WA
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','WEST VIRGINIA','WV
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','WISCONSIN','WI
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','WYOMING','WY
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','US','Puerto Rico','PR
','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('FedEx','PR','Puerto Rico','PR','zmy');


INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Canada','加拿大','CA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Afghanistan','阿富汗','AF','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Albania','阿尔巴尼亚','AL','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Algeria','阿尔及利亚','DZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','American Samoa','美属萨摩亚','AS','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Andorra','安道尔','AD','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Angola','安哥拉','AO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Anguilla','安圭拉岛','AI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Antigua and Barbuda','NULL','AG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Argentina','阿根廷','AR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Armenia','亚美尼亚','AM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Aruba','阿鲁巴岛','AW','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Australia','澳大利亚','AU','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Austria','奥地利','AT','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Azerbaijan','阿塞拜疆','AZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Azores','亚速尔群岛','A2','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Bahamas','巴哈马群岛','BS','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Bahrain','巴林','BH','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Bangladesh','孟加拉国','BD','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Barbados','巴巴多斯','BB','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Belarus','白俄罗斯','BY','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Belgium','比利时','BE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Belize','伯利兹','BZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Benin','贝宁','BJ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Bermuda','百慕大群岛','BM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Bhutan','不丹','BT','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Bolivia','玻利维亚','BO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Bonaire','博内尔岛','BL','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Bosnia','波斯尼亚','BA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Botswana','博茨瓦纳','BW','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Brazil','巴西','BR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','British Virgin Islands','英属维尔京群岛','VG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Brunei','文莱','BN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Bulgaria','保加利亚','BG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Burkina Faso','布基纳法索','BF','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Burundi','布隆迪','BI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Cambodia','柬埔寨','KH','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Cameroon','喀麦隆','CM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Canary Islands','加那利群岛','IC','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Cape Verde Island','佛得角群岛','CV','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Cayman Islands','开曼群岛','KY','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Central African Republic','中非共和国','CF','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Chad','乍得','TD','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Chile','智利','CL','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','China','中国','CN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Colombia','哥伦比亚','CO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Comoros','科摩罗','KM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Congo','刚果','CG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Cook Islands','库克群岛','CK','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Costa Rica','哥斯达黎加','CR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Croatia','克罗地亚','HR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Curacao','库拉索岛','CB','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Cyprus','塞浦路斯','CY','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Czech Republic','捷克共和国','CZ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Denmark','丹麦','DK','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Djibouti','吉布提','DJ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Dominica','多米尼加','DM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Dominican Republic','多米尼加共和国','DO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','East Timor','东帝汶','TL','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Ecuador','厄瓜多尔','EC','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Egypt','埃及','EG','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','El Salvador','萨尔瓦多','SV','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','England','英格兰','EN','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Equatorial Guinea','赤道几内亚','GQ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Eritrea','厄立特里亚','ER','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Estonia','爱沙尼亚','EE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Ethiopia','埃塞俄比亚','ET','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Europe','欧洲','EP','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Faeroe Islands','法罗群岛','FO','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Fiji','斐济','FJ','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Finland','芬兰','FI','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','France','法国','FR','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','French Guiana','法属圭亚那','GF
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','French Polynesia','法属波利尼西亚','PF
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Gabon','加蓬','GA
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Gambia','冈比亚','GM
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Georgia','格鲁吉亚','GE
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Germany','德国','DE
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Ghana','加纳','GH
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Gibraltar','直布罗陀','GI
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Greece','希腊','GR
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Greenland','格陵兰岛','GL
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Grenada','格林纳达','GD
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Guadeloupe','瓜德罗普岛','GP
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Guam','关岛','GU
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Guatemala','瓜地马拉','GT
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Guernsey','NULL','GG
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Guinea','几内亚','GN
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Guinea-Bissau','几内亚比绍共和国','GW
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Guyana','圭亚那','GY
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Haiti','海地','HT
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Holland','荷兰','HO
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Honduras','洪都拉斯','HN
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Hong Kong','香港','HK
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Hungary','匈牙利','HU
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Iceland','冰岛','IS
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','India','印度','IN
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Indonesia','印度尼西亚','ID
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Iraq','伊拉克','IQ
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Ireland','爱尔兰','IE
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Israel','以色列','IL
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Italy','意大利','IT
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Ivory Coast','象牙海岸','CI
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Jamaica','牙买加','JM
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Japan','日本','JP
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Jersey','泽西','JE
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Jordan','乔丹','JO
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Kazakhstan','哈萨克斯坦','KZ
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Kenya','肯尼亚','KE
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Kiribati','基里巴斯','KI
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','South Korea','韩国','KR
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Kosrae','库赛埃','KO
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Kuwait','科威特','KW
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Kyrgyzstan','吉尔吉斯斯坦','KG
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Laos','老挝','LA
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Latvia','拉脱维亚','LV
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Lebanon','黎巴嫩','LB
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Lesotho','莱索托','LS
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Liberia','利比里亚','LR
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Libya','利比亚','LY
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Liechtenstein','列支敦士登','LI
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Lithuania','立陶宛','LT
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Luxembourg','卢森堡','LU
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Macau','澳门','MO
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Macedonia','马其顿','MK
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Madagascar','马达加斯加','MG
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Madeira','马德拉群岛','M3
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Malawi','马拉维','MW
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Malaysia','马来西亚','MY
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Maldives','马尔代夫','MV
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Mali','马里','ML
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Malta','马耳他','MT
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Marshall Islands','马绍尔群岛','MH
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Martinique','马提尼克','MQ
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Mauritania','毛里塔尼亚','MR
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Mauritius','毛里求斯','MU
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Mayotte','马约特岛','YT
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Mexico','墨西哥','MX
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Micronesia, Federated States of','密克罗尼西亚联邦','FM
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Moldova','摩尔多瓦','MD
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Monaco','摩纳哥','MC
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Mongolia','蒙古','MN
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Montenegro','黑山共和国','ME
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Montserrat','蒙特塞拉特岛','MS
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Morocco','摩洛哥','MA
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Mozambique','莫桑比克','MZ
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Namibia','纳米比亚','NA
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Nepal','尼泊尔','NP
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Netherlands','荷兰','NL
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','New Caledonia','新喀里多尼亚','NC
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','New Zealand','新西兰','NZ
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Nicaragua','尼加拉瓜','NI
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Niger','尼日尔','NE
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Nigeria','尼日利亚','NG
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Norfolk Island','诺福克岛','NF
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Northern Ireland','北爱尔兰','NB
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Northern Mariana Islands','北马里亚纳群岛','MP
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Norway','挪威','NO
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Oman','阿曼','OM
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Pakistan','巴基斯坦','PK
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Palau','帕劳群岛','PW
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Panama','巴拿马','PA
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Papua New Guinea','巴布亚新几内亚','PG
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Paraguay','巴拉圭','PY
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Peru','珀鲁','PE
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Philippines','菲律宾','PH
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Poland','波兰','PL
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Ponape','波纳佩岛','PO
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Portugal','葡萄牙','PT
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Puerto Rico','波多黎各','PR
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Qatar','卡塔尔','QA
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Reunion','留尼旺岛','RE
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Romania','罗马尼亚','RO
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Rota','罗塔','RT
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Russia','俄罗斯','RU
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Rwanda','卢旺达','RW
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Saba','萨巴','SS
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Saipan','塞班岛','SP
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Samoa','萨摩亚','WS
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','San Marino','圣马力诺','SM
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Saudi Arabia','沙乌地阿拉伯','SA
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Scotland','苏格兰','SF
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Senegal','塞内加尔','SN
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Serbia','塞尔维亚','RS
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Seychelles','塞舌尔','SC
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Sierra Leone','塞拉利昂','SL
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Singapore','新加坡','SG
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Slovakia','斯洛伐克','SK
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Slovenia','斯洛文尼亚','SI
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Solomon Islands','所罗门群岛','SB
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','South Africa','南非','ZA
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Spain','西班牙','ES
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Sri Lanka','斯里兰卡','LK
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','St. Barthelemy','圣巴泰勒米岛','NT
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','St. Christopher','圣克里斯托弗','SW
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','St. Croix','圣克罗伊','SX
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','St. Eustatius','圣尤斯达求斯','EU
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','St. John','圣约翰','UV
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','St. Kitts and Nevis','圣基茨和尼维斯','KN
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','St. Lucia','圣露西亚','LC
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','St. Maarten','圣马丁','MB
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','St. Martin','NULL','TB
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','St. Thomas','圣汤姆斯','VL
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','St. Vincent and the Grenadines','圣文森特和格林纳丁斯','VC
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Suriname','苏里南','SR
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Swaziland','斯威士兰','SZ
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Sweden','瑞典','SE
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Switzerland','瑞士','CH
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Tahiti','塔希提','TA
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Taiwan','台湾','TW
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Tajikistan','塔吉克斯坦','TJ
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Tanzania','坦桑尼亚','TZ
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Thailand','泰国','TH
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Tinian','提尼安岛（天宁岛）','TI
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Togo','NULL','TG
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Tonga','NULL','TO
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Tortola','NULL','ZZ
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Trinidad and Tobago','NULL','TT
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Truk','特鲁克岛','TU
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Tunisia','突尼斯','TN
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Turkey','土耳其','TR
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Turkmenistan','土库曼斯坦','TM
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Turks and Caicos Islands','NULL','TC
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Tuvalu','图瓦卢','TV
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','U.S. Virgin Islands','美属维尔京群岛','VI
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Uganda','乌干达','UG
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Ukraine','乌克兰','UA
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Union Island','NULL','UI
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','United Arab Emirates','NULL','AE
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','United Kingdom','（大不列颠）联合王国','GB
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','United States','美国','US
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Uruguay','乌拉圭','UY
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Uzbekistan','乌兹别克斯坦','UZ
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Vanuatu','瓦努阿图','VU
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Vatican City State','梵蒂冈城','VA
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Venezuela','委内瑞拉','VE
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Vietnam','越南','VN
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Virgin Gorda','美属维尔京群岛','VR
','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Wales','威尔士','WL','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Wallis and Futuna Islands','NULL','WF','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Yap','雅浦岛','YA','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Yemen','也门','YE','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Zambia','赞比亚','ZM','zmy');
INSERT INTO tms_logistics_country (logistics_company, english_name, chinese_name, country_code, create_by) values('UPS','Zimbabwe','津巴布韦','ZW','zmy');

INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','CA','ALBERTA','AB','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','CA','BRITISH COLUMBIA','BC','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','CA','MANITOBA','MB','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','CA','NEW BRUNSWICK','NB','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','CA','NEWFOUNDLAND','NF','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','CA','NORTHWEST TERR&NUNAVUT','NT','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','CA','NOVA SCOTIA','NS','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','CA','NUNAVUT','NU','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','CA','ONTARIO','ON','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','CA','PRINCE EDWARD ISLAND','PE','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','CA','QUEBEC','PQ','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','CA','SASKATCHEWAN','SK','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','CA','YUKON','YT','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','ALABAMA','AL','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','ALASKA','AK','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','ARIZONA','AZ','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','ARKANSAS','AR','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','CALIFORNIA','CA','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','COLORADO','CO','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','CONNECTICUT','CT','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','DELAWARE','DE','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','DISTRICT OF COLUMBIA','DC','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','FLORIDA','FL','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','GEORGIA','GA','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','HAWAII','HI','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','IDAHO','ID','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','ILLINOIS','IL','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','INDIANA','IN','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','IOWA','IA','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','KANSAS','KS','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','KENTUCKY','KY','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','LOUISIANA','LA','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','MAINE','ME','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','MARYLAND','MD','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','MASSACHUSETTS','MA','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','MICHIGAN','MI','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','MINNESOTA','MN','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','MISSISSIPPI','MS','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','MISSOURI','MO','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','MONTANA','MT','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','NEBRASKA','NE','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','NEVADA','NV','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','NEW HAMPSHIRE','NH','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','NEW JERSEY','NJ','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','NEW MEXICO','NM','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','NEW YORK','NY','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','NORTH CAROLINA','NC','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','NORTH DAKOTA','ND','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','OHIO','OH','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','OKLAHOMA','OK','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','OREGON','OR','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','PENNSYLVANIA','PA','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','RHODE ISLAND','RI','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','SOUTH CAROLINA','SC','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','SOUTH DAKOTA','SD','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','TENNESSEE','TN','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','TEXAS','TX','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','UTAH','UT','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','VERMONT','VT','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','VIRGINIA','VA','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','WASHINGTON','WA','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','WEST VIRGINIA','WV','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','WISCONSIN','WI','zmy');
INSERT INTO tms_logistics_country_province_mapping (logistics_company, country_code, province_name, province_code, create_by) values('UPS','US','WYOMING','WY','zmy');

