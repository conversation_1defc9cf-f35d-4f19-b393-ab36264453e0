CREATE TABLE IF NOT EXISTS tms_alert_task (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `logistics_no` VARCHAR(128) COMMENT '物流运单号',
  `tid` VARCHAR(100) COMMENT 'ERP订单号',
  `logistics_company` VARCHAR (50) COMMENT '物流公司',
  `logistics_type` VARCHAR(50) COMMENT '物流类型',
  `dept_name` VARCHAR (50) COMMENT '部门',
  `store_id` VARCHAR (50) COMMENT '店铺ID',
  `logistics_channel_code` VARCHAR (50) COMMENT '物流渠道编码',
  `package_status` VARCHAR (50) COMMENT '包裹物流状态',
  `ship_date`TIMESTAMP NULL DEFAULT NULL COMMENT '发货时间',
  `route_last_update_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '物流流转最后更新时间',
  `trigger_condition` VARCHAR (50) COMMENT '触发条件',
  `task_status` TINYINT(4) COMMENT '任务状态: 0---待处理，1---已处理',
  `process_status` VARCHAR(128) COMMENT '处理状态:漏发，已揽收，忽略/配件中，已送达，物流丢件，仓库丢件,已换单号，客户取消订单, 投递失败,协商索赔，调整发货量，停止合作，调整超期参数，其他',
  `is_claim` TINYINT(4) COMMENT '任务状态: 0---不索赔，1---不索赔',
  `is_return` TINYINT(4) COMMENT '是否退件: 0---不退件，1---退件',
  `remarks` VARCHAR(256) COMMENT '备注',
  `create_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` VARCHAR (45) COMMENT '创建者',
  `update_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` VARCHAR (45) COMMENT '更新者'
) ENGINE = INNODB COMMENT '物流异常任务表';

ALTER TABLE tms_alert_task add index ix_logistics_no(logistics_no);
ALTER TABLE tms_alert_task add index ix_tid(tid);
ALTER TABLE tms_alert_task add index ix_logistics_company(logistics_company);
ALTER TABLE tms_alert_task add index ix_ship_date(ship_date);
ALTER TABLE tms_alert_task add index ix_process_status(process_status);
ALTER TABLE tms_alert_task add index ix_task_status(task_status);
ALTER TABLE tms_alert_task add index ix_trigger_condition(trigger_condition);
ALTER TABLE tms_alert_task add index ix_store_id(store_id);
ALTER TABLE tms_alert_task add index ix_is_claim(is_claim);



CREATE TABLE IF NOT EXISTS tms_expired_task (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `logistics_company` VARCHAR (50) COMMENT '物流公司',
  `trigger_condition` VARCHAR (50) COMMENT '触发条件',
  `task_status` TINYINT(4) COMMENT '任务状态: 0---待处理，1---已处理',
  `process_status` VARCHAR(128) COMMENT '处理状态:协商索赔，调整发货量，停止合作，调整超期参数，其他',
  `is_claim` TINYINT(4) COMMENT '任务状态: 0---不索赔，1---索赔',
  `threshold` varchar(64) COMMENT '超期率阀值',
  `statistic_duration` INT(11) COMMENT '统计时长:天',
  `statistic_start_date` TIMESTAMP NULL DEFAULT NULL COMMENT '统计开始时间',
  `statistic_end_date` TIMESTAMP NULL DEFAULT NULL COMMENT '统计结束时间',
  `package_count` INT(11) COMMENT '包裹数',
  `expired_package_count` INT(11) COMMENT '超期包裹数',
  `expired_rate` VARCHAR(32) COMMENT '超期率',
  `remarks` VARCHAR(256) COMMENT '备注',
  `create_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` VARCHAR (45) COMMENT '创建者',
  `update_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` VARCHAR (45) COMMENT '更新者'
) ENGINE = INNODB COMMENT '物流超期任务表' ;

ALTER TABLE tms_expired_task add index ix_process_status(process_status);
ALTER TABLE tms_expired_task add index ix_task_status(task_status);
ALTER TABLE tms_expired_task add index ix_logistics_company(logistics_company);
ALTER TABLE tms_expired_task add index ix_trigger_condition(trigger_condition);
ALTER TABLE tms_expired_task add index ix_is_claim(is_claim);



CREATE TABLE IF NOT EXISTS tms_expired_task_item (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `expired_task_id` INT COMMENT '外键id',
  `logistics_no` VARCHAR(128) COMMENT '物流运单号',
  `tid` VARCHAR(100) COMMENT 'ERP订单号',
  `logistics_company` VARCHAR (50) COMMENT '物流公司',
  `logistics_type` VARCHAR(50) COMMENT '物流类型',
  `dept_name` VARCHAR (50) COMMENT '部门',
  `store_id` VARCHAR (50) COMMENT '店铺ID',
  `logistics_channel_code` VARCHAR (50) COMMENT '物流渠道编码',
  `package_status` VARCHAR (50) COMMENT '包裹物流状态',
  `ship_date`TIMESTAMP NULL DEFAULT NULL COMMENT '发货时间',
  `promised_arrival_date`  TIMESTAMP NULL DEFAULT NULL COMMENT '承诺到达时间',
  `arrival_date`  TIMESTAMP NULL DEFAULT NULL COMMENT '到达时间',
  `route_last_update_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '物流流转最后更新时间',
  `create_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` VARCHAR (45) COMMENT '创建者',
  `update_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` VARCHAR (45) COMMENT '更新者'
) ENGINE = INNODB COMMENT '物流超期任务明细表' ;

ALTER TABLE tms_expired_task_item add index ix_logistics_no(logistics_no);
ALTER TABLE tms_expired_task_item add index ix_tid(tid);
ALTER TABLE tms_expired_task_item add index ix_expired_task_id(expired_task_id);


insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'FedEx',NULL,'2','异常');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'FedEx',NULL,'4','超期');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'FedEx',NULL,'7','疑似丢件');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'UPS',NULL,'2','异常');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'UPS',NULL,'4','超期');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'UPS',NULL,'7','疑似丢件');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'TNT',NULL,'2','异常');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'TNT',NULL,'4','超期');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'TNT',NULL,'7','疑似丢件');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'DHL',NULL,'2','异常');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'DHL',NULL,'4','超期');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'DHL',NULL,'7','疑似丢件');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'顺丰国际',NULL,'7','异常');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'顺丰国际',NULL,'13','超期');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'顺丰国际',NULL,'20','疑似丢件');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'E邮宝',NULL,'7','异常');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'E邮宝',NULL,'12','超期');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'E邮宝',NULL,'20','疑似丢件');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'EDL','US','9','超期');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'EDL','US','6','异常');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'EDL','US','16','疑似丢件');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'4PX',NULL,'5','异常');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'4PX',NULL,'8','超期');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'4PX',NULL,'18','疑似丢件');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'4PX','HR','14','超期');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'4PX','CZ','14','超期');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'4PX','SI','14','超期');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'4PX','HR','21','疑似丢件');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'4PX','CZ','21','疑似丢件');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'4PX','SI','21','疑似丢件');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'CNE',NULL,'5','异常');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'CNE',NULL,'9','超期');
insert into `tms_timeliness_config` (`logistics_channel_code`, `logistics_company`, `country_code`, `days`, `type`) values(NULL,'CNE',NULL,'20','疑似丢件');