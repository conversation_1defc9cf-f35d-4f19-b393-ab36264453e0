<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
    <property name="app.name" value="api-tms"/>
    <property name="tomcat.instance.name" value="api-tms"/>

    <appender name="info" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <root level="debug">
        <appender-ref ref="info"/>
    </root>
</configuration>

