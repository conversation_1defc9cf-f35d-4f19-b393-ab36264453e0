package com.nsy.api.tms.service.external.refactor;

import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.dao.entity.TmsRouteRecordEntity;
import com.nsy.api.tms.enumeration.PackageStatusEnum;
import com.nsy.api.tms.logistics.kts.request.KtsRoute;
import com.nsy.api.tms.logistics.kts.request.KtsRouteRequest;
import com.nsy.api.tms.logistics.kts.response.KtsRouteDetail;
import com.nsy.api.tms.logistics.kts.response.KtsRouteResponse;
import com.nsy.api.tms.logistics.kts.response.KtsRoutes;
import com.nsy.api.tms.service.TmsRouteService;
import com.nsy.api.tms.utils.JaxbObjectAndXmlUtil;
import com.nsy.api.tms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLEncoder;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class KtsTrackNewService extends KtsNewService {
    private static final Logger LOGGER = LoggerFactory.getLogger(KtsTrackNewService.class);

    @Value("${kts.track.url}")
    private String trackUrl;
    
    @Autowired
    TmsRouteService routeService;

    public void doTrack(TmsPackageEntity packageEntity) {
        // 记录日志
        TmsRequestLogEntity logEntity = requestLogService.recordBaseInfoLog(packageEntity);
        //更新tms_package 表路由查询次数
        packageEntity.setRouteSearchTimes(packageEntity.getRouteSearchTimes() + 1);
        packageService.save(packageEntity);
        Map<String, String> configMap = buildTrackConfig(packageEntity);
        KtsRouteRequest routeRequest = null;
        try {
            routeRequest = buildRequest(packageEntity);
            executeRoute(routeRequest, packageEntity, configMap, logEntity);
        } catch (UnsupportedEncodingException | NoSuchAlgorithmException e) {
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, JsonMapper.toJson(routeRequest), e.getMessage(), packageEntity.getLogisticsNo());
            e.printStackTrace();
        }
    }

    private void executeRoute(KtsRouteRequest request, TmsPackageEntity packageEntity, Map<String, String> configMap, TmsRequestLogEntity logEntity) throws UnsupportedEncodingException, NoSuchAlgorithmException {
        String logisticsInterface = convertFromXml(JaxbObjectAndXmlUtil.object2Xml(request));
        String dataDigest = buildSignature(logisticsInterface + configMap.get(CONFIG_CHECK_CODE_TRACK));
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(trackUrl)
                .queryParam("client_code", configMap.get(CONFIG_CLIENT_CODE_TRACK))
                .queryParam("msg_type", "API_ROUTE_QUERY")
                .queryParam("logistics_interface", URLEncoder.encode(logisticsInterface, "utf-8"))
                .queryParam("data_digest", URLEncoder.encode(dataDigest, "utf-8"));
        URI url = builder.build(true).toUri();
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        LOGGER.debug("url:{}, responseXml;{}", url, responseEntity.getBody());
        KtsRouteResponse ktsRouteResponse = JaxbObjectAndXmlUtil.xml2Object(responseEntity.getBody(), KtsRouteResponse.class);
        if (ktsRouteResponse.getRouteConent() != null) {
            KtsRoutes ktsRoutes = ktsRouteResponse.getRouteConent().getKtsRoutesList().get(0);
            if (!ktsRoutes.isSuccess()) {
                processFailRouteReply(url.toString(), responseEntity.getBody(), packageEntity.getLogisticsNo(), logEntity);
                return;
            }
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, url.toString(), responseEntity.getBody(), packageEntity.getLogisticsNo());
            processSuccessRouteReply(ktsRoutes, packageEntity);
        } else {
            if (!ktsRouteResponse.isSuccess()) {
                processFailRouteReply(url.toString(), responseEntity.getBody(), packageEntity.getLogisticsNo(), logEntity);
            }
        }
    }

    private void processSuccessRouteReply(KtsRoutes ktsRoutes, TmsPackageEntity packageEntity) {
        List<KtsRouteDetail> ktsRouteDetailList = ktsRoutes.getKtsRouteDetailList();
        KtsRouteDetail lastRoute = ktsRouteDetailList.get(0);
        List<TmsRouteRecordEntity> routeRecordEntityList = routeService.findByLogisticsNoOrderByCreateDateDesc(packageEntity.getLogisticsNo());
        if (null != routeRecordEntityList && !routeRecordEntityList.isEmpty()) {
            TmsRouteRecordEntity lastRouteRecord = routeRecordEntityList.get(0);
            if (lastRouteRecord.getAcceptTime().compareTo(DateUtils.parse(lastRoute.getOprDate(), DateUtils.DATE_FORMAT_DATE4)) == 0 && lastRouteRecord.getAcceptAddress().equalsIgnoreCase(lastRoute.
                    getCity()) && lastRouteRecord.getRemark().equalsIgnoreCase(lastRoute.getDesc())) {
            }
        }
        if (ktsRouteDetailList.size() == 1) {
            packageEntity.setStatus(PackageStatusEnum.SHIPPED.getDesc());
        } else {
            if ("K12".equals(lastRoute.getBarCode())) {
                packageEntity.setStatus(PackageStatusEnum.SHIPPED.getDesc());
            } else if ("K50".equals(lastRoute.getBarCode())) {
                packageEntity.setStatus(PackageStatusEnum.TAKEN.getDesc());
            } else if ("K09".equals(lastRoute.getBarCode())) {
                packageEntity.setArrivalTime(DateUtils.parse(lastRoute.getOprDate(), DateUtils.DATE_FORMAT_DATE4));
                packageEntity.setStatus(PackageStatusEnum.DELIVERED.getDesc());
            } else {
                packageEntity.setStatus(PackageStatusEnum.TRANSIT.getDesc());
            }
            for (int i = ktsRouteDetailList.size() - 1; i >= 0; i--) {
                if ("K50".equals(ktsRouteDetailList.get(i).getBarCode())) {
                    packageEntity.setReceiveTime(DateUtils.parse(ktsRouteDetailList.get(i).getOprDate(), DateUtils.DATE_FORMAT_DATE4));
                    break;
                }
            }
        }
        packageEntity.setRouteLastUpdateTime(DateUtils.parse(lastRoute.getOprDate(), DateUtils.DATE_FORMAT_DATE4));
        persistTmsRouteRecord(packageEntity.getLogisticsNo(), lastRoute, packageEntity.getStatus());
        packageService.save(packageEntity);
    }

    private void processFailRouteReply(String url, String responseStr, String logisticsNo, TmsRequestLogEntity logEntity) {
        requestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, url, responseStr, logisticsNo);
    }

    private void persistTmsRouteRecord(String logisticsNo, KtsRouteDetail route, String status) {
        TmsRouteRecordEntity routeRecordEntity = new TmsRouteRecordEntity();
        routeRecordEntity.setStatus(status);
        routeRecordEntity.setAcceptTime(DateUtils.parse(route.getOprDate(), DateUtils.DATE_FORMAT_DATE4));
        routeRecordEntity.setAcceptAddress(route.getCity());
        routeRecordEntity.setLogisticsNo(logisticsNo);
        routeRecordEntity.setRemark(route.getDesc());
        routeService.save(routeRecordEntity);
    }

    private Map<String, String> buildTrackConfig(TmsPackageEntity packageEntity) {
//        String keyGroup = configEntity.getKeyGroup();
//        if ("stage".equalsIgnoreCase(env.getActiveProfiles()[0])) {
//            keyGroup = keyGroup + "_Track";
//        }
        return logisticsAccountService.buildAccountConfig(packageEntity.getLogisticsAccountId());
    }

    private KtsRouteRequest buildRequest(TmsPackageEntity packageEntity) {
        KtsRouteRequest routeRequest = new KtsRouteRequest();
        routeRequest.setLanguage("en");
        routeRequest.setPlatFormCode("amazon");
        List<KtsRoute> routeList = new ArrayList<>();
        KtsRoute route = new KtsRoute();
        route.setOrderId(packageEntity.getTmsTid());
        route.setSfWaybillNo(packageEntity.getLogisticsNo());
        routeList.add(route);
        routeRequest.setKtsRouteList(routeList);
        return routeRequest;
    }


    private static String convertFromXml(String str) {
        boolean flag = true;
        boolean quotesFlag = true;
        StringBuffer ans = new StringBuffer();
        String tmp = "";
        for (int i = 0; i < str.length(); i++) {
            if ('"' == str.charAt(i)) {
                ans.append(str.charAt(i));
                quotesFlag = !quotesFlag;
            } else if ('<' == str.charAt(i)) {
                tmp = tmp.trim();
                ans.append(tmp);
                flag = true;
                ans.append(str.charAt(i));
            } else if ('>' == str.charAt(i)) {
                if (quotesFlag) {
                    flag = false;
                    ans.append(str.charAt(i));
                    tmp = "";
                } else {
                    ans.append("&gt;");
                }
            } else if (flag) {
                ans.append(str.charAt(i));
            } else {
                tmp += str.charAt(i);
            }
        }
        return ans.toString();
    }
}
