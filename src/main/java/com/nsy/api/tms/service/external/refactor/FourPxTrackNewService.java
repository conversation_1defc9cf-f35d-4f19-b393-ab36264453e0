package com.nsy.api.tms.service.external.refactor;

import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.dao.entity.TmsRouteRecordEntity;
import com.nsy.api.tms.enumeration.PackageStatusEnum;
import com.nsy.api.tms.logistics.fourpx.FourPxLogisticsTrackResponse;
import com.nsy.api.tms.service.TmsRouteService;
import com.nsy.api.tms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestClientException;
import java.net.URI;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class FourPxTrackNewService extends FourPxNewService {
    private static final Logger LOGGER = LoggerFactory.getLogger(FourPxTrackNewService.class);

    @Autowired
    TmsRouteService routeService;

    @Value("${4px.track.domain}")
    String fourTrackDomain;

    private String trackApiName;
    private String trackApiVersion;
    private String trackStatusException;
    private String trackStatusDeliverd;
    private String trackStatusTaken;

    @Override
    public void afterPropertiesSet() throws Exception {
        super.afterPropertiesSet();
        this.trackApiName = "tr.order.tracking.get";
        this.trackApiVersion = "1.0";
        this.trackStatusException = "FPX_C_DT,FPX_C_DTTM,FPX_C_DTCQ,FPX_C_HF,FPX_C_HFNP,FPX_C_BTC,FPX_M_PRTS,FPX_M_SE,FPX_I_HC,FPX_I_HCIT,FPX_I_HCTI,FPX_I_HCPW,FPX_I_HCPA,FPX_I_HCWI,FPX_I_HCPI,FPX_I_HCFE,FPX_I_HCCB,FPX_I_HCDN,FPX_I_HCLE,FPX_I_HCVC,FPX_I_HCVV,FPX_I_HCPR,FPX_I_HCCI,FPX_I_HCRC,FPX_I_HCCD,FPX_I_HCAD,FPX_I_HCUG,FPX_I_HCAI,FPX_I_HCCR,FPX_I_HCAP,FPX_I_HCWC,FPX_I_HCRR,FPX_I_HCNR,FPX_I_HCPC,FPX_I_HCCC,FPX_I_HCCF,FPX_D_SHRP,FPX_D_CCNS,FPX_D_SH,FPX_D_SHNP,FPX_D_SHRC,FPX_D_SHRD,FPX_D_SHND,FPX_D_SHWC,FPX_D_SR,FPX_D_RR,FPX_D_DDRC,FPX_D_SHWD,FPX_D_SHCC,FPX_D_SHPD,FPX_D_SHAP,FPX_D_SHOA,FPX_S_CC,FPX_Y_DS,FPX_Y_CCMC,FPX_Y_CCSC,FPX_O_RT,FPX_O_SD,FPX_O_PRUC,FPX_O_PRRR,FPX_O_PRIA,FPX_O_PRRF,FPX_O_SCBS,FPX_O_RTOC,FPX_O_RTHM,FPX_D_FD,FPX_Y_COBH,FPX_D_VN,FPX_I_DG,FPX_Y_ADPR,FPX_O_RFLM,FPX_D_FDPL,FPX_D_FDIA,FPX_D_FDLO,FPX_D_FDVO,FPX_D_FDNC,FPX_D_DFCR,FPX_D_FDSD,FPX_O_SPHS,FPX_O_SHFC";
        this.trackStatusDeliverd = "FPX_D_CCPH,FPX_S_OK,FPX_S_OKGP,FPX_S_OKVP,FPX_S_OKPO,FPX_S_OKCC,FPX_S_OKSC,FPX_S_OKRC";
        this.trackStatusTaken = "FPX_L_RPIF";
    }

    public void doTrack(TmsPackageEntity packageEntity) {
        // 记录日志
        TmsRequestLogEntity logEntity = requestLogService.recordBaseInfoLog(packageEntity);
        //更新tms_package 表路由查询次数
        packageEntity.setRouteSearchTimes(packageEntity.getRouteSearchTimes() + 1);
        packageService.save(packageEntity);
        Map<String, String> configMap = buildTrackConfig(packageEntity);
        HashMap<String, String> body = new HashMap<>();
        body.put("deliveryOrderNo", packageEntity.getLogisticsNo());
        try {
            //get route info
            URI baseUrl = getSignedUrl(trackApiName, trackApiVersion, JsonMapper.toJson(body), this.fourTrackDomain, configMap);
            String reply = restTemplate.postForObject(baseUrl, body, String.class);
            FourPxLogisticsTrackResponse pxResponse = JsonMapper.fromJson(reply, FourPxLogisticsTrackResponse.class);
            LOGGER.debug("路由信息:{}", JsonMapper.toJson(pxResponse));
            //process route info
            processSuccessTrackReply(packageEntity, body, pxResponse, logEntity);
        } catch (RestClientException e) {
            processFailTrackReply(body, "调用异常", packageEntity.getLogisticsNo(), logEntity);
            throw new RuntimeException(e);
        }
    }

    private Map<String, String> buildTrackConfig(TmsPackageEntity packageEntity) {
//        String keyGroup = channelConfigEntity.getKeyGroup();
//        if ("stage".equalsIgnoreCase(env.getActiveProfiles()[0])) {
//            keyGroup = keyGroup + "_Track";
//        }
        return logisticsAccountService.buildAccountConfig(packageEntity.getLogisticsAccountId());

    }

    private void processFailTrackReply(Map body, String message, String logisticsNo, TmsRequestLogEntity logEntity) {
        requestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, JSONUtils.toJSON(body), message, logisticsNo);
    }

    private void processSuccessTrackReply(TmsPackageEntity packageEntity, Map trackRequest, FourPxLogisticsTrackResponse trackResponse, TmsRequestLogEntity logEntity) {
        // 1. 更新日志
        requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, JSONUtils.toJSON(trackRequest), JSONUtils.toJSON(trackResponse), packageEntity.getLogisticsNo());
        if (!trackResponse.getResult().equals("1")) {
            LOGGER.info("暂无路由信息:{}", packageEntity.getLogisticsNo());
            return;
        }
        //2.判断路由表是否存在更新
        if (isTrackUpdated(packageEntity, trackResponse)) {
            //3.更新路由表
            persistTmsRouteRecord(packageEntity, trackResponse);
        }
        // 4.更新tms_package状态
        updatePackageStatus(packageEntity, trackResponse);
    }

    /**
     * 判断路由表是否存在更新
     *
     * @param packageEntity
     * @param trackResponse
     * @return true 存在更新
     */
    private boolean isTrackUpdated(TmsPackageEntity packageEntity, FourPxLogisticsTrackResponse trackResponse) {
        if (!trackResponse.getResult().equals("1")) {
            LOGGER.info("logisticsNo:{}, 暂无路由信息", packageEntity.getLogisticsNo());
            return false;
        }
        List<FourPxLogisticsTrackResponse.Tracking> trackInfoList = trackResponse.getData().getTrackingList();
        if (CollectionUtils.isEmpty(trackInfoList)) {
            return false;
        }
        int originTrackSize = trackInfoList.size();
        List<TmsRouteRecordEntity> routeRecordEntityList = routeService.findByLogisticsNoOrderByCreateDateDesc(packageEntity.getLogisticsNo());
        if (null != routeRecordEntityList && !routeRecordEntityList.isEmpty()) {
            routeRecordEntityList.forEach((routeRecord) -> trackInfoList.removeIf(trackInfo -> compareTrackInfo2RouteRecord(trackInfo, routeRecord) == 0));
        }
        LOGGER.info("查询到的路由数目" + originTrackSize);
        LOGGER.info("需要更新的路由数目" + trackInfoList.size());
        return !trackInfoList.isEmpty();
    }

    private int compareTrackInfo2RouteRecord(FourPxLogisticsTrackResponse.Tracking trackInfo, TmsRouteRecordEntity routeRecord) {
        int count;
        if (trackInfo.getBusinessLinkCode().compareTo(routeRecord.getStatus()) == 0 && trackInfo.getOccurDatetime().compareTo(routeRecord.getAcceptTime()) == 0 && trackInfo.getOccurLocation().equals(routeRecord.getAcceptAddress()) && trackInfo.getTrackingContent().equals(routeRecord.getRemark())) {
            count = 0;
        } else {
            count = -1;
        }
        return count;
    }

    private void persistTmsRouteRecord(TmsPackageEntity packageEntity, FourPxLogisticsTrackResponse trackResponse) {
        List<FourPxLogisticsTrackResponse.Tracking> trackInfoList = trackResponse.getData().getTrackingList();
        ArrayList<TmsRouteRecordEntity> tmsRouteRecordList = new ArrayList<>();
        trackInfoList.forEach((trackInfo) -> {
            TmsRouteRecordEntity routeRecordEntity = new TmsRouteRecordEntity();
            routeRecordEntity.setLogisticsNo(packageEntity.getLogisticsNo());
            routeRecordEntity.setAcceptAddress(trackInfo.getOccurLocation());
            routeRecordEntity.setAcceptTime(trackInfo.getOccurDatetime());
            routeRecordEntity.setStatus(trackInfo.getBusinessLinkCode());
            routeRecordEntity.setRemark(trackInfo.getTrackingContent());
            tmsRouteRecordList.add(routeRecordEntity);
        });
        LOGGER.info("插入数据库的路由: {}", JsonMapper.toJson(tmsRouteRecordList));
        routeService.saveRouteRecordEntityList(tmsRouteRecordList);
    }

    private void updatePackageStatus(TmsPackageEntity packageEntity, FourPxLogisticsTrackResponse trackResponse) {
        List<FourPxLogisticsTrackResponse.Tracking> trackInfoList = trackResponse.getData().getTrackingList();
        if (trackInfoList.isEmpty()) {
            return;
        }
        FourPxLogisticsTrackResponse.Tracking trackInfo = trackInfoList.get(0);
        String newStatus = getStatusByTrackDescription(trackInfo.getBusinessLinkCode());
        Date receiveTime = packageEntity.getReceiveTime();
        if ("异常".equalsIgnoreCase(newStatus)) {
            packageEntity.setStatus(PackageStatusEnum.TRANSIT.getDesc());
//            generateAlertTask(packageEntity);
        } else {
            packageEntity.setStatus(newStatus);
        }
        //update arrival time
        if (newStatus.equals(PackageStatusEnum.DELIVERED.getDesc())) {
            packageEntity.setArrivalTime(trackInfo.getOccurDatetime());
        }
        //update receive time
        if (!newStatus.equals(PackageStatusEnum.SHIPPED.getDesc()) && receiveTime == null) {
            for (FourPxLogisticsTrackResponse.Tracking track : trackInfoList) {
                if (getStatusByTrackDescription(track.getBusinessLinkCode()).equals(PackageStatusEnum.TAKEN.getDesc())) {
                    packageEntity.setReceiveTime(track.getOccurDatetime());
                    break;
                }
            }
        }
        packageEntity.setRouteLastUpdateTime(trackInfo.getOccurDatetime());
        packageService.save(packageEntity);
    }

    private String getStatusByTrackDescription(String businessLinkCode) {
        LOGGER.debug("接口路由状态: {}", businessLinkCode);
        String status;
        if (trackStatusException.contains(businessLinkCode)) {
            status = "异常";
        } else if (trackStatusDeliverd.contains(businessLinkCode)) {
            status = PackageStatusEnum.DELIVERED.getDesc();
        } else if (trackStatusTaken.contains(businessLinkCode)) {
            status = PackageStatusEnum.TAKEN.getDesc();
        } else {
            status = PackageStatusEnum.TRANSIT.getDesc();
        }
        return status;
    }
}
