package com.nsy.api.tms.service.impl;

import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.dao.entity.TmsAlertTaskEntity;
import com.nsy.api.tms.dao.entity.TmsAlertTaskRemarkEntity;
import com.nsy.api.tms.dao.entity.TmsClaimEntity;
import com.nsy.api.tms.dao.entity.TmsCountryEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsTagEntity;
import com.nsy.api.tms.domain.LogisticsMethodCount;
import com.nsy.api.tms.domain.TmsAlertTaskReturnInfo;
import com.nsy.api.tms.enumeration.IsReturnStatusEnum;
import com.nsy.api.tms.enumeration.PackageStatusEnum;
import com.nsy.api.tms.enumeration.PackageTagEnum;
import com.nsy.api.tms.enumeration.ProcessStatusEnum;
import com.nsy.api.tms.enumeration.TmsClaimStatusEnum;
import com.nsy.api.tms.enumeration.TmsTaskTriggerConditionEnum;
import com.nsy.api.tms.repository.PackageRepository;
import com.nsy.api.tms.repository.TmsAlertTaskRemarkRepository;
import com.nsy.api.tms.repository.TmsAlertTaskRepository;
import com.nsy.api.tms.repository.TmsClaimRepository;
import com.nsy.api.tms.repository.TmsCountryRepository;
import com.nsy.api.tms.repository.TmsTagRepository;
import com.nsy.api.tms.request.TmsAlertHandleRequest;
import com.nsy.api.tms.request.TmsAlertTaskListRequest;
import com.nsy.api.tms.response.base.BaseListResponse;
import com.nsy.api.tms.service.PackageService;
import com.nsy.api.tms.service.TmsAlertTaskManagementService;
import com.nsy.api.tms.service.TmsOfficialWebsiteService;
import com.nsy.api.tms.service.privilege.AccessControlService;
import com.nsy.api.tms.service.privilege.PrivilegeAction;
import com.nsy.api.tms.service.privilege.Privileges;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service("tmsAlertTaskManagementService")
public class TmsAlertTaskManagementServiceImpl implements TmsAlertTaskManagementService {

    private final Logger logger = LoggerFactory.getLogger(TmsAlertTaskManagementServiceImpl.class);
    private static final Integer NOT_DELETE = 0;
    private static final Integer TASK_STATUS_DONE = 1;
    private static final Integer TASK_STATUS_ING = 2;
    private static final Integer ALTER_REMARK = -1;

    @Autowired
    private EntityManager entityManager;
    @Autowired
    protected TmsAlertTaskRepository tmsAlertTaskRepository;
    @Autowired
    private TmsClaimRepository claimRepository;
    @Autowired
    TmsOfficialWebsiteService officialWebsiteService;
    @Autowired
    private AccessControlService accessControlService;
    @Autowired
    private TmsTagRepository tmsTagRepository;
    @Autowired
    private TmsAlertTaskRemarkRepository tmsAlertTaskRemarkRepository;
    @Autowired
    private PackageService packageService;
    @Autowired
    PackageRepository packageRepository;
    @Inject
    TmsCountryRepository countryRepository;
    @Inject
    TmsAlertTaskRemarkRepository alertTaskRemarkRepository;

    @Override
    public BaseListResponse<TmsAlertTaskReturnInfo> findList(TmsAlertTaskListRequest request) {
        // 按任务生成时间 倒序
        Sort sort = Sort.by(new Sort.Order(Sort.Direction.DESC, "createDate"));
        Pageable pageable = PageRequest.of(request.getPageIndex() - 1, request.getPageSize(), sort);
        //数据权限验证
        Specification<TmsAlertTaskEntity> specification = this.makePrivileged(request);
        Page<TmsAlertTaskEntity> taskEntityPage = tmsAlertTaskRepository.findAll(specification, pageable);
        List<TmsAlertTaskReturnInfo> taskEntityList = this.prepareResultList(TmsAlertTaskReturnInfo.class, taskEntityPage.getContent());
        return new BaseListResponse<>(taskEntityPage.getTotalElements(), taskEntityList);
    }

    protected <T> List<T> prepareResultList(Class<T> tClass, List<TmsAlertTaskEntity> list) {
        if (Objects.isNull(list) || list.isEmpty()) {
            return new ArrayList<>();
        }
        Stream<TmsAlertTaskEntity> stream = list.stream();
        if (list.size() > 100) {
            stream = stream.parallel();
        }
        return stream.map(entity -> {
            try {
                T t = tClass.newInstance();
                BeanUtilsEx.copyProperties(entity, t);
                //设置目的地国
                TmsPackageEntity packageEntity = packageRepository.findByLogisticsNo(entity.getLogisticsNo());
                Method setCountryName = tClass.getMethod("setCountryName", String.class);
                List<TmsCountryEntity> countryEntities = countryRepository.findByCountryCode(packageEntity.getCountryCode());
                setCountryName.invoke(t, CollectionUtils.isEmpty(countryEntities) ? "" : countryEntities.get(0).getChineseName());
                //设置官网追踪链接
                Method setOfficialWebsiteMethod = tClass.getMethod("setOfficialWebsite", String.class);
                setOfficialWebsiteMethod.invoke(t, officialWebsiteService.getOfficialWebsite(entity.getLogisticsMethod(), entity.getLogisticsNo()));
                // 设置备注信息 从备注表取最新的一条
                Method setRemarksMethod = tClass.getMethod("setRemarks", String.class);
                setRemarksMethod.invoke(t, getLatestRemark(entity.getId()));
                return t;
            } catch (IllegalAccessException | InvocationTargetException | InstantiationException | NoSuchMethodException e) {
                logger.error("{}类型不匹配", tClass.getName());
            }
            throw new RuntimeException("类型不匹配");
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void handle(TmsAlertHandleRequest request) {
        //1.修改任务状态
        this.alterTask(request);
        if (Objects.nonNull(request.getEstimatedFinishTime())) {
            logger.info("修改任务状态为处理中---结束");
            return;
        }
        //2.获取该任务详情
        TmsAlertTaskEntity alertTaskEntity = getAlertTaskEntity(request.getId());
        //3.如果勾选索赔 根据任务详情内容新增一条待索赔记录 原因 处理状态值
        if (Objects.nonNull(request.getIsClaim()) && request.getIsClaim() == 1) {
            TmsClaimEntity claimEntity = generateClaimItem(request, alertTaskEntity);
            claimRepository.save(claimEntity);
        }
        //4,不同任务处理状态 做不同的处理
        alertHandle(request, alertTaskEntity);
        //5,已处理的任务,将备注同步到备注表
        addDoneTaskRemark(request);
    }

    /**
     * 获取每个物流方式的未处理任务数
     *
     * @param alertTaskListRequest
     * @return
     */
    @Override
    public List<LogisticsMethodCount> getUnHandleTaskCount(TmsAlertTaskListRequest alertTaskListRequest) {
        // 数据权限
        Specification<TmsAlertTaskEntity> specification = this.makePrivileged(alertTaskListRequest);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<LogisticsMethodCount> query = criteriaBuilder.createQuery(LogisticsMethodCount.class);
        Root<TmsAlertTaskEntity> root = query.from(TmsAlertTaskEntity.class);
        // 查询条件
        query.where(specification.toPredicate(root, query, criteriaBuilder));
        // 分组 物流方式
        query.groupBy(root.get("logisticsMethod"));
        // select
        query.multiselect(root.get("logisticsMethod"), criteriaBuilder.count(root));
        TypedQuery<LogisticsMethodCount> managerQuery = entityManager.createQuery(query);
        // 结果处理
        //List<LogisticsMethodCount> companyCounts = managerQuery.getResultList();
        // 如果要显示全部物流方式 加上他
        /*List<String> collect = companyCounts.stream().map(LogisticsMethodCount::getLogisticsMethod).collect(Collectors.toList());
        Arrays.asList(LogisticsMethodEnum.getLogisticsMethodArray()).forEach(s -> {
            if(!collect.contains(s)){
                companyCounts.add(new LogisticsMethodCount(s,0L));
            }
        });*/
        return managerQuery.getResultList();
    }

    @Override
    public void oneKeyStartTasks(TmsAlertTaskListRequest alertTaskListRequest) {
        // 数据权限
        Specification<TmsAlertTaskEntity> specification = this.makePrivileged(alertTaskListRequest);
        List<TmsAlertTaskEntity> all = tmsAlertTaskRepository.findAll(specification).stream().parallel()
                .peek(entity -> {
                    entity.setTaskStatus(TASK_STATUS_ING);
                    entity.setTaskBeginTime(new Date());
                }).collect(Collectors.toList());
        tmsAlertTaskRepository.saveAll(all);
        logger.info("一键开启处理{}条任务", all.size());
    }
    @Override
    public void batchProcessTasks(TmsAlertTaskListRequest request) {
        List<TmsAlertTaskEntity> tmsAlertTaskList;
        if (request.getLogisticsNos().isEmpty()) {
            Specification<TmsAlertTaskEntity> specification = makeBatchProcessPrivileged(request);
            tmsAlertTaskList = tmsAlertTaskRepository.findAll(specification, PageRequest.of(0, 2000)).stream().map(entity -> {
                entity.setDeleteFlag(TmsPackageEntity.DELETED);
                entity.setUpdateBy(accessControlService.getRealName());
                return entity;
            }).collect(Collectors.toList());
        } else {
            tmsAlertTaskList = tmsAlertTaskRepository.findByLogisticsNoInAndDeleteFlagAndShipDateLessThanEqual(request.getLogisticsNos(), NOT_DELETE, DateUtils.addMonths(DateTime.now().toDate(), -3))
                    .stream().map(entity -> {
                        entity.setDeleteFlag(TmsPackageEntity.DELETED);
                        entity.setUpdateBy(accessControlService.getRealName());
                        return entity;
                    }).collect(Collectors.toList());
        }
        tmsAlertTaskRepository.saveAll(tmsAlertTaskList);
        logger.info("批量处理了{}条任务", tmsAlertTaskList.size());
    }
    @Override
    public List<TmsAlertTaskRemarkEntity> getTmsAlertTaskRemarks(Integer taskId) {
        return tmsAlertTaskRemarkRepository.findByTaskIdOrderByCreateDateDesc(taskId);
    }

    @Override
    public void addRemark(TmsAlertTaskRemarkEntity tmsAlertTaskRemarkEntity) {
        tmsAlertTaskRemarkEntity.setCreateBy(accessControlService.getRealName());
        tmsAlertTaskRemarkRepository.save(tmsAlertTaskRemarkEntity);
    }

    /**
     * 根据任务id 获取最新的备注
     * @param id
     * @return
     */
    private String getLatestRemark(Integer id) {
        List<TmsAlertTaskRemarkEntity> list = tmsAlertTaskRemarkRepository.findByTaskIdOrderByCreateDateDesc(id);
        if (list != null && !list.isEmpty()) {
            return list.get(0).getRemark();
        }
        return null;
    }

    /**
     * 添加备注到备注表
     * @param request
     */
    private void addDoneTaskRemark(TmsAlertHandleRequest request) {
        TmsAlertTaskRemarkEntity tmsAlertTaskRemarkEntity = new TmsAlertTaskRemarkEntity();
        tmsAlertTaskRemarkEntity.setTaskId(request.getId());
        tmsAlertTaskRemarkEntity.setRemark(request.getRemark());
        this.addRemark(tmsAlertTaskRemarkEntity);
    }

    /**
     * 修改任务状态
     * @param request
     */
    private void alterTask(TmsAlertHandleRequest request) {
        Optional<TmsAlertTaskEntity> optional = tmsAlertTaskRepository.findById(request.getId());
        if (optional.isPresent()) {
            TmsAlertTaskEntity alertTaskEntity = optional.get();
            alertTaskEntity.setUpdateBy(accessControlService.getRealName()); //修改人
            // 任务状态
            alertTaskEntity.setTaskStatus(TASK_STATUS_DONE);
            // 任务开始 与实际结束时间
            Date date = new Date();
            if (Objects.isNull(alertTaskEntity.getTaskBeginTime())) {
                alertTaskEntity.setTaskBeginTime(date);
            }
            alertTaskEntity.setActualCompletionTime(date);
            // 是否索赔
            Optional.ofNullable(request.getIsClaim()).ifPresent(alertTaskEntity::setIsClaim);
            //处理状态
            Optional.ofNullable(request.getStatus()).ifPresent(alertTaskEntity::setProcessStatus);
            //备注
            Optional.ofNullable(request.getRemark()).ifPresent(alertTaskEntity::setRemarks);
            //是否退件
            Optional.ofNullable(request.getIsReturn()).ifPresent(alertTaskEntity::setIsReturn);
            // 客户沟通结果
            Optional.ofNullable(request.getCommunicationResult()).ifPresent(alertTaskEntity::setCustomerCommunicationResults);
            // 预计完成时间
            Optional.ofNullable(request.getEstimatedFinishTime()).ifPresent(time -> {
                // 说明为处理中
                alertTaskEntity.setTaskStatus(TASK_STATUS_ING); // 任务状态变成处理中
                alertTaskEntity.setActualCompletionTime(null); // 实际处理时间置空
                alertTaskEntity.setEstimatedFinishTime(time); // 预计完成时间
            });
            tmsAlertTaskRepository.save(alertTaskEntity);
        }
    }
    /**
     * 具体的处理
     */
    private void alertHandle(TmsAlertHandleRequest request, TmsAlertTaskEntity alertTaskEntity) {
        ProcessStatusEnum statusEnum = ProcessStatusEnum.resolve(request.getStatus());
        if (!Objects.nonNull(statusEnum)) {
            throw new RuntimeException("处理状态值：" + request.getStatus() + "与处理状态枚举不匹配");
        }
        switch (statusEnum) {
            // 忽略/配送中/配送中--运输
            case IGNORE_OR_IN_TRANSIT:
            case IN_TRANSIT:
                alterPackageStatus(alertTaskEntity.getLogisticsNo(), null, PackageStatusEnum.TRANSIT.getDesc());
                break;
            // 投递失败 -- 投递失败
            case FAILED_DELIVERED:
                // alterPackageStatus(alertTaskEntity.getLogisticsNo(), null, PackageStatusEnum.DELIVERED_FAILED.getDesc());
                handleSendFailedAndDeliver(request.getNewLogisticsNo(), alertTaskEntity);
                break;
            // 漏发
            case MISSING_POST:
                // 新增包裹漏发标签
                TmsTagEntity tmsTagEntity = generateTagItem(alertTaskEntity);
                tmsTagRepository.save(tmsTagEntity);
                break;
            // 已送达 --送达
            case DELIVERED:
                alterPackageStatus(alertTaskEntity.getLogisticsNo(), request.getDeliveryDate(), PackageStatusEnum.DELIVERED.getDesc());
                break;
            //  已揽收 --已收
            case HAS_PICKED:
                alterPackageStatus(alertTaskEntity.getLogisticsNo(), null, PackageStatusEnum.TAKEN.getDesc());
                break;
            // 物流丢件，仓库丢件
            case LOGISTICS_MISSING:
            case WAREHOUSE_MISSING:
                alterPackageStatus(alertTaskEntity.getLogisticsNo(), null, PackageStatusEnum.LOST.getDesc());
                break;
            // 已换单号，客户取消订单
            case ORDER_CHANGED:
            case CUSTOMER_CANCEL_ORDER:
                alterPackageStatus(alertTaskEntity.getLogisticsNo(), null, PackageStatusEnum.CANCEL.getDesc());
                break;
            // 待客户自取件 -- 包裹状态：待自取
            case PENDING_PICKUP:
                alterPackageStatus(alertTaskEntity.getLogisticsNo(), null, PackageStatusEnum.PENDING_PICKUP.getDesc());
                break;
            default:
        }
    }

    private void handleSendFailedAndDeliver(String newLogisticsNo, TmsAlertTaskEntity alertTaskEntity) {
        // 处理状态为投递失败 ，是否退件选择改派时
        if (IsReturnStatusEnum.REASSIGN.getDesc().equals(alertTaskEntity.getIsReturn())) {
            Optional.ofNullable(newLogisticsNo).orElseThrow(() -> new RuntimeException("改派单号不可为空"));
            TmsPackageEntity newPackage = new TmsPackageEntity();
            this.reassign(alertTaskEntity.getLogisticsNo(), PackageStatusEnum.DELIVERED_FAILED.getDesc(), newPackage);
            String realName = accessControlService.getRealName();
            newPackage.setLogisticsNo(newLogisticsNo);
            newPackage.setUpdateBy(realName);
            newPackage.setCreateBy(realName);
            packageService.save(newPackage);
            return;
        }
        // 是否退件选择其他时
        alterPackageStatus(alertTaskEntity.getLogisticsNo(), null, PackageStatusEnum.DELIVERED_FAILED.getDesc());
    }

    /**
     * 改派
     */
    private void reassign(String logisticsNo, String status, TmsPackageEntity newPackage) {
        TmsPackageEntity packageEntity = Optional.ofNullable(packageService.getNoDeletedPackageByLogisticsNo(logisticsNo))
                .orElseThrow(() -> new RuntimeException("处理失败：Table->tms_package中找不到 运单号为：" + logisticsNo + "数据"));
        // 复制一些属性
        Optional.ofNullable(newPackage).ifPresent(tmsPackageEntity -> {
            BeanUtilsEx.copyProperties(packageEntity, newPackage, "id", "deleteFlag", "createDate", "updateDate");
        });
        packageEntity.setDeleteFlag(1);
        packageEntity.setStatus(status);
        packageEntity.setUpdateBy(accessControlService.getRealName()); //修改人
        packageService.save(packageEntity);
    }

    /**
     * 修改包裹状态
     *
     * @param logisticsNo
     * @param status
     */
    private void alterPackageStatus(String logisticsNo, Date deliveryDate, String status) {
        TmsPackageEntity packageEntity = packageService.getNoDeletedPackageByLogisticsNo(logisticsNo);
        if (Objects.nonNull(packageEntity)) {
            packageEntity.setStatus(status);
            packageEntity.setUpdateBy(accessControlService.getRealName()); //修改人
            Optional.ofNullable(deliveryDate)
                    .ifPresent(date -> packageEntity.setArrivalTime(deliveryDate));
            packageService.save(packageEntity);
            return;
        }
        throw new RuntimeException("处理失败：Table->tms_package中找不到 运单号为：" + logisticsNo + "数据");
    }

    /**
     * 获取任务信息
     * @param id
     * @return
     */
    private TmsAlertTaskEntity getAlertTaskEntity(Integer id) {
        Optional<TmsAlertTaskEntity> optionalTmsAlertTaskEntity = tmsAlertTaskRepository.findById(id);
        if (optionalTmsAlertTaskEntity.isPresent()) {
            return optionalTmsAlertTaskEntity.get();
        }
        throw new RuntimeException("找不到该任务，请确认");
    }

    /**
     * 生成漏发标签记录
     * @param alertTaskEntity
     * @return
     */
    private TmsTagEntity generateTagItem(TmsAlertTaskEntity alertTaskEntity) {
        TmsTagEntity tmsTagEntity = new TmsTagEntity();
        tmsTagEntity.setLogisticsChannelCode(alertTaskEntity.getLogisticsChannelCode());
        tmsTagEntity.setLogisticsMethod(alertTaskEntity.getLogisticsMethod());
        tmsTagEntity.setLogisticsNo(alertTaskEntity.getLogisticsNo());
        tmsTagEntity.setLogisticsType(alertTaskEntity.getLogisticsType());
        tmsTagEntity.setTid(alertTaskEntity.getTid()); // 订单号
        tmsTagEntity.setBusinessKey(alertTaskEntity.getBusinessKey()); // 业务单号
        tmsTagEntity.setTag(PackageTagEnum.MISSING_SHIPPED.getDesc()); // 漏发标签
        return tmsTagEntity;
    }

    /**
     * 生成待索赔项
     */
    private TmsClaimEntity generateClaimItem(TmsAlertHandleRequest request, TmsAlertTaskEntity alertTaskEntity) {
        TmsClaimEntity claimEntity = new TmsClaimEntity();
        claimEntity.setLogisticsNo(alertTaskEntity.getLogisticsNo());
        claimEntity.setTid(alertTaskEntity.getTid());
        claimEntity.setLogisticsMethod(alertTaskEntity.getLogisticsMethod());
        claimEntity.setLogisticsType(alertTaskEntity.getLogisticsType());
        claimEntity.setDeptName(alertTaskEntity.getDeptName());
        claimEntity.setShipDate(alertTaskEntity.getShipDate());
        //设置状态为待索赔
        claimEntity.setClaimStatus(TmsClaimStatusEnum.TOCLAIM.getDesc());
        // 原因为 处理状态值
        claimEntity.setClaimReason(request.getStatus());
        // 备注
        claimEntity.setRemarks(request.getRemark());
        // 创建人
        claimEntity.setCreateBy(accessControlService.getRealName());
        // 店铺id
        claimEntity.setStoreId(alertTaskEntity.getStoreId());
        return claimEntity;
    }

    private Specification<TmsAlertTaskEntity> buildSpecification(TmsAlertTaskListRequest request) {
        return (Specification<TmsAlertTaskEntity>) (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            // 过滤已删除的记录
            predicates.add(criteriaBuilder.equal(root.get("deleteFlag"), NOT_DELETE));
            /**
             * 根据不同任务类型 获取不同结果
             * 0-未收
             * 1-异常
             * 2-疑似丢件
             */
            switch (request.getTaskType()) {
                case 0:
                    predicates.add(criteriaBuilder.equal(root.get("triggerCondition"), TmsTaskTriggerConditionEnum.UNCOLLECT.getName()));
                    break;
                case 1:
                    predicates.add(criteriaBuilder.equal(root.get("triggerCondition"), TmsTaskTriggerConditionEnum.ABNORMAL.getName()));
                    break;
                case 2:
                    predicates.add(criteriaBuilder.equal(root.get("triggerCondition"), TmsTaskTriggerConditionEnum.MISSING_LIKELY.getName()));
                    break;
                default:
                    throw new RuntimeException("非法任务类型！");
            }
            buildAlertTaskSpecification(request, predicates, criteriaBuilder, root);
            //任务状态
            Optional.ofNullable(request.getTaskStatus()).ifPresent(integer ->
                    predicates.add(criteriaBuilder.equal(root.get("taskStatus"), integer)));
            this.buildSpecificationOfTime(request, predicates, criteriaBuilder, root);
            return criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()]));
        };
    }

    private void buildAlertTaskSpecification(TmsAlertTaskListRequest request, List<Predicate> predicates, CriteriaBuilder criteriaBuilder,
                                             Root<TmsAlertTaskEntity> root) {
        if (StringUtils.hasText(request.getLogisticsNo())) { //包裹编码
            predicates.add(criteriaBuilder.equal(root.get("logisticsNo"), request.getLogisticsNo()));
        }
        if (StringUtils.hasText(request.getPackageStatus())) { //包裹状态
            predicates.add(criteriaBuilder.equal(root.get("packageStatus"), request.getPackageStatus()));
        }
        if (StringUtils.hasText(request.getLogisticsMethod())) { //物流方式
            predicates.add(criteriaBuilder.equal(root.get("logisticsMethod"), request.getLogisticsMethod()));
        }
        if (StringUtils.hasText(request.getLogisticsType())) { //物流类型
            predicates.add(criteriaBuilder.equal(root.get("logisticsType"), request.getLogisticsType()));
        }
        if (StringUtils.hasText(request.getTid())) { //订单号
            List<String> tids = Arrays.asList(request.getTid().split(","));
            predicates.add(criteriaBuilder.in(root.get("tid")).value(tids));
        }
        if (null != request.getStoreId() && request.getStoreId() != 0) {
            predicates.add(criteriaBuilder.equal(root.get("storeId"), request.getStoreId()));
        }
        if (StringUtils.hasText(request.getCountryCode())) {
            predicates.add(criteriaBuilder.equal(root.get("countryCode"), request.getCountryCode()));
        }
        if (StringUtils.hasText(request.getStoreName())) {
            predicates.add(criteriaBuilder.like(root.get("storeName"), "%" + request.getStoreName() + "%"));
        }
        if (StringUtils.hasText(request.getRemarks())) {
            Specification<TmsAlertTaskRemarkEntity> specification = buildAlertRemarkSpecification(request.getRemarks());
            List<TmsAlertTaskRemarkEntity> alertTaskRemarkList = alertTaskRemarkRepository.findAll(specification);
            Map<Integer, List<TmsAlertTaskRemarkEntity>> collect = alertTaskRemarkList.stream()
                    .collect(Collectors.groupingBy(TmsAlertTaskRemarkEntity::getTaskId));
            Path<Object> path = root.get("id");
            CriteriaBuilder.In<Object> in = criteriaBuilder.in(path);
            collect.forEach((k, v) -> {
                in.value(k); // 存入值
            });
            if (!collect.isEmpty()) {
                predicates.add(criteriaBuilder.and(in));
            } else {
                in.value(ALTER_REMARK);
                predicates.add(criteriaBuilder.and(in));
            }
        }
    }

    private Specification<TmsAlertTaskEntity> buildBatchProcessTasksSpecification(TmsAlertTaskListRequest request) {
        return buildSpecification(request).and((Specification<TmsAlertTaskEntity>) (root, query, criteriaBuilder) ->
                criteriaBuilder.and(criteriaBuilder.lessThanOrEqualTo(root.get("shipDate"), DateUtils.addMonths(DateTime.now().toDate(), -3))));
    }
    private Specification<TmsAlertTaskRemarkEntity> buildAlertRemarkSpecification(String remark) {
        return (Specification<TmsAlertTaskRemarkEntity>) (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.hasText(remark)) {
                predicates.add(criteriaBuilder.like(root.get("remark"), "%" + remark + "%"));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()]));
        };
    }

    private void buildSpecificationOfTime(TmsAlertTaskListRequest request, List<Predicate> predicates, CriteriaBuilder criteriaBuilder,
                                          Root<TmsAlertTaskEntity> root) {
        // 任务生成 开始时间
        Optional.ofNullable(request.getStartDate()).ifPresent(beginDate ->
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createDate"), beginDate)));
        // 任务生成 结束时间
        Optional.ofNullable(request.getEndDate()).ifPresent(endDate ->
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("createDate"), endDate)));
        // 发货开始时间
        Optional.ofNullable(request.getShipStartDate()).ifPresent(beginDate ->
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("shipDate"), beginDate)));
        // 发货结束时间
        Optional.ofNullable(request.getShipEndDate()).ifPresent(endDate ->
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("shipDate"), endDate)));
        // 预计完成开始时间
        Optional.ofNullable(request.getEstimatedCompletionStartTime()).ifPresent(beginDate ->
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("estimatedFinishTime"), beginDate)));
        // 预计完成结束时间
        Optional.ofNullable(request.getEstimatedCompletionEndTime()).ifPresent(endDate ->
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("estimatedFinishTime"), endDate)));
    }
    //数据权限验证
    protected Specification<TmsAlertTaskEntity> makePrivileged(TmsAlertTaskListRequest request) {
        return accessControlService.doPrivileged(new PrivilegeAction<Specification<TmsAlertTaskEntity>>() {
            @Override
            public Specification<TmsAlertTaskEntity> admin() {
                return buildSpecification(request);
            }
            @Override
            public Specification<TmsAlertTaskEntity> employee(Privileges privileges) {
                return buildSpecification(request).and((Specification<TmsAlertTaskEntity>) (root, query, criteriaBuilder) ->
                        CollectionUtils.isEmpty(privileges.getStoreIds()) ? null : root.get("storeId").in(privileges.getStoreIds()));
            }
        });
    }
    protected Specification<TmsAlertTaskEntity> makeBatchProcessPrivileged(TmsAlertTaskListRequest request) {
        return accessControlService.doPrivileged(new PrivilegeAction<Specification<TmsAlertTaskEntity>>() {
            @Override
            public Specification<TmsAlertTaskEntity> admin() {
                return buildBatchProcessTasksSpecification(request);
            }
            @Override
            public Specification<TmsAlertTaskEntity> employee(Privileges privileges) {
                return buildBatchProcessTasksSpecification(request).and((Specification<TmsAlertTaskEntity>) (root, query, criteriaBuilder) ->
                        CollectionUtils.isEmpty(privileges.getStoreIds()) ? null : root.get("storeId").in(privileges.getStoreIds()));
            }
        });
    }
}
