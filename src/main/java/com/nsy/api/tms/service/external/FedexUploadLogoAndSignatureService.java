package com.nsy.api.tms.service.external;

import com.nsy.api.tms.logistics.fedex.uploaddoc.stub.ClientDetail;
import com.nsy.api.tms.logistics.fedex.uploaddoc.stub.ImageId;
import com.nsy.api.tms.logistics.fedex.uploaddoc.stub.Notification;
import com.nsy.api.tms.logistics.fedex.uploaddoc.stub.NotificationSeverityType;
import com.nsy.api.tms.logistics.fedex.uploaddoc.stub.TransactionDetail;
import com.nsy.api.tms.logistics.fedex.uploaddoc.stub.UploadDocumentPortType;
import com.nsy.api.tms.logistics.fedex.uploaddoc.stub.UploadDocumentServiceLocator;
import com.nsy.api.tms.logistics.fedex.uploaddoc.stub.UploadImageDetail;
import com.nsy.api.tms.logistics.fedex.uploaddoc.stub.UploadImagesReply;
import com.nsy.api.tms.logistics.fedex.uploaddoc.stub.UploadImagesRequest;
import com.nsy.api.tms.logistics.fedex.uploaddoc.stub.VersionId;
import com.nsy.api.tms.logistics.fedex.uploaddoc.stub.WebAuthenticationCredential;
import com.nsy.api.tms.logistics.fedex.uploaddoc.stub.WebAuthenticationDetail;
import com.nsy.api.tms.response.base.ApiResponse;
import com.nsy.api.tms.service.TmsConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.net.URL;
import java.util.Map;

@Service
public class FedexUploadLogoAndSignatureService {
    private static final Logger LOGGER = LoggerFactory.getLogger(FedexUploadLogoAndSignatureService.class);

    @Autowired
    TmsConfigService configService;

    public ApiResponse uploadLogoAndSignature(String keyGroup) {
        ApiResponse response = new ApiResponse();
        Map<String, String> configMap = configService.getConfigMap(keyGroup);
        if (configMap.isEmpty()) {
            response.setMessage("keyGroup不存在");
            response.setCode(0);
            return response;
        }
        try {
            UploadImagesRequest request = buildRequest(configMap);
            UploadDocumentServiceLocator service;
            service = new UploadDocumentServiceLocator();
            UploadDocumentPortType port = service.getUploadDocumentServicePort();
            UploadImagesReply reply = port.uploadImages(request);
            response = isResponseOk(reply);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            response.setMessage(e.getMessage());
        }
        return response;
    }
    private UploadImagesRequest buildRequest(Map<String, String> configMap) {
        UploadImagesRequest request = new UploadImagesRequest();
        request.setClientDetail(createClientDetail(configMap.get(FedExService.CONFIG_ACCOUNT_NUMBER), configMap.get(FedExService.CONFIG_METER_NUMBER)));
        request.setWebAuthenticationDetail(createWebAuthenticationDetail(configMap.get(FedExService.CONFIG_KEY), configMap.get(FedExService.CONFIG_PASSWORD)));
        TransactionDetail transactionDetail = new TransactionDetail();
        transactionDetail.setCustomerTransactionId("java sample - Upload Documents Client"); // The client will get the same value back in the response
        request.setTransactionDetail(transactionDetail);
        VersionId versionId = new VersionId("cdus", 11, 0, 0);
        request.setVersion(versionId);

        UploadImageDetail[] upImage = new UploadImageDetail[2];
        upImage[0] = new UploadImageDetail();
        upImage[0].setId(ImageId.IMAGE_1);
        upImage[1] = new UploadImageDetail();
        upImage[1].setId(ImageId.IMAGE_2);
        byte[] imageContent1 = null;
        byte[] imageContent2 = null;
        try {
            // 数字签名
            imageContent1 = getFileInput("https://nsy-products.oss-cn-hangzhou.aliyuncs.com/development/chh/fqi/1605769226793.png");
            // 公司Logo
            imageContent2 = getFileInput("https://nsy-products.oss-cn-hangzhou.aliyuncs.com/development/hhg/kvg/1605769209255.png");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        upImage[0].setImage(imageContent1);
        upImage[1].setImage(imageContent2);
        request.setImages(upImage);
        return request;
    }

    private byte[] getFileInput(String imageUrl) throws Exception {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            URL url = new URL(imageUrl);
            BufferedImage image = ImageIO.read(url);
            String type = imageUrl.substring(imageUrl.lastIndexOf('.') + 1);
            ImageIO.write(image, type, bos);
            return bos.toByteArray();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            bos.close();
        }
        return null;
    }
    private ApiResponse isResponseOk(UploadImagesReply reply) {
        NotificationSeverityType notificationSeverityType = reply.getHighestSeverity();
        ApiResponse response = new ApiResponse();
        if (notificationSeverityType == null) {
            response.setMessage("上传失败");
            response.setCode(0);
            return response;
        }
        if (notificationSeverityType.equals(NotificationSeverityType.SUCCESS)) {
            response.setMessage("SUCCESS");
            response.setCode(1);
        } else if (notificationSeverityType.equals(NotificationSeverityType.WARNING)
                || notificationSeverityType.equals(NotificationSeverityType.NOTE)
                || notificationSeverityType.equals(NotificationSeverityType.ERROR)) {
            Notification[] status = reply.getNotifications(); // 默认两条
            response.setMessage(status[0].getMessage());
            response.setCode(0);
        }
        return response;
    }

    private ClientDetail createClientDetail(String accountNumber, String meterNumber) {
        ClientDetail clientDetail = new ClientDetail();
        clientDetail.setAccountNumber(accountNumber);
        clientDetail.setMeterNumber(meterNumber);
        return clientDetail;
    }

    private WebAuthenticationDetail createWebAuthenticationDetail(String key, String password) {
        WebAuthenticationCredential userCredential = new WebAuthenticationCredential();
        userCredential.setKey(key);
        userCredential.setPassword(password);
        return new WebAuthenticationDetail(null, userCredential);
    }
}
