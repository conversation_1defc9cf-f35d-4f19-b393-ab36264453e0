package com.nsy.api.tms.service;

import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.enumeration.LogisticsMethodEnum;
import com.nsy.api.tms.enumeration.PackageStatusEnum;
import com.nsy.api.tms.enumeration.TrackType;
import com.nsy.api.tms.service.external.BaseLogisticsService;
import com.nsy.api.tms.service.external.refactor.BaseLogisticsNewService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.inject.Inject;
import java.util.List;

@Service
public class TrackWithOfficeSiteService implements ITrackService {

    @Inject
    PackageService packageService;
    @Inject
    LogisticsHelper logisticsHelper;


    private static final Logger LOGGER = LoggerFactory.getLogger(TrackWithOfficeSiteService.class);

    @Override
    public boolean isSupport(TrackType trackType) {
        return TrackType.OFFICE_SITE == trackType;
    }

    @Override
    public void track() {
        List<String> needTrackStatusList = PackageStatusEnum.NEED_TRACK_STATUS_LIST;
        LogisticsMethodEnum.NEED_TO_TRACK_WITH_OFFICE_SITE.stream().forEach((logisticsMethod) -> needTrackStatusList.stream().forEach((status) -> {
            List<TmsPackageEntity> packageEntityList = packageService.findToTrackPackage(logisticsMethod, status);
            for (TmsPackageEntity packageEntity : packageEntityList) {
                doTrack(packageEntity);
            }
        }));
    }

    private void doTrack(TmsPackageEntity packageEntity) {
        try {
            if (StringUtils.hasText(packageEntity.getKeyGroup())) {
                BaseLogisticsService logisticsService = logisticsHelper.getLogisticsServiceByLogisticsMethod(packageEntity.getLogisticsMethod(), packageEntity.getLogisticsChannelCode());
                logisticsService.doTrack(packageEntity);
            } else {
                BaseLogisticsNewService logisticsService = logisticsHelper.getLogisticsService(packageEntity.getLogisticsMethod(), packageEntity.getLogisticsChannelCode());
                logisticsService.doTrack(packageEntity);
            }
//            tmsTagAndAlertService.handleTagAndTask(packageEntity);
        } catch (Exception e) {
            LOGGER.info("logisticsNo:{},logisticsMethod:{},logisticsChannel:{}, exception occur when invoking track-api, exception:{}", packageEntity.getLogisticsNo(), packageEntity.getLogisticsMethod(), packageEntity.getLogisticsChannelCode(), e.getMessage());
            LOGGER.error(e.getMessage(), e);
            return;
        }
    }

    @Override
    public void track(TmsPackageEntity packageEntity) {
        doTrack(packageEntity);
    }


}
