package com.nsy.api.tms.service.external;

import com.alibaba.fastjson.JSON;
import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.dao.entity.TmsRouteRecordEntity;
import com.nsy.api.tms.enumeration.PackageStatusEnum;
import com.nsy.api.tms.logistics.jiacheng.JiaChengTrackRequest;
import com.nsy.api.tms.logistics.jiacheng.response.JiaChengTrackResponse;
import com.nsy.api.tms.logistics.jiacheng.response.Statusdetail;
import com.nsy.api.tms.service.TmsRouteService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * HXD
 * 2021/5/27
 **/
@Service
public class JiaChengTrackService extends JiaChengService {

    private static final String SERVICE_METHOD = "track";
    private static final Logger LOGGER = LoggerFactory.getLogger(JiaChengTrackService.class);

    @Inject
    private RestTemplate restTemplate;

    @Autowired
    private TmsRouteService tmsRouteService;

    public void doTrack(TmsPackageEntity packageEntity, String serviceUrl) {
        TmsRequestLogEntity logEntity = tmsRequestLogService.recordBaseInfoLog(packageEntity);
        //更新tms_package 表路由查询次数
        packageEntity.setRouteSearchTimes(packageEntity.getRouteSearchTimes() + 1);
        packageService.save(packageEntity);
        JiaChengTrackRequest trackRequest = new JiaChengTrackRequest();
        trackRequest.setWaybillnumber(packageEntity.getLogisticsNo());
        trackRequest.setCustomerid("-1");
        String requestContent = JSONUtils.toJSON(trackRequest);
        String request = Base64.getEncoder().encodeToString(requestContent.getBytes(StandardCharsets.UTF_8));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        params.add("service", SERVICE_METHOD);
        params.add("data_body", request);
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(params, headers);
        try {
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(serviceUrl, requestEntity, String.class);
            if (HttpStatus.OK.equals(responseEntity.getStatusCode()) && StringUtils.hasText(responseEntity.getBody()) && responseEntity.getBody().contains(packageEntity.getLogisticsNo())) {
                JiaChengTrackResponse trackResponse = JSONUtils.fromJSON(responseEntity.getBody(), JiaChengTrackResponse.class);
                processSuccessTrackReply(packageEntity, trackResponse);
                tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, request, JSON.toJSONString(trackResponse), packageEntity.getLogisticsNo());
            } else {
                tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, request, JSONUtils.toJSON(responseEntity), packageEntity.getLogisticsNo());
                LOGGER.error("佳成物流更新失败：request=={}==， response=={}==", requestContent, JSONUtils.toJSON(responseEntity));
            }
        } catch (Exception e) {
            tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, request, e.getMessage(), packageEntity.getLogisticsNo());
            LOGGER.error(e.getMessage(), e);
        }

    }

    private void processSuccessTrackReply(TmsPackageEntity packageEntity, JiaChengTrackResponse response) {
        if (CollectionUtils.isEmpty(response.getDisplaydetail().get(0).getStatusdetail())) {
            return;
        }
        //1. 更新tms_package状态，同时，写路由表（当前查询路由信息与上一条路由表记录状态一致，则不重新插入）
        List<TmsRouteRecordEntity> routeRecordEntityList = tmsRouteService.findByLogisticsNoOrderByCreateDateDesc(packageEntity.getLogisticsNo());
        List<Statusdetail> detailList = Optional.ofNullable(response.getDisplaydetail().get(0).getStatusdetail()).orElse(new ArrayList<>());
        detailList.sort(Comparator.comparing((e) -> DateUtils.parse(e.getTime(), DateUtils.DATE_FORMAT_DATE4)));
        Collections.reverse(detailList);
        Statusdetail latestDetails = detailList.get(0);
        // 新增或更新路由
        if (routeRecordEntityList.isEmpty()
                || !(routeRecordEntityList.get(0).getAcceptTime().compareTo(DateUtils.parse(latestDetails.getTime(), DateUtils.DATE_FORMAT_DATE4)) == 0
                && routeRecordEntityList.get(0).getAcceptAddress().equalsIgnoreCase(latestDetails.getLocate())
                && routeRecordEntityList.get(0).getRemark().equalsIgnoreCase(latestDetails.getStatuscnname()))) {
            persistTmsRouteRecord(packageEntity.getLogisticsNo(), latestDetails, packageEntity.getStatus());
        }
        packageEntity.setRouteLastUpdateTime(DateUtils.parse(latestDetails.getTime(), DateUtils.DATE_FORMAT_DATE4));
        if ("派送妥投".equals(latestDetails.getStatuscnname()) || "Delivered".equalsIgnoreCase(latestDetails.getStatus())) {
            packageEntity.setStatus(PackageStatusEnum.DELIVERED.getDesc());
        } else {
            packageEntity.setStatus(PackageStatusEnum.TRANSIT.getDesc());
        }
        packageService.save(packageEntity);
    }

    private void persistTmsRouteRecord(String logisticsNo, Statusdetail details, String status) {
        TmsRouteRecordEntity routeRecordEntity = new TmsRouteRecordEntity();
        routeRecordEntity.setStatus(status);
        routeRecordEntity.setAcceptAddress(details.getLocate());
        routeRecordEntity.setAcceptTime(DateUtils.parse(details.getTime(), DateUtils.DATE_FORMAT_DATE4));
        routeRecordEntity.setLogisticsNo(logisticsNo);
        routeRecordEntity.setRemark(details.getStatus());
        tmsRouteService.save(routeRecordEntity);
    }
}
