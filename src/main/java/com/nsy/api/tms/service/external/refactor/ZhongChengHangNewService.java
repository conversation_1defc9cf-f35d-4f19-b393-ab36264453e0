package com.nsy.api.tms.service.external.refactor;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.api.core.apicore.util.MD5Util;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.annotation.LogisticsServiceHandler;
import com.nsy.api.tms.dao.entity.TmsLogisticsAccountEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.domain.Address;
import com.nsy.api.tms.domain.OrderItemInfo;
import com.nsy.api.tms.domain.order.OrderNewInfo;
import com.nsy.api.tms.enumeration.LogisticsMethodEnum;
import com.nsy.api.tms.logistics.zch.Items;
import com.nsy.api.tms.logistics.zch.Result;
import com.nsy.api.tms.logistics.zch.TransferResult;
import com.nsy.api.tms.logistics.zch.ZchAddress;
import com.nsy.api.tms.logistics.zch.ZchResponse;
import com.nsy.api.tms.logistics.zch.ZhongChengHangRequest;
import com.nsy.api.tms.request.OrderNewRequest;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.response.PrintLabelResponse;
import com.nsy.api.tms.response.SecondaryNumberResponse;
import com.nsy.api.tms.service.external.ZhongChengHangTrackService;
import com.nsy.api.tms.utils.FileUtils;
import com.nsy.api.tms.utils.JsonMapper;
import com.nsy.api.tms.utils.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * HXD
 * 2021/5/11
 **/
@Service
@LogisticsServiceHandler(logisticsMethod = LogisticsMethodEnum.ZHONGCHENGHANG)
public class ZhongChengHangNewService extends BaseLogisticsNewService implements InitializingBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(ZhongChengHangNewService.class);

    private static final String LABEL_NAME = "%sZCH-%s";

    public static final String VERSION = "version";
    public static final String CLIENT_KEY = "client_key";
    public static final String CLIENT_SECRET = "client_secret";

    @Value("${zhongchenghang.server.url}")
    private String zchServiceURL;
    @Value("${zhongchenghang.server.label}")
    private String zchServiceLabel;
    @Value("${zhongchenghang.server.transfer}")
    private String zchServiceTransfer;
    @Inject
    private ZhongChengHangTrackService zhongChengHangTrackService;

    @Inject
    private RestTemplate restTemplate;

    @Override
    public void preDeal(OrderNewRequest request) {
        reSetUserPriceByTotalNum(request.getOrderInfo().getOrderItemInfoList(), request);
    }

    @Override
    protected ZhongChengHangRequest buildLogisticsOrderRequest(OrderNewRequest orderRequest, Map<String, String> configMap) {
        ZhongChengHangRequest request = new ZhongChengHangRequest();
        OrderNewInfo orderInfo = orderRequest.getOrderInfo();
        request.setGenerateLabelUrl(Boolean.TRUE);
        request.setGenerateLabel(Boolean.TRUE);
        request.setSendAddress(buildSender(orderInfo.getSender()));
        request.setToAddress(buildReceiver(orderInfo.getReceiver(), orderInfo.getReceiveCountryCode()));
        request.setWidth(BigDecimal.valueOf(orderInfo.getWidth()));
        request.setLength(BigDecimal.valueOf(orderInfo.getLength()));
        request.setHeight(BigDecimal.valueOf(orderInfo.getHeight()));
        request.setCustomerRefNo(orderInfo.getTid());
        request.setImageType("PDF");
        request.setNonDeliveryOption("RETURN");
        request.setArticleName(org.apache.commons.lang3.StringUtils.substring(orderInfo.getOrderItemInfoList().stream()
                .map(OrderItemInfo::getCnName).filter(StringUtils::hasText).distinct().collect(Collectors.joining(",")), 0, 49));
        request.setItems(buildItems(orderInfo));
        request.setWaybillType("Ordinary");
        request.setProvider(orderInfo.getLogisticsChannelCode());
        request.setTotalDeclaredValue(BigDecimal.valueOf(orderInfo.getOrderItemInfoList().stream().filter(x -> x.getUnitPrice() != null)
                .mapToDouble(orderItem -> orderItem.getUnitPrice() * orderItem.getCount()).sum()));
        request.setArticleEnglishName(org.apache.commons.lang3.StringUtils.substring(orderInfo.getOrderItemInfoList().stream()
                .map(OrderItemInfo::getEnName).filter(StringUtils::hasText).distinct().collect(Collectors.joining(",")), 0, 49));
        request.setWeightUnit("千克");
        request.setVolumeUnit("厘米");
        request.setWeight(BigDecimal.valueOf(orderInfo.getWeight()));
        request.setTotalDeclaredValueCurrency(1);
        request.setIossNumber(orderInfo.getIossNumber());
        return request;
    }

    private List<Items> buildItems(OrderNewInfo orderInfo) {
        return orderInfo.getOrderItemInfoList().stream().map(orderItem -> {
            Items items = new Items();
            items.setCargoName(orderItem.getCnName());
            items.setHscode(orderItem.getHsCode());
            items.setQty(orderItem.getCount());
            items.setCargoEnglishName(orderItem.getEnName());
            items.setDeclaredValue(BigDecimal.valueOf(orderItem.getUnitPrice()));
            items.setItemWeight(BigDecimal.valueOf(orderItem.getWeight()));
            items.setUnitPrice(BigDecimal.valueOf(orderItem.getUnitPrice()));
            return items;
        }).collect(Collectors.toList());
    }

    private ZchAddress buildReceiver(Address sysReceiver, String receiveCountryCode) {
        if (Objects.equals(receiveCountryCode, "CN")) {
            throw new BusinessServiceException("目的地国家不能是中国，请核对");
        }
        ZchAddress receiver = new ZchAddress();
        receiver.setName(sysReceiver.getName());
        if (sysReceiver.getStreet().length() < 80) {
            receiver.setAddress1(sysReceiver.getStreet());
        } else if (sysReceiver.getStreet().length() < 159) {
            receiver.setAddress1(org.apache.commons.lang3.StringUtils.substring(sysReceiver.getStreet(), 0, 80));
            receiver.setAddress2(org.apache.commons.lang3.StringUtils.substring(sysReceiver.getStreet(), 80, 160));
        } else {
            throw new BusinessServiceException("地址超过160，请减少地址长度");
        }
        receiver.setCountryCode(receiveCountryCode);
        receiver.setState(sysReceiver.getProvince());
        receiver.setCity(sysReceiver.getCity());
        if (Objects.equals(receiveCountryCode, "US")) {
            receiver.setZipCode5(org.apache.commons.lang3.StringUtils.substring(sysReceiver.getPostCode(), 0, 5));
        } else {
            receiver.setZipCode5(sysReceiver.getPostCode());
        }
        receiver.setEmail(sysReceiver.getEmail());
        receiver.setPhone(StringUtils.hasText(sysReceiver.getPhone()) ? sysReceiver.getPhone() : sysReceiver.getMobile());
        return receiver;
    }

    private ZchAddress buildSender(Address sysSender) {
        ZchAddress sender = new ZchAddress();
        sender.setName(sysSender.getName());
        if (sysSender.getStreet().length() < 80) {
            sender.setAddress1(sysSender.getStreet());
        } else if (sysSender.getStreet().length() < 159) {
            sender.setAddress1(org.apache.commons.lang3.StringUtils.substring(sysSender.getStreet(), 0, 79));
            sender.setAddress2(org.apache.commons.lang3.StringUtils.substring(sysSender.getStreet(), 79, 159));
        }
        sender.setCountryCode("CN");
        sender.setState(sysSender.getProvince());
        sender.setCity(sysSender.getCity());
        sender.setZipCode5(sysSender.getPostCode());
        sender.setPhone(StringUtils.hasText(sysSender.getPhone()) ? sysSender.getPhone() : sysSender.getMobile());
        return sender;
    }

    @Override
    public SecondaryNumberResponse getSecondaryNumber(TmsPackageEntity packageEntity) {
        if (StringUtils.hasText(packageEntity.getSecondaryNumber())) {
            return super.getSecondaryNumber(packageEntity);
        }
        Map<String, String> configMap = logisticsAccountService.buildAccountConfig(packageEntity.getLogisticsAccountId());
        List<String> list = new ArrayList<>();
        list.add(packageEntity.getTid());
        HttpEntity<String> entity = new HttpEntity<>(JsonMapper.toJson(list), buildHeader(configMap));
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(zchServiceTransfer + packageEntity.getLogisticsChannelCode(), entity, String.class);
        if (!HttpStatus.OK.equals(responseEntity.getStatusCode()) || !StringUtils.hasText(responseEntity.getBody())) {
            throw new RuntimeException("中成航获取转运单号失败：" + JsonMapper.toJson(responseEntity));
        }
        TransferResult transferResult = JsonMapper.fromJson(responseEntity.getBody(), TransferResult.class);
        if (transferResult != null && !CollectionUtils.isEmpty(transferResult.getResult())) {
            packageEntity.setSecondaryNumber(transferResult.getResult().get(0).getTrackingNo());
            packageService.save(packageEntity);
        }
        return super.getSecondaryNumber(packageEntity);
    }


    @Override
    public GenerateOrderResponse syncGenerateOrder(OrderNewRequest orderRequest, TmsLogisticsAccountEntity logisticsAccountEntity) {
        TmsRequestLogEntity logEntity = requestLogService.recordBaseInfoLog(orderRequest.getOrderInfo());
        GenerateOrderResponse response = new GenerateOrderResponse();
        ZchResponse zchResponse = null;
        Map<String, String> configMap = buildAccountConfig(logisticsAccountEntity, orderRequest.getOrderInfo());
        ZhongChengHangRequest zhongChengHangRequest = buildLogisticsOrderRequest(orderRequest, configMap);
        String requestContent = JsonMapper.toJson(zhongChengHangRequest);
        try {
            HttpEntity<String> entity = new HttpEntity<>(requestContent, buildHeader(configMap));
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(zchServiceURL, entity, String.class);
            if (!HttpStatus.OK.equals(responseEntity.getStatusCode()) || !StringUtils.hasText(responseEntity.getBody())) {
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, requestContent, responseEntity.getBody(), null);
                throw new RuntimeException("中成航获取面单失败：" + JsonMapper.toJson(responseEntity));
            }
            zchResponse = JSONUtils.fromJSON(responseEntity.getBody(), ZchResponse.class);
            if (zchResponse.getResponseCode() == 0 && zchResponse.getResult().getIsSuccess()) {
                GenerateOrderResponse.SuccessEntity successEntity = processSuccessReply(orderRequest.getOrderInfo(), zchResponse.getResult());
                response.setSuccessEntity(successEntity);
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, requestContent, JsonMapper.toJson(zchResponse), successEntity.getLogisticsNo());
            } else {
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, requestContent, responseEntity.getBody(), null);
                LOGGER.error("中成航物流下单失败：request=={}==， response=={}==", requestContent, JSONUtils.toJSON(responseEntity));
                response.setError(buildError("400", judgeResponse(zchResponse)));
            }
        } catch (Exception e) {
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, requestContent, zchResponse == null ? e.getMessage() : JSONUtils.toJSON(zchResponse), null);
            LOGGER.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return response;
    }

    public static HttpHeaders buildHeader(Map<String, String> configMap) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON_UTF8));
        headers.add("version", configMap.get(VERSION));
        headers.add("key", configMap.get(CLIENT_KEY));
        headers.add("token", MD5Util.crypt(configMap.get(CLIENT_KEY) + configMap.get(CLIENT_SECRET) + configMap.get(VERSION)));
        return headers;
    }

    private String judgeResponse(ZchResponse zchResponse) {
        if (zchResponse == null) {
            return "";
        }
        String errorMsg = "";
        if (StringUtils.hasText(zchResponse.getErrorMessage())) {
            errorMsg = zchResponse.getErrorMessage();
        }
        if (zchResponse.getResult() != null && StringUtils.hasText(zchResponse.getResult().getErrorMessage())) {
            errorMsg += zchResponse.getResult().getErrorMessage();
        }
        if (StringUtils.hasText(errorMsg)) {
            return errorMsg;
        }
        return "获取面单失败，请联系技术人员";
    }

    private GenerateOrderResponse.SuccessEntity processSuccessReply(OrderNewInfo orderInfo, Result result) {
        String trackingNumber = result.getBarCode();
        String labelUrl = downloadLabelPdfAndUploadOSS(trackingNumber, result.getFileUrl());
        return buildSuccessEntity(orderInfo, trackingNumber, labelUrl, result.getWaybillNo(), null);
    }

    private String downloadLabelPdfAndUploadOSS(String trackingNumber, String labelURL) {
        try {
            byte[] pdfByte = FileUtils.downloadPdf(labelURL, restTemplate);
            String labelFileName = String.format(LABEL_NAME, labelFolder, trackingNumber);
            return getPdfLabel(pdfByte, labelFileName);
        } catch (Exception e) {
            LOGGER.error("中成航面单无法获取: " + e.getMessage());
            return "";
        }
    }

    @Override
    public PrintLabelResponse printLabel(TmsPackageEntity tmsPackageEntity) {
        if (StringUtils.hasText(tmsPackageEntity.getLabelUrl())) {
            return super.printLabel(tmsPackageEntity);
        }
        Map<String, String> configMap = logisticsAccountService.buildAccountConfig(tmsPackageEntity.getLogisticsAccountId());
        HttpEntity<Object> httpEntity = new HttpEntity<>(buildHeader(configMap));
        String format = String.format(zchServiceLabel, tmsPackageEntity.getLogisticsTid());
        ResponseEntity<Result> responseEntity = restTemplate.exchange(format, HttpMethod.GET, httpEntity, Result.class);
        if (responseEntity.getBody() != null && responseEntity.getBody().getErrorCode() == 0 && responseEntity.getBody().getIsSuccess()) {
            String labelUrl = downloadLabelPdfAndUploadOSS(tmsPackageEntity.getLogisticsNo(), responseEntity.getBody().getFileUrl());
            if (StringUtils.hasText(labelUrl)) {
                tmsPackageEntity.setLabelUrl(labelUrl);
                packageService.save(tmsPackageEntity);
            }
        } else {
            throw new BusinessServiceException("中成航无法获取面单");
        }
        return super.printLabel(tmsPackageEntity);
    }

    @Override
    protected String buildSerializedRequest(OrderNewRequest request, TmsLogisticsAccountEntity logisticsAccountEntity) {
        Map<String, String> configMap = buildAccountConfig(logisticsAccountEntity, request.getOrderInfo());
        ZhongChengHangRequest request1 = buildLogisticsOrderRequest(request, configMap);
        return JSONUtils.toJSON(request1);
    }

    @Override
    public void validateOrderRequest(OrderNewRequest request) {
        OrderNewInfo orderInfo = request.getOrderInfo();
        super.validateOrderRequest(request);
        Validator.isValid(orderInfo.getReceiver(), attr -> !Objects.isNull(attr), "receiver 节点不能为空");
        Validator.isValid(orderInfo.getReceiver().getPostCode(), StringUtils::hasText, "receiver.postCode 节点不能为空");
        orderInfo.getOrderItemInfoList().forEach(item -> {
            Validator.isValid(item.getCount(), attr -> !Objects.isNull(attr), "OrderItemInfo.count 不能为空");
            Validator.isValid(item.getCustomsPrice(), attr -> !Objects.isNull(attr), "OrderItemInfo.customsPrice 不能为空");
        });
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.labelFolder += "ZhongChengHang/";
        this.ossLabelFolder += "ZhongChengHang/";
    }


    @Override
    public void doTrack(TmsPackageEntity tmsPackageEntity) {
        zhongChengHangTrackService.doTrack(tmsPackageEntity);
    }

    //    @Override
    //    public void doTrack(TmsPackageEntity tmsPackageEntity) {
    //        trackService.doTrack(tmsPackageEntity, jcServiceURL);
    //    }

}
