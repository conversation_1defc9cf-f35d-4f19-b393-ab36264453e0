package com.nsy.api.tms.service;

import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.tms.dao.entity.TmsLogisticsCompanyAttachEntity;
import com.nsy.api.tms.repository.TmsLogisticsCompanyAttachRepository;
import com.nsy.api.tms.request.TmsLogisticsCompanyAttachRequest;
import com.nsy.api.tms.service.privilege.AccessControlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/29 13:53
 */
@Service
public class TmsLogisticsCompanyAttachService {

    @Autowired
    private TmsLogisticsCompanyAttachRepository logisticsCompanyAttachRepository;

    @Autowired
    private AccessControlService accessControlService;

    public List<TmsLogisticsCompanyAttachEntity> getLogisticsAttachInfo(Integer logisticsCompanyId) {
        return logisticsCompanyAttachRepository.findByLogisticsId(logisticsCompanyId);
    }

    public void addLogisticsCompanyAttachInfoList(List<TmsLogisticsCompanyAttachRequest> attachRequestList, Integer logisticsCompanyId) {
        //先删除数据库内的信息在统一新增
        this.removeByLogisticsIdList(logisticsCompanyId);
        logisticsCompanyAttachRepository.saveAll(attachRequestList.stream().map(attach -> {
            TmsLogisticsCompanyAttachEntity attachEntity = new TmsLogisticsCompanyAttachEntity();
            BeanUtilsEx.copyProperties(attach, attachEntity);
            attachEntity.setLogisticsId(logisticsCompanyId);
            attachEntity.setCreateBy(accessControlService.getUserName());
            attachEntity.setUpdateBy(accessControlService.getUserName());
            return attachEntity;
        }).collect(Collectors.toList()));
    }

    public void removeByLogisticsIdList(Integer logisticsId) {
        logisticsCompanyAttachRepository.deleteByLogisticsId(logisticsId);
    }
}
