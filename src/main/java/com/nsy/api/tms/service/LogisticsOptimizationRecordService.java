package com.nsy.api.tms.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.tms.dao.entity.LogisticsOptimizationRecordEntity;
import com.nsy.api.tms.domain.OptimizationRecord;
import com.nsy.api.tms.mapper.LogisticsOptimizationRecordMapper;
import com.nsy.api.tms.request.LogisticsCompanyMatchRequest;
import com.nsy.api.tms.utils.JsonMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 订单匹配物流记录(OrderMatchLogisticsCompanyRecord)服务层
 *
 * <AUTHOR>
 * @since 2023-04-06 17:24:49
 */
@Service
public class LogisticsOptimizationRecordService extends ServiceImpl<LogisticsOptimizationRecordMapper, LogisticsOptimizationRecordEntity> {

    /**
     * 根据店铺id,  mappingIds找出当天单量较少的一个
     * 相当于轮询算法
     * <AUTHOR>
     * 2023-04-06
     */
    public Integer getMinRecordCountByStoreIdToday(Integer storeId, List<Integer> mappingIds) {
        Date date = new Date();
        DateTime dateBegin = DateUtil.beginOfDay(date);
        DateTime dateEnd = DateUtil.endOfDay(date);
        List<OptimizationRecord> minRecordCountByStoreIdToday = this.getBaseMapper().getMinRecordCountByStoreIdToday(dateBegin, dateEnd, mappingIds, storeId);
        mappingIds.forEach(item -> {
            if (minRecordCountByStoreIdToday.stream().noneMatch(it -> item.equals(it.getMappingId()))) {
                OptimizationRecord record = new OptimizationRecord();
                record.setMappingId(item);
                record.setRecordCount(0);
                minRecordCountByStoreIdToday.add(0, record);
            }
        });
        return minRecordCountByStoreIdToday.get(0).getMappingId();
    }

    public LogisticsOptimizationRecordEntity addRecord(LogisticsCompanyMatchRequest request) {
        LogisticsOptimizationRecordEntity recordEntity = new LogisticsOptimizationRecordEntity();
        recordEntity.setMatchDate(new Date());
        recordEntity.setTid(request.getTid());
        recordEntity.setLocation(request.getLocation());
        save(recordEntity);
        return recordEntity;
    }

    // 记录匹配信息
    public void recordMessage(LogisticsOptimizationRecordEntity recordEntity, String message) {
        recordEntity.setMessage(StrUtil.maxLength(message, 250));
        updateById(recordEntity);
    }

    // 成功匹配
    public void successRecord(Integer mappingId, LogisticsOptimizationRecordEntity recordEntity, LogisticsCompanyMatchRequest request) {
        recordEntity.setMappingId(mappingId);
        recordEntity.setMessage(StrUtil.maxLength(JsonMapper.toJson(request), 250));
        recordEntity.setStoreId(request.getStoreId());
        updateById(recordEntity);
    }
}
