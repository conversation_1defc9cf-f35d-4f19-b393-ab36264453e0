package com.nsy.api.tms.service.external.refactor;

import com.alibaba.fastjson.JSON;
import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.dao.entity.TmsRouteRecordEntity;
import com.nsy.api.tms.enumeration.PackageStatusEnum;
import com.nsy.api.tms.logistics.aliexpress.request.track.AlieExpressTrackRequest;
import com.nsy.api.tms.logistics.aliexpress.response.track.AliExpressTrackResponse;
import com.nsy.api.tms.logistics.aliexpress.response.track.TrackDetail;
import com.nsy.api.tms.service.TmsRouteService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Service
public class AliExpressTrackNewService extends AliExpressNewService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AliExpressTrackNewService.class);

    @Autowired
    TmsRouteService routeService;

    public void doTrack(TmsPackageEntity packageEntity) {
        // 记录日志
        TmsRequestLogEntity logEntity = requestLogService.recordBaseInfoLog(packageEntity);
        //更新路由查询次数
        packageEntity.setRouteSearchTimes(packageEntity.getRouteSearchTimes() + 1);
        packageService.save(packageEntity);
        Map<String, String> configMap = buildTrackConfig(packageEntity);
        String trackRequest = null;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
            params.add("sessionKey", configMap.get(CONFIG_SESSION_KEY));
            params.add("request", populateTrackRequest(packageEntity));
            String url = serverUrl + "/Querytrackingresult";
            LOGGER.info("物流追踪URL:{}", url);
            trackRequest = populateTrackRequest(packageEntity);
            LOGGER.info("LogisticsNo:{}, request:{}", packageEntity.getLogisticsNo(), trackRequest);
            AliExpressTrackResponse aliExpressTrackResponse = restTemplate.postForObject(url, params, AliExpressTrackResponse.class);
            LOGGER.info("LogisticsNo:{}, response:{}", packageEntity.getLogisticsNo(), JSON.toJSONString(aliExpressTrackResponse));
            if ("true".equalsIgnoreCase(aliExpressTrackResponse.getResultSuccess())) {
                //调用成功
                processSuccessResponse(packageEntity, trackRequest, aliExpressTrackResponse, logEntity);
            } else {
                processFailedResponse(trackRequest, aliExpressTrackResponse, packageEntity.getLogisticsNo(), logEntity);
                LOGGER.info("LogisticsNo:{},路由查询异常,响应值:{}", packageEntity.getLogisticsNo(), JSON.toJSONString(aliExpressTrackResponse));
            }
        } catch (Exception e) {
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, trackRequest, e.getMessage(), packageEntity.getLogisticsNo());
            throw new RuntimeException(e);
        }
    }

    private Map<String, String> buildTrackConfig(TmsPackageEntity packageEntity) {
        // todo
//        String keyGroup = channelConfigEntity.getKeyGroup();
//        if ("stage".equalsIgnoreCase(env.getActiveProfiles()[0])) {
//            keyGroup = keyGroup + "_Track";
//        }
        return logisticsAccountService.buildAccountConfig(packageEntity.getLogisticsAccountId());
    }

    private void processFailedResponse(String trackRequest, AliExpressTrackResponse aliExpressTrackResponse, String logisticsNo, TmsRequestLogEntity logEntity) {
        requestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, trackRequest, JSONUtils.toJSON(aliExpressTrackResponse), logisticsNo);
    }

    private void processSuccessResponse(TmsPackageEntity packageEntity, String trackRequest, AliExpressTrackResponse aliExpressTrackResponse, TmsRequestLogEntity logEntity) {
        // 1. 更新日志
        requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, trackRequest, JSONUtils.toJSON(aliExpressTrackResponse), packageEntity.getLogisticsNo());
        // 3. 更新tms_package状态，同时，写路由表（当前查询路由信息与上一条路由表记录状态一致，则不重新插入）
        TrackDetail latestTrackDetail = aliExpressTrackResponse.getTrackDetails().get(0);
        if (Objects.isNull(latestTrackDetail) || !StringUtils.hasText(latestTrackDetail.getEventDesc())) {
            LOGGER.info("logisticsNo:{}, 暂无路由信息", packageEntity.getLogisticsNo());
            return;
        }
        //最近一次物流时间
        Date latestTrackDate = DateUtils.parse(latestTrackDetail.getEventDate(), "yyyy-MM-dd HH:mm:ss");
        //最近一次物流address
        String latestTrackLocation = !StringUtils.hasText(latestTrackDetail.getAddress()) ? null : latestTrackDetail.getAddress();
        List<TmsRouteRecordEntity> routeRecordEntityList = routeService.findByLogisticsNoOrderByCreateDateDesc(packageEntity.getLogisticsNo());
        if (routeRecordEntityList != null && !routeRecordEntityList.isEmpty()) {
            TmsRouteRecordEntity lastRouteRecord = routeRecordEntityList.get(0);
            //  如果数据库中的最近一条和查询到的状态一样，则不用保存
            if (latestTrackDetail.getEventDesc().equalsIgnoreCase(lastRouteRecord.getRemark())
                    && latestTrackDate.compareTo(lastRouteRecord.getAcceptTime()) == 0) {
                LOGGER.info("logisticsNo:{}, 路由信息没有新的变更", packageEntity.getLogisticsNo());
                LOGGER.info("last route:{}", lastRouteRecord.toString());
                return;
            }
        }
        //保存最新一条路由记录
        saveTmsRouteRecord(packageEntity.getLogisticsNo(), latestTrackDetail, latestTrackDate, latestTrackLocation, aliExpressTrackResponse.getTrackDetails());
        if ("CREATED".equalsIgnoreCase(parseStatusByEventDesc(latestTrackDetail.getEventDesc()))) {
            packageEntity.setStatus(PackageStatusEnum.SHIPPED.getDesc());
        } else if ("Delivered".equalsIgnoreCase(parseStatusByEventDesc(latestTrackDetail.getEventDesc()))) {
            packageEntity.setStatus(PackageStatusEnum.DELIVERED.getDesc());
            packageEntity.setArrivalTime(latestTrackDate);
        } else if ("PU".equalsIgnoreCase(parseStatusByEventDesc(latestTrackDetail.getEventDesc()))) {
            packageEntity.setStatus(PackageStatusEnum.TAKEN.getDesc());
        } else if ("Alert".equalsIgnoreCase(parseStatusByEventDesc(latestTrackDetail.getEventDesc()))) {
            packageEntity.setStatus(PackageStatusEnum.TRANSIT.getDesc());
//            generateAlertTask(packageEntity);
        } else {
            packageEntity.setStatus(PackageStatusEnum.TRANSIT.getDesc());
        }
        //处理针对状态为delivered仍在继续deliver的
        handleSpecialCase(aliExpressTrackResponse, packageEntity);
        if (Objects.isNull(packageEntity.getReceiveTime())) {
            for (TrackDetail trackDetail : aliExpressTrackResponse.getTrackDetails()) {
                if ("PU".equalsIgnoreCase(parseStatusByEventDesc(trackDetail.getEventDesc()))) {
                    packageEntity.setReceiveTime(DateUtils.parse(trackDetail.getEventDate(), "yyyy-MM-dd HH:mm:ss"));
                }
            }
        }
        packageEntity.setRouteLastUpdateTime(latestTrackDate);
        packageService.save(packageEntity);
    }

    private void handleSpecialCase(AliExpressTrackResponse aliExpressTrackResponse, TmsPackageEntity packageEntity) {
        aliExpressTrackResponse.getTrackDetails().forEach(item -> {
            if (item.getEventDesc().toLowerCase().contains("delivered")) {
                packageEntity.setStatus(PackageStatusEnum.DELIVERED.getDesc());
                packageEntity.setArrivalTime(DateUtils.parse(item.getEventDate(), "yyyy-MM-dd HH:mm:ss"));
            }
        });
    }

    private void saveTmsRouteRecord(String logisticsNo, TrackDetail latestTrackDetail, Date latestTrackDate, String latestTrackLocation, List<TrackDetail> details) {
        TmsRouteRecordEntity routeRecordEntity = new TmsRouteRecordEntity();
        routeRecordEntity.setStatus(parseStatusByEventDesc(latestTrackDetail.getEventDesc()));
        routeRecordEntity.setAcceptAddress(latestTrackLocation);
        routeRecordEntity.setAcceptTime(latestTrackDate);
        routeRecordEntity.setLogisticsNo(logisticsNo);
        routeRecordEntity.setRemark(latestTrackDetail.getEventDesc());
        details.forEach(item -> {
            if (item.getEventDesc().toLowerCase().contains("delivered")) {
                routeRecordEntity.setStatus("Delivered");
            }
        });
        routeService.save(routeRecordEntity);
    }


    private String populateTrackRequest(TmsPackageEntity packageEntity) {
        AlieExpressTrackRequest alieExpressTrackRequest = new AlieExpressTrackRequest();
        alieExpressTrackRequest.setLogisticsNo(packageEntity.getLogisticsNo());
        alieExpressTrackRequest.setOutRef(packageEntity.getTmsTid());
        alieExpressTrackRequest.setOrigin("ESCROW");
        alieExpressTrackRequest.setServiceName("CPAM");
        alieExpressTrackRequest.setToArea("CN");
        return JSON.toJSONString(alieExpressTrackRequest);
    }

    private String parseStatusByEventDesc(String eventDesc) {
        return eventDesc.contains("Accepted by carrier") ? "PU"
                : eventDesc.contains("Shipment confirmation") ? "CREATED"
                : eventDesc.toLowerCase().contains("delivered")
                ? "Delivered" : eventDesc.contains("Shipment cancelled")
                ? "Alert" : "In Transit";
    }
}
