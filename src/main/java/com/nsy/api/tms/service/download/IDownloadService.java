package com.nsy.api.tms.service.download;

import com.nsy.api.tms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.tms.request.DownloadRequest;
import com.nsy.api.tms.response.base.DownloadResponse;

public interface IDownloadService {

    /**
     * 下载队列类型
     */
    QuartzDownloadQueueTypeEnum type();

    /**
     * 分页查询下载数据
     */
    DownloadResponse queryExportData(DownloadRequest request);
}
