package com.nsy.api.tms.service.upload;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.enumeration.QuartzUploadQueueTypeEnum;
import com.nsy.api.tms.enumeration.ValuationMethodEnum;
import com.nsy.api.tms.request.upload.TmsLogisticsFreightImport;
import com.nsy.api.tms.request.upload.UploadRequest;
import com.nsy.api.tms.response.upload.UploadResponse;
import com.nsy.api.tms.service.TmsUploadLogisticsFreightService;
import com.nsy.api.tms.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;


@Service
public class PriceFirstUploadService implements IProcessUploadDataService {
    @Autowired
    private TmsUploadLogisticsFreightService uploadLogisticsFreightService;


    @Override
    public QuartzUploadQueueTypeEnum type() {
        return QuartzUploadQueueTypeEnum.TMS_LOGISTICS_PRICE_FIRST_RENEWAL_HEAVY;
    }

    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        UploadResponse response = new UploadResponse();
        if (!StringUtils.hasText(request.getDataJsonStr())) {
            return response;
        }
        List<TmsLogisticsFreightImport> importList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), TmsLogisticsFreightImport.class);
        if (CollectionUtils.isEmpty(importList)) {
            return response;
        }
        List<TmsLogisticsFreightImport> errorList = new ArrayList<>();
        importList.forEach(row -> {
            try {
                uploadLogisticsFreightService.priceUpload(row, request, ValuationMethodEnum.FIRST_RENEWAL);
            } catch (BusinessServiceException e) {
                row.setErrorMsg(e.getMessage());
                errorList.add(row);
            }
        });
        if (!CollectionUtils.isEmpty(errorList)) {
            response.setDataJsonStr(JsonMapper.toJson(errorList));
        }
        return response;
    }
}
