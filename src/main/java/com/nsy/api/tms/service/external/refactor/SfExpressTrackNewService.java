package com.nsy.api.tms.service.external.refactor;

import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.dao.entity.TmsRouteRecordEntity;
import com.nsy.api.tms.enumeration.PackageStatusEnum;
import com.nsy.api.tms.logistics.sf.SfexpressService;
import com.nsy.api.tms.logistics.sf.VerifyCodeUtil;
import com.nsy.api.tms.logistics.sf.request.SfRequest;
import com.nsy.api.tms.logistics.sf.request.SfRequestBody;
import com.nsy.api.tms.logistics.sf.request.SfRouteRequest;
import com.nsy.api.tms.logistics.sf.response.SfRoute;
import com.nsy.api.tms.logistics.sf.response.SfTrackResponse;
import com.nsy.api.tms.service.TmsRouteService;
import com.nsy.api.tms.utils.JaxbObjectAndXmlUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;

@Service
public class SfExpressTrackNewService extends SfNewService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SfExpressTrackNewService.class);

    private static final String ROUTE_SERVICE = "RouteService";
    private static final String CN_LANG = "zh-CN";
   
    @Autowired
    TmsRouteService routeService;
    @Autowired
    SfexpressService sfexpressService;

    public void doTrack(TmsPackageEntity packageEntity) {
        // 记录日志
        TmsRequestLogEntity logEntity = requestLogService.recordBaseInfoLog(packageEntity);
        //更新tms_package 表路由查询次数
        packageEntity.setRouteSearchTimes(packageEntity.getRouteSearchTimes() + 1);
        packageService.save(packageEntity);
        Map<String, String> configMap = buildTrackConfig(packageEntity);
        SfRequest request = buildRouteRequest(packageEntity, configMap);
        String requestXml = JaxbObjectAndXmlUtil.object2XmlNoHeader(request);
        String verifyCode = VerifyCodeUtil.md5EncryptAndBase64(requestXml, configMap.get(CONFIG_CHECK_CODE));
        try {
            String responseXmlStr = sfexpressService.sfexpressService(requestXml, verifyCode);
            LOGGER.debug("requestXml: {}, verifyCode:{}, responseXml;{}", requestXml, verifyCode, responseXmlStr);
            SfTrackResponse response = JaxbObjectAndXmlUtil.xml2Object(responseXmlStr, SfTrackResponse.class);
            if (null != response && "OK".equals(response.getHead())) {
                processSuccessRouteReply(request, responseXmlStr, packageEntity, logEntity);
            } else {
                processFailRouteReply(logEntity, requestXml, responseXmlStr, packageEntity.getLogisticsNo());
            }
        } catch (Exception e) {
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, requestXml, e.getMessage(), packageEntity.getLogisticsNo());
            throw new RuntimeException(e);
        }
    }

    private void processSuccessRouteReply(SfRequest request, String responseStr, TmsPackageEntity packageEntity, TmsRequestLogEntity logEntity) {
        // 1. 更新日志
        requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, JSONUtils.toJSON(request), responseStr, packageEntity.getLogisticsNo());
        SfTrackResponse response = JaxbObjectAndXmlUtil.xml2Object(responseStr, SfTrackResponse.class);
        if (null == response || response.getBody().getRouteResponseList() == null) {
            LOGGER.info("logisticsNo:{}, 暂无路由信息", packageEntity.getLogisticsNo());
            return;
        }
        List<SfRoute> routeList = response.getBody().getRouteResponseList().get(0).getRoute();
        SfRoute lastRoute = routeList.get(0);
        List<TmsRouteRecordEntity> routeRecordEntityList = routeService.findByLogisticsNoOrderByCreateDateDesc(packageEntity.getLogisticsNo());
        // 更新路由
        if (routeRecordEntityList.isEmpty()
                || !(routeRecordEntityList.get(0).getAcceptTime().compareTo(DateUtils.parse(lastRoute.getAcceptTime(), DateUtils.DATE_FORMAT_DATE4)) == 0 && routeRecordEntityList.get(0).getAcceptAddress().equalsIgnoreCase(lastRoute.
                getAcceptAddress()) && routeRecordEntityList.get(0).getRemark().equalsIgnoreCase(lastRoute.getRemark()))) {
            persistTmsRouteRecord(packageEntity.getLogisticsNo(), lastRoute, packageEntity.getStatus());
        }

        if (routeList.size() == 1) {
            packageEntity.setStatus(PackageStatusEnum.SHIPPED.getDesc());
        } else {
            if (lastRoute.getRemark().contains("电子信息已收到") || lastRoute.getRemark().contains("提交预报成功")) {
                packageEntity.setStatus(PackageStatusEnum.SHIPPED.getDesc());
            } else if (lastRoute.getRemark().contains("已收取快件")) {
                packageEntity.setStatus(PackageStatusEnum.TAKEN.getDesc());
            } else if (lastRoute.getRemark().contains("货件已成功派送") || lastRoute.getRemark().contains("货物已经派送")
                    || lastRoute.getRemark().contains("货物已派送") || lastRoute.getRemark().contains("收货人已签收")
                    || lastRoute.getRemark().contains("快件已派送")) {
                packageEntity.setArrivalTime(DateUtils.parse(lastRoute.getAcceptTime(), DateUtils.DATE_FORMAT_DATE4));
                packageEntity.setStatus(PackageStatusEnum.DELIVERED.getDesc());
            } else if (lastRoute.getRemark().contains("派件异常") || lastRoute.getRemark().contains("派送失败")) {
                packageEntity.setStatus(PackageStatusEnum.TRANSIT.getDesc());
//                generateAlertTask(packageEntity);
            } else {
                packageEntity.setStatus(PackageStatusEnum.TRANSIT.getDesc());
            }
            for (int i = routeList.size() - 1; i >= 0; i--) {
                if (routeList.get(i).getRemark().contains("已收取快件") || routeList.get(i).getRemark().contains("收件") || routeList.get(i).getRemark().contains("货物已到达顺丰转运中心")) {
                    packageEntity.setReceiveTime(DateUtils.parse(routeList.get(i).getAcceptTime(), DateUtils.DATE_FORMAT_DATE4));
                    break;
                }
            }
        }
        packageEntity.setRouteLastUpdateTime(DateUtils.parse(lastRoute.getAcceptTime(), DateUtils.DATE_FORMAT_DATE4));
        packageService.save(packageEntity);
    }

    private void processFailRouteReply(TmsRequestLogEntity logEntity, String requestStr, String responseStr, String logisticsNo) {
        requestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, requestStr, responseStr, logisticsNo);
    }

    private void persistTmsRouteRecord(String logisticsNo, SfRoute route, String status) {
        TmsRouteRecordEntity routeRecordEntity = new TmsRouteRecordEntity();
        routeRecordEntity.setStatus(status);
        routeRecordEntity.setAcceptTime(DateUtils.parse(route.getAcceptTime(), DateUtils.DATE_FORMAT_DATE4));
        routeRecordEntity.setAcceptAddress(route.getAcceptAddress());
        routeRecordEntity.setLogisticsNo(logisticsNo);
        routeRecordEntity.setRemark(route.getRemark());
        routeService.save(routeRecordEntity);
    }

    private Map<String, String> buildTrackConfig(TmsPackageEntity packageEntity) {
//        String keyGroup = channelConfigEntity.getKeyGroup();
//        if ("stage".equalsIgnoreCase(env.getActiveProfiles()[0])) {
//            keyGroup = keyGroup + "_Track";
//        }
        return logisticsAccountService.buildAccountConfig(packageEntity.getLogisticsAccountId());
    }

    private SfRequestBody addBody(TmsPackageEntity packageEntity) {
        SfRequestBody body = new SfRequestBody();
        body.setRoute(addRoute(packageEntity));
        return body;
    }

    private SfRouteRequest addRoute(TmsPackageEntity packageEntity) {
        SfRouteRequest routeRequest = new SfRouteRequest();
        routeRequest.setMethodType(1);
        routeRequest.setTrackingType(1);
        routeRequest.setTrackingNumber(packageEntity.getLogisticsNo());
        return routeRequest;
    }

    private SfRequest buildRouteRequest(TmsPackageEntity packageEntity, Map<String, String> configMap) {
        SfRequest request = new SfRequest();
        request.setService(ROUTE_SERVICE);
        request.setLang(CN_LANG);
        request.setHead(configMap.get(CONFIG_CLIENT_CODE));
        request.setBody(addBody(packageEntity));
        return request;
    }
}
