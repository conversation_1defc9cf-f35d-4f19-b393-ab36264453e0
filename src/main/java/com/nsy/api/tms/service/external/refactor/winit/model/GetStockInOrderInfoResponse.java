package com.nsy.api.tms.service.external.refactor.winit.model;

import java.util.Date;
import java.util.List;

/**
 * HXD
 * 2024/7/17
 **/

public class GetStockInOrderInfoResponse {
    private String orderType;
    private String sellerOrderNo;
    private String destinationWarehouseCode;
    private String customsDeclarationName;
    private String expressNo;
    private String winitProductName;
    private int totalItemQty;
    private String importDeclarationRuleName;
    private int totalMerchandiseQty;
    private String needReservationSendWh;
    private String expressVendorName;
    private String inspectionWarehouseCode;
    private String importerName;
    private String importDeclarationType;
    private String exporterCode;
    private String isCompleted;
    private String orderNo;
    private String importDeclareValueType;
    private int totalPackageQty;
    private String inspectionWarehouseName;
    private String inspectionType;
    private String planShelfCompletedDate;
    private String logisticsPlanName;
    private String destinationWarehouseName;
    private String expressVendorCode;
    private String importDeclarationRuleCode;
    private String importDeclareWay;
    private String pickupType;
    private List<MerchandiseList> merchandiseList;
    private String importDeclarationName;
    private String pickupAddressCode;
    private Date createdDate;
    private String logisticsPlanNo;
    private String pickupAddress;
    private String exporterName;
    private String winitProductCode;
    private String shelveCompletedDate;
    private String customsDeclarationType;
    private String importerCode;
    private String status;

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setSellerOrderNo(String sellerOrderNo) {
        this.sellerOrderNo = sellerOrderNo;
    }

    public String getSellerOrderNo() {
        return sellerOrderNo;
    }

    public void setDestinationWarehouseCode(String destinationWarehouseCode) {
        this.destinationWarehouseCode = destinationWarehouseCode;
    }

    public String getDestinationWarehouseCode() {
        return destinationWarehouseCode;
    }

    public void setCustomsDeclarationName(String customsDeclarationName) {
        this.customsDeclarationName = customsDeclarationName;
    }

    public String getCustomsDeclarationName() {
        return customsDeclarationName;
    }

    public void setExpressNo(String expressNo) {
        this.expressNo = expressNo;
    }

    public String getExpressNo() {
        return expressNo;
    }

    public void setWinitProductName(String winitProductName) {
        this.winitProductName = winitProductName;
    }

    public String getWinitProductName() {
        return winitProductName;
    }

    public void setTotalItemQty(int totalItemQty) {
        this.totalItemQty = totalItemQty;
    }

    public int getTotalItemQty() {
        return totalItemQty;
    }

    public void setImportDeclarationRuleName(String importDeclarationRuleName) {
        this.importDeclarationRuleName = importDeclarationRuleName;
    }

    public String getImportDeclarationRuleName() {
        return importDeclarationRuleName;
    }

    public void setTotalMerchandiseQty(int totalMerchandiseQty) {
        this.totalMerchandiseQty = totalMerchandiseQty;
    }

    public int getTotalMerchandiseQty() {
        return totalMerchandiseQty;
    }

    public void setNeedReservationSendWh(String needReservationSendWh) {
        this.needReservationSendWh = needReservationSendWh;
    }

    public String getNeedReservationSendWh() {
        return needReservationSendWh;
    }

    public void setExpressVendorName(String expressVendorName) {
        this.expressVendorName = expressVendorName;
    }

    public String getExpressVendorName() {
        return expressVendorName;
    }

    public void setInspectionWarehouseCode(String inspectionWarehouseCode) {
        this.inspectionWarehouseCode = inspectionWarehouseCode;
    }

    public String getInspectionWarehouseCode() {
        return inspectionWarehouseCode;
    }

    public void setImporterName(String importerName) {
        this.importerName = importerName;
    }

    public String getImporterName() {
        return importerName;
    }

    public void setImportDeclarationType(String importDeclarationType) {
        this.importDeclarationType = importDeclarationType;
    }

    public String getImportDeclarationType() {
        return importDeclarationType;
    }

    public void setExporterCode(String exporterCode) {
        this.exporterCode = exporterCode;
    }

    public String getExporterCode() {
        return exporterCode;
    }

    public void setIsCompleted(String isCompleted) {
        this.isCompleted = isCompleted;
    }

    public String getIsCompleted() {
        return isCompleted;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setImportDeclareValueType(String importDeclareValueType) {
        this.importDeclareValueType = importDeclareValueType;
    }

    public String getImportDeclareValueType() {
        return importDeclareValueType;
    }

    public void setTotalPackageQty(int totalPackageQty) {
        this.totalPackageQty = totalPackageQty;
    }

    public int getTotalPackageQty() {
        return totalPackageQty;
    }

    public void setInspectionWarehouseName(String inspectionWarehouseName) {
        this.inspectionWarehouseName = inspectionWarehouseName;
    }

    public String getInspectionWarehouseName() {
        return inspectionWarehouseName;
    }

    public void setInspectionType(String inspectionType) {
        this.inspectionType = inspectionType;
    }

    public String getInspectionType() {
        return inspectionType;
    }

    public void setPlanShelfCompletedDate(String planShelfCompletedDate) {
        this.planShelfCompletedDate = planShelfCompletedDate;
    }

    public String getPlanShelfCompletedDate() {
        return planShelfCompletedDate;
    }

    public void setLogisticsPlanName(String logisticsPlanName) {
        this.logisticsPlanName = logisticsPlanName;
    }

    public String getLogisticsPlanName() {
        return logisticsPlanName;
    }

    public void setDestinationWarehouseName(String destinationWarehouseName) {
        this.destinationWarehouseName = destinationWarehouseName;
    }

    public String getDestinationWarehouseName() {
        return destinationWarehouseName;
    }

    public void setExpressVendorCode(String expressVendorCode) {
        this.expressVendorCode = expressVendorCode;
    }

    public String getExpressVendorCode() {
        return expressVendorCode;
    }

    public void setImportDeclarationRuleCode(String importDeclarationRuleCode) {
        this.importDeclarationRuleCode = importDeclarationRuleCode;
    }

    public String getImportDeclarationRuleCode() {
        return importDeclarationRuleCode;
    }

    public void setImportDeclareWay(String importDeclareWay) {
        this.importDeclareWay = importDeclareWay;
    }

    public String getImportDeclareWay() {
        return importDeclareWay;
    }

    public void setPickupType(String pickupType) {
        this.pickupType = pickupType;
    }

    public String getPickupType() {
        return pickupType;
    }

    public void setMerchandiseList(List<MerchandiseList> merchandiseList) {
        this.merchandiseList = merchandiseList;
    }

    public List<MerchandiseList> getMerchandiseList() {
        return merchandiseList;
    }

    public void setImportDeclarationName(String importDeclarationName) {
        this.importDeclarationName = importDeclarationName;
    }

    public String getImportDeclarationName() {
        return importDeclarationName;
    }

    public void setPickupAddressCode(String pickupAddressCode) {
        this.pickupAddressCode = pickupAddressCode;
    }

    public String getPickupAddressCode() {
        return pickupAddressCode;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setLogisticsPlanNo(String logisticsPlanNo) {
        this.logisticsPlanNo = logisticsPlanNo;
    }

    public String getLogisticsPlanNo() {
        return logisticsPlanNo;
    }

    public void setPickupAddress(String pickupAddress) {
        this.pickupAddress = pickupAddress;
    }

    public String getPickupAddress() {
        return pickupAddress;
    }

    public void setExporterName(String exporterName) {
        this.exporterName = exporterName;
    }

    public String getExporterName() {
        return exporterName;
    }

    public void setWinitProductCode(String winitProductCode) {
        this.winitProductCode = winitProductCode;
    }

    public String getWinitProductCode() {
        return winitProductCode;
    }

    public void setShelveCompletedDate(String shelveCompletedDate) {
        this.shelveCompletedDate = shelveCompletedDate;
    }

    public String getShelveCompletedDate() {
        return shelveCompletedDate;
    }

    public void setCustomsDeclarationType(String customsDeclarationType) {
        this.customsDeclarationType = customsDeclarationType;
    }

    public String getCustomsDeclarationType() {
        return customsDeclarationType;
    }

    public void setImporterCode(String importerCode) {
        this.importerCode = importerCode;
    }

    public String getImporterCode() {
        return importerCode;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
}
