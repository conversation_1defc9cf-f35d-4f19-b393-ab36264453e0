package com.nsy.api.tms.service.external;

import com.nsy.api.core.apicore.exception.InternalServiceException;
import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.dao.entity.TmsThirdPartTokenEntity;
import com.nsy.api.tms.domain.ExportDeclarationItemInfo;
import com.nsy.api.tms.domain.OrderInfo;
import com.nsy.api.tms.logistics.dhl.dps.BaseDPSDetailResponse;
import com.nsy.api.tms.logistics.dhl.dps.BaseDPSResponse;
import com.nsy.api.tms.logistics.dhl.dps.DecHead;
import com.nsy.api.tms.logistics.dhl.dps.DecList;
import com.nsy.api.tms.logistics.dhl.dps.DecLists;
import com.nsy.api.tms.logistics.dhl.dps.DecMessage;
import com.nsy.api.tms.logistics.dhl.dps.MsgHead;
import com.nsy.api.tms.logistics.dhl.dps.SubmitDeclareRequest;
import com.nsy.api.tms.logistics.dhl.dps.TokenRequest;
import com.nsy.api.tms.repository.TmsThirdPartTokenRepository;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.service.TmsRequestLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * DHL上传单证
 * HXD
 * 2021/6/22
 **/
@Service
public class DhlDpsService {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${dhl.dps.server.url}")
    private String dpsServerUrl;

    public static final String PLATFORM = "DHL-DPS";

    @Autowired
    private TmsRequestLogService tmsRequestLogService;
    @Autowired
    private TmsThirdPartTokenRepository repository;

    // 以下为请求路径
    public static final String GET_TOKEN = "/dps-auth-server/api/gateway-token/generate";
    public static final String UPLOAD_FILE = "/dps-declaration-api/api/declare/upload-file";
    public static final String SUBMIT_DECLARE = "/dps-declaration-api/api/declare/submit-standard-declare?version=2301";

    private static final Logger LOGGER = LoggerFactory.getLogger(DhlDpsService.class);

    public GenerateOrderResponse submitDeclare(OrderInfo orderInfo, String logisticNo, Map<String, String> configMap) {
        SubmitDeclareRequest declareRequest = new SubmitDeclareRequest();
        DecMessage decMessage = new DecMessage();
        DecHead decHead = new DecHead();
        DecLists decLists = new DecLists();
        List<DecList> decList = new ArrayList<>();
        MsgHead msgHead = new MsgHead();
        declareRequest.setDecMessage(decMessage);
        decMessage.setDecHead(decHead);
        decMessage.setDecLists(decLists);
        decMessage.setMsgHead(msgHead);
        decLists.setDecList(decList);
        buildDecHead(decHead, configMap);
        int i = 1;
        for (ExportDeclarationItemInfo item : orderInfo.getExportDeclarationItemList()) {
            // 构建申报信息
            decList.add(buildDecList(i, item));
            i++;
        }
        buildMsgHead(msgHead, logisticNo);
        TmsRequestLogEntity entity = tmsRequestLogService.recordOtherInfoLog("DHL-DPS", orderInfo.getLogisticsChannelCode(), orderInfo.getTid(), orderInfo.getKeyGroup(), "submit-declare");
        String result = httpDPS(entity, JSONUtils.toJSON(declareRequest), configMap);
        if (StringUtils.hasText(result) && result.contains("接口失败")) {
            GenerateOrderResponse response = new GenerateOrderResponse();
            response.setError(buildErrorResp(result));
            return response;
        }
        return null;
    }

    private GenerateOrderResponse.Error buildErrorResp(String result) {
        GenerateOrderResponse.Error error = new GenerateOrderResponse.Error();
        error.setCode(HttpStatus.BAD_REQUEST.name());
        error.setMessage(result);
        return error;
    }

    private DecList buildDecList(int i, ExportDeclarationItemInfo item) {
        DecList dec = new DecList();
        dec.setgNo(i);
        dec.setDeclGoodsEname(item.getEnName());
        dec.setGName(item.getCnName());
        dec.setCodeTs(item.getCommodityCode());
        dec.setGModel(item.getgModel());
        dec.setgQty(new BigDecimal(item.getQuantity()).setScale(4, BigDecimal.ROUND_DOWN));
        dec.setGUnit(item.getgUnit());
        dec.setDeclPrice(item.getUnitPrice());
        dec.setDeclTotal(Double.valueOf(item.getDeclTotal()));
        dec.setTradeCurr(item.getTradeCurr());
        dec.setFirstQty(new BigDecimal(item.getDeclareValueByFirstUnit()).setScale(4, BigDecimal.ROUND_DOWN));
        dec.setFirstUnit(item.getDeclareUnitByFirst());
        dec.setSecondUnit(StringUtils.hasText(item.getDeclareUnitBySecond()) ? item.getDeclareUnitBySecond() : null);
        //        dec.setSecondQty(StringUtils.hasText(item.getDeclareValueBySecondUnit()) ? item.getDeclareValueBySecondUnit() : null);
        // 重新设置两位小数
        dec.setSecondQty(StringUtils.hasText(item.getDeclareValueBySecondUnit()) ? String.valueOf(new BigDecimal(item.getDeclareValueBySecondUnit())
                .setScale(4, BigDecimal.ROUND_DOWN)) : null);
        dec.setGoodsBrand("无");
        dec.setDutyMode("1");
        dec.setOriginCountry("CHN");
        return dec;
    }

    private void buildMsgHead(MsgHead msgHead, String logisticNo) {
        msgHead.setMsgId(UUID.randomUUID().toString());
        msgHead.setMsgGenDtm(DateUtils.format(new Date(), DateUtils.DATE_FORMAT_DATE4));
        msgHead.setVersion("1.0");
        msgHead.setAction("A");
        msgHead.setAwbGenDtm(DateUtils.format(new Date(), DateUtils.DATE_FORMAT_DATE4));
        msgHead.setAwbNo(logisticNo);
        msgHead.setDeclareType("C");
    }

    private void buildDecHead(DecHead decHead, Map<String, String> configMap) {
        decHead.setIEFlag("E");
        if (Objects.equals(configMap.get("dhl_siteId"), "xiamen")) {
            decHead.setTradeName("厦门金多厦网络科技有限公司");
            decHead.setTradeCoScc("913502065878881595");
            decHead.setTradeCode("3502161BA2");
        } else {
            decHead.setTradeName("泉州时颖服饰有限公司");
            decHead.setTradeCoScc("91350503611608429U");
            decHead.setTradeCode("3505961258");
        }
        decHead.setTradeMode("0110");
        decHead.setTransMode("3");
        decHead.setCutMode("101");
        decHead.setOwnerName(decHead.getTradeName());
        decHead.setOwnerCode(decHead.getTradeCode());
        decHead.setOwnerCodeScc(decHead.getTradeCoScc());
    }

    /**
     * 上传随附单证文件(目前暂时不用，发票已存在对方系统)
     * <AUTHOR>
     * 2021-06-23
     */
    /*public void uploadDPSFile(String logisticsNo, byte[] file) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.set("Authorization", getToken());
        headers.set("x-request-id", UUID.randomUUID().toString());
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("awbNo", logisticsNo);
        params.add("file", file);
        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<MultiValueMap<String, Object>>(params, headers);
        TmsRequestLogEntity tmsRequestLogEntity = tmsRequestLogService.recordOtherInfoLog("DHL-DPS", null, null, null, "upload-file");
        tmsRequestLogEntity.setLogisticsNo(logisticsNo);
        httpDPS(tmsRequestLogEntity, JSONUtils.toJSON(httpEntity), UPLOAD_FILE, httpEntity);
    }*/

    /**
     * 获取token
     *
     * @param configMap
     * <AUTHOR>
     * 2021-06-22
     */
    private String getToken(Map<String, String> configMap) {
        List<TmsThirdPartTokenEntity> cs = repository.findByPlatform(PLATFORM);
        if (CollectionUtils.isEmpty(cs)) {
            return refreshToken(new TmsThirdPartTokenEntity(), configMap);
        }
        TmsThirdPartTokenEntity tmsThirdPartTokenEntity = cs.get(0);
        if (tmsThirdPartTokenEntity.getExpireDate().getTime() <= new Date().getTime()) {
            return refreshToken(tmsThirdPartTokenEntity, configMap);
        } else {
            return tmsThirdPartTokenEntity.getToken();
        }
    }

    /**
     * 获取服务器token 并保存
     *
     * <AUTHOR>
     * 2021-06-15
     */
    public String refreshToken(TmsThirdPartTokenEntity entity, Map<String, String> configMap) {
        TokenRequest request = new TokenRequest();
        request.setConsumerCode(configMap.get("dps_consumerCode"));
        request.setKey(configMap.get("dps_key"));
        TmsRequestLogEntity tmsRequestLogEntity = tmsRequestLogService.recordOtherInfoLog("DHL-DPS", null, null, null, "get-token");
        String token = httpDPSForToken(tmsRequestLogEntity, JSONUtils.toJSON(request));
        if (entity.getId() == null) {
            entity.setCreateDate(new Date());
            entity.setPlatform(PLATFORM);
            entity.setStartDate(new Date());
        }
        entity.setToken(token);
        entity.setExpireDate(DateUtils.add(new Date(), Calendar.MINUTE, 60));
        entity.setUpdateDate(new Date());
        repository.save(entity);
        return token;
    }

    protected String httpDPSForToken(TmsRequestLogEntity logEntity, String requestInfo) {
        ResponseEntity<String> responseEntity = null;
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
        httpHeaders.set("x-request-id", UUID.randomUUID().toString());
        HttpEntity<String> entity = new HttpEntity<>(requestInfo, httpHeaders);
        String msg = "";
        try {
            responseEntity = restTemplate.postForEntity(dpsServerUrl + GET_TOKEN, entity, String.class);
            BaseDPSResponse baseDPSResponse = JSONUtils.fromJSON(responseEntity.getBody(), BaseDPSResponse.class);
            if (responseEntity.getStatusCode() != HttpStatus.OK || baseDPSResponse.getStatus() == null || baseDPSResponse.getStatus() != 0) {
                msg = "DHL上传单证的" + GET_TOKEN + "接口失败, 错误信息:" + baseDPSResponse.getMessage();
                throw new InternalServiceException(msg);
            }
            tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, requestInfo, responseEntity.getBody(), null);
            return baseDPSResponse.getResult().getToken();
        } catch (Exception e) {
            tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, requestInfo, responseEntity == null ? e.getMessage() : JSONUtils.toJSON(responseEntity.getBody()), null);
            LOGGER.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 请求DPS接口
     *
     * <AUTHOR>
     * 2021-06-15
     */
    protected String httpDPS(TmsRequestLogEntity logEntity, String requestInfo, Map<String, String> configMap) {
        ResponseEntity<String> responseEntity = null;
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
        httpHeaders.set("x-request-id", UUID.randomUUID().toString());
        httpHeaders.set("Authorization", getToken(configMap));
        HttpEntity<String> entity = new HttpEntity<>(requestInfo, httpHeaders);
        String msg = "";
        try {
            responseEntity = restTemplate.postForEntity(dpsServerUrl + SUBMIT_DECLARE, entity, String.class);
            BaseDPSDetailResponse baseDPSResponse = JSONUtils.fromJSON(responseEntity.getBody(), BaseDPSDetailResponse.class);
            if (responseEntity.getStatusCode() != HttpStatus.OK || baseDPSResponse.getStatus() == null || baseDPSResponse.getStatus() != 0) {
                msg = "DHL上传单证的" + SUBMIT_DECLARE + "接口失败, 错误信息:" + baseDPSResponse.getMessage();
                tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, requestInfo, responseEntity.getBody(), null);
                return msg;
            }
            tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, requestInfo, responseEntity.getBody(), null);
            return "success";
        } catch (Exception e) {
            tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, requestInfo, responseEntity == null ? e.getMessage() : JSONUtils.toJSON(responseEntity.getBody()), null);
            LOGGER.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }
}
