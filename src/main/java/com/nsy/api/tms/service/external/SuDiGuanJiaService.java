package com.nsy.api.tms.service.external;

import com.nsy.api.tms.annotation.TmsHandler;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@TmsHandler(logisticsMethod = "速递管家")
public class SuDiGuanJiaService extends TomsService implements InitializingBean {


    @Value("${sudiguanjia.server.url}")
    private String suDiGuanJiaServiceUrl;


    @Override
    public void afterPropertiesSet() throws Exception {
        this.serviceUrl = suDiGuanJiaServiceUrl;
        this.labelNameTemplate = "%sXinYuLong-%s";
    }

}
