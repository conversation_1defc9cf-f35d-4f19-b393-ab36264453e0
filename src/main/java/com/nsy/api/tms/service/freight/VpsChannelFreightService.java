package com.nsy.api.tms.service.freight;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.exception.InvalidRequestException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.constants.CountryCodeConstant;
import com.nsy.api.tms.constants.PackageBillConstant;
import com.nsy.api.tms.constants.TmsCommonConstant;
import com.nsy.api.tms.dao.entity.TmsLogisticsAccountEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsChannelConfigEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsCompanyEntity;
import com.nsy.api.tms.dao.entity.freight.VpsChannelCountryFreightEntity;
import com.nsy.api.tms.dao.entity.freight.VpsChannelFreightEntity;
import com.nsy.api.tms.domain.CompanyAndChannelInfo;
import com.nsy.api.tms.domain.SelectModel;
import com.nsy.api.tms.domain.TmsLogisticsChannelConfig;
import com.nsy.api.tms.domain.TmsLogisticsCompany;
import com.nsy.api.tms.domain.VpsChannelCountryDetailInfo;
import com.nsy.api.tms.enumeration.BillingTypeChannelEnum;
import com.nsy.api.tms.enumeration.BillingTypeEnum;
import com.nsy.api.tms.enumeration.ChannelFreightStatusEnum;
import com.nsy.api.tms.enumeration.LogisticsTypeEnum;
import com.nsy.api.tms.external.user.UserDictionaryApiService;
import com.nsy.api.tms.filter.TenantContext;
import com.nsy.api.tms.mapper.freight.VpsChannelFreightMapper;
import com.nsy.api.tms.repository.TmsLogisticsAccountRepository;
import com.nsy.api.tms.repository.TmsLogisticsChannelConfigRepository;
import com.nsy.api.tms.repository.TmsLogisticsCompanyRepository;
import com.nsy.api.tms.request.StatusRequest;
import com.nsy.api.tms.request.freight.VpsChannelFreightAuditRequest;
import com.nsy.api.tms.request.freight.VpsChannelFreightCopyRequest;
import com.nsy.api.tms.request.freight.VpsChannelFreightInsertRequest;
import com.nsy.api.tms.request.freight.VpsChannelFreightPageRequest;
import com.nsy.api.tms.response.PageResponse;
import com.nsy.api.tms.response.freight.StatusCountResponse;
import com.nsy.api.tms.response.freight.VpsChannelCountryFreightResponse;
import com.nsy.api.tms.response.freight.VpsChannelFreightDetailResponse;
import com.nsy.api.tms.response.freight.VpsChannelFreightResponse;
import com.nsy.api.tms.service.privilege.AccessControlService;
import com.nsy.api.tms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 物流渠道价格表(VpsChannelFreight)服务层
 *
 * <AUTHOR>
 * @since 2023-09-04 17:05:25
 */
@Service
public class VpsChannelFreightService extends ServiceImpl<VpsChannelFreightMapper, VpsChannelFreightEntity> {

    private static final Logger LOGGER = LoggerFactory.getLogger(VpsChannelFreightService.class);


    @Autowired
    UserDictionaryApiService dictionaryApiService;
    @Resource
    AccessControlService accessControlService;
    @Autowired
    VpsChannelFreightLogService channelFreightLogService;
    @Autowired
    TmsLogisticsChannelConfigRepository channelConfigRepository;
    @Autowired
    TmsLogisticsCompanyRepository companyRepository;
    @Autowired
    VpsChannelCountryFreightService channelCountryFreightService;
    @Autowired
    TmsLogisticsAccountRepository accountRepository;


    public List<StatusCountResponse> statusTabCount() {
        Integer companyId = accessControlService.getCompanyId();
        Map<String, List<StatusCountResponse>> collect;
        if (companyId != null) {
            collect = this.getBaseMapper().countByStatusAll(companyId).stream().collect(Collectors.groupingBy(StatusCountResponse::getStatus));
        } else {
            String tenant = TenantContext.getTenant();
            collect = this.getBaseMapper().countByStatus(tenant).stream().collect(Collectors.groupingBy(StatusCountResponse::getStatus));
        }
        List<StatusCountResponse> list = Arrays.stream(ChannelFreightStatusEnum.values()).map(statusEnum -> {
            StatusCountResponse response = new StatusCountResponse();
            List<StatusCountResponse> responses = collect.get(statusEnum.name());
            response.setQty(CollectionUtils.isEmpty(responses) ? Integer.valueOf(0) : responses.get(0).getQty());
            response.setStatus(statusEnum.name());
            response.setValue(statusEnum.name());
            response.setLabel(statusEnum.getDesc());
            return response;
        }).collect(Collectors.toList());
        StatusCountResponse response = new StatusCountResponse();
        response.setQty(list.stream().mapToInt(StatusCountResponse::getQty).sum());
        response.setStatus("ALL");
        response.setValue("ALL");
        response.setLabel("所有");
        list.add(response);
        return list;
    }

    /**
    * 分页查询
    */
    public PageResponse<VpsChannelFreightResponse> queryByPage(VpsChannelFreightPageRequest request) {
        Integer companyId = accessControlService.getCompanyId();
        if (companyId != null) {
            request.setCompanyId(companyId);
        }
        PageResponse<VpsChannelFreightResponse> pageResponse = new PageResponse<>();
        Page<VpsChannelFreightEntity> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<VpsChannelFreightEntity> iPage = page(page, buildPageQueryWrapper(request));
        List<VpsChannelFreightResponse> list = iPage.getRecords().stream().map(entity -> {
            CompanyAndChannelInfo companyAndChannelInfo = getCompanyAndChannelInfo(entity.getChannelId());
            VpsChannelFreightResponse resp = new VpsChannelFreightResponse();
            BeanUtils.copyProperties(entity, resp);
            resp.setStatusCn(ChannelFreightStatusEnum.getDescByName(entity.getStatus()));
            resp.setBillingTypeCn(BillingTypeChannelEnum.getDescByName(entity.getBillingType()));
            resp.setLogisticsCompany(companyAndChannelInfo.getLogisticsCompany().getLogisticsCompany());
            resp.setLogisticsChannelName(companyAndChannelInfo.getLogisticsChannelConfig().getLogisticsChannelName());
            resp.setLogisticsType(companyAndChannelInfo.getLogisticsCompany().getLogisticsType());
            resp.setLogisticsTypeCn(LogisticsTypeEnum.getDescByName(resp.getLogisticsType()));
            return resp;
        }).collect(Collectors.toList());
        pageResponse.setContent(list);
        pageResponse.setTotalCount(iPage.getTotal());
        return pageResponse;
    }
    
    public VpsChannelFreightDetailResponse getOneById(Integer id) {
        VpsChannelFreightEntity entity = getById(id);
        VpsChannelFreightDetailResponse resp = new VpsChannelFreightDetailResponse();
        BeanUtils.copyProperties(entity, resp);
        resp.setStatusCn(ChannelFreightStatusEnum.getDescByName(entity.getStatus()));
        resp.setBillingTypeCn(BillingTypeChannelEnum.getDescByName(entity.getBillingType()));
        List<VpsChannelCountryFreightEntity> list = channelCountryFreightService.getByChannelFreightIdOrderByChannelFreightId(id);
        Map<String, List<VpsChannelCountryFreightResponse>> collect = list.stream().map(item ->
                channelCountryFreightService.convertResponse(item)).collect(Collectors.groupingBy(VpsChannelCountryFreightResponse::getCountryCode));
        List<VpsChannelCountryDetailInfo> countryFreightList = new ArrayList<>();
        List<String> countryList = new ArrayList<>();
        collect.forEach((k, v) -> {
            // 每个国家再根据区域分组
            v.forEach(i -> {
                if (StrUtil.isBlank(i.getAreaName())) {
                    i.setAreaName("");
                }
            });
            Map<String, List<VpsChannelCountryFreightResponse>> areaMap = v.stream().collect(Collectors.groupingBy(VpsChannelCountryFreightResponse::getAreaName));
            areaMap.forEach((ik, iv) -> {
                VpsChannelCountryDetailInfo channelCountryDetailInfo = new VpsChannelCountryDetailInfo();
                channelCountryDetailInfo.setCountryCode(k);
                channelCountryDetailInfo.setCountryName(CountryCodeConstant.getCountryCodeMap().get(k));
                channelCountryDetailInfo.setFreightList(iv);
                channelCountryDetailInfo.setDiscountFactor(iv.get(0).getDiscountFactor());
                channelCountryDetailInfo.setOtherFreightMarkList(iv.get(0).getOtherFreightMark() == null ? null : Arrays.asList(iv.get(0).getOtherFreightMark().split(",")));
                channelCountryDetailInfo.setStartDate(iv.get(0).getStartDate());
                channelCountryDetailInfo.setEndDate(iv.get(0).getEndDate());
                channelCountryDetailInfo.setRemark(iv.get(0).getRemark());
                channelCountryDetailInfo.setStartPostCode(iv.get(0).getStartPostCode());
                channelCountryDetailInfo.setAreaName(StrUtil.isBlank(ik) ? null : ik);
                iv.forEach(i -> {
                    if (StrUtil.isBlank(i.getAreaName())) {
                        i.setAreaName(null);
                    }
                });
                countryFreightList.add(channelCountryDetailInfo);
                countryList.add(k);
            });
        });
        resp.setCountryFreightList(countryFreightList);
        resp.setCountryList(countryList);
        CompanyAndChannelInfo companyAndChannelInfo = getCompanyAndChannelInfo(entity.getChannelId());
        resp.setCompanyAndChannelInfo(companyAndChannelInfo);
        return resp;
    }
    
    // 新增
    @Transactional(rollbackFor = Exception.class)
    public void saveOrEdit(VpsChannelFreightInsertRequest insertRequest) {
        Integer companyId = accessControlService.getCompanyId();
        CompanyAndChannelInfo companyAndChannelInfo = getCompanyAndChannelInfo(insertRequest.getChannelId());
        // 校验参数
        validInsertParams(insertRequest, companyAndChannelInfo.getLogisticsCompany());
        VpsChannelFreightEntity entity = new VpsChannelFreightEntity();
        VpsChannelFreightEntity oldEntity = null;
        if (insertRequest.getId() != null) {
            oldEntity = getById(insertRequest.getId());
            if (companyId != null && !ChannelFreightStatusEnum.REJECT.name().equals(oldEntity.getStatus()) && !ChannelFreightStatusEnum.INIT.name().equals(oldEntity.getStatus())) {
                throw new BusinessServiceException("只有驳回、待审状态下才能修改");
            }
            BeanUtilsEx.copyProperties(oldEntity, entity);
        }
        BeanUtilsEx.copyProperties(insertRequest, entity);
        entity.setCreateBy(accessControlService.getUserName());
        if (companyId != null || StrUtil.isBlank(entity.getStatus())) {
            entity.setStatus(ChannelFreightStatusEnum.INIT.name());
        }
        if (entity.getCompanyId() == null)
            entity.setCompanyId(companyAndChannelInfo.getLogisticsCompany().getId());
        // 渠道价格表，删除之前的国家运价
        saveOrUpdateEntity(entity, insertRequest, oldEntity);
        // 渠道国家价格表
        insertRequest.getCountryFreightList().forEach(country -> {
            country.getFreightList().forEach(each -> {
                VpsChannelCountryFreightEntity freightEntity = new VpsChannelCountryFreightEntity();
                BeanUtilsEx.copyProperties(each, freightEntity);
                freightEntity.setAccount(insertRequest.getAccount());
                freightEntity.setCompanyId(entity.getCompanyId());
                freightEntity.setChannelFreightId(entity.getId());
                freightEntity.setChannelId(insertRequest.getChannelId());
                freightEntity.setStartDate(country.getStartDate());
                freightEntity.setEndDate(country.getEndDate());
                freightEntity.setRemark(country.getRemark());
                freightEntity.setOtherFreightMark(CollectionUtils.isEmpty(country.getOtherFreightMarkList()) ? null : String.join(",", country.getOtherFreightMarkList()));
                freightEntity.setAreaName(country.getAreaName());
                freightEntity.setStartPostCode(country.getStartPostCode());
                channelCountryFreightService.save(freightEntity);
            });
        });
    }

    // 新增并且写入日志
    private void saveOrUpdateEntity(VpsChannelFreightEntity entity, VpsChannelFreightInsertRequest insertRequest, VpsChannelFreightEntity oldEntity) {
        saveOrUpdate(entity);
        LambdaUpdateWrapper<VpsChannelFreightEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(VpsChannelFreightEntity::getAccount, entity.getAccount());
        updateWrapper.set(VpsChannelFreightEntity::getDefaultUnitFreight, entity.getDefaultUnitFreight());
        updateWrapper.set(VpsChannelFreightEntity::getDefaultDiscountFactor, entity.getDefaultDiscountFactor());
        updateWrapper.set(VpsChannelFreightEntity::getRemark, entity.getRemark());
        updateWrapper.eq(VpsChannelFreightEntity::getId, entity.getId());
        update(updateWrapper);
        List<VpsChannelCountryFreightEntity> byChannelFreightId = channelCountryFreightService.getByChannelFreightId(entity.getId());
        if (!CollectionUtils.isEmpty(byChannelFreightId)) {
            LOGGER.info("更新物流价格，删除之前的数据，渠道国家价格json:" + JsonMapper.toJson(byChannelFreightId));
            channelCountryFreightService.removeByIds(byChannelFreightId.stream().map(VpsChannelCountryFreightEntity::getId).collect(Collectors.toList()));
        }
        saveOrUpdateLog(entity, insertRequest, oldEntity, byChannelFreightId);
    }

    private void saveOrUpdateLog(VpsChannelFreightEntity entity, VpsChannelFreightInsertRequest insertRequest, VpsChannelFreightEntity oldEntity, List<VpsChannelCountryFreightEntity> byChannelFreightId) {
        if (insertRequest.getId() != null && oldEntity != null) {
            String logContent = updateLogContext(entity, oldEntity);
            channelFreightLogService.addLog(entity.getCompanyId(), entity.getId(), "修改", logContent);
            if (!CollectionUtils.isEmpty(byChannelFreightId)) {
                countryLog(entity, insertRequest, byChannelFreightId);
            }
        } else {
            channelFreightLogService.addLog(entity.getCompanyId(), entity.getId(), "新增", "新增运费");
        }
    }

    private void countryLog(VpsChannelFreightEntity entity, VpsChannelFreightInsertRequest insertRequest, List<VpsChannelCountryFreightEntity> byChannelFreightId) {
        List<String> oldCountryCodeList = byChannelFreightId.stream().map(VpsChannelCountryFreightEntity::getCountryCode).distinct().collect(Collectors.toList());
        List<String> newCountryCodeList = insertRequest.getCountryFreightList().stream().map(VpsChannelCountryDetailInfo::getCountryCode).distinct().collect(Collectors.toList());
        List<String> dto = CollectionUtil.newArrayList(oldCountryCodeList);
        oldCountryCodeList.removeAll(newCountryCodeList);
        if (!CollectionUtils.isEmpty(oldCountryCodeList)) {
            channelFreightLogService.addLog(entity.getCompanyId(), entity.getId(), "修改", "删除国家" + JsonMapper.toJson(oldCountryCodeList));
        }
        newCountryCodeList.removeAll(dto);
        if (!CollectionUtils.isEmpty(newCountryCodeList)) {
            channelFreightLogService.addLog(entity.getCompanyId(), entity.getId(), "修改", "新增国家" + JsonMapper.toJson(newCountryCodeList));
        }
    }

    private String updateLogContext(VpsChannelFreightEntity entity, VpsChannelFreightEntity oldEntity) {
        List<String> changeLog = new ArrayList<>();
        if (!Objects.equals(oldEntity.getChannelId(), entity.getChannelId())) {
            TmsLogisticsChannelConfigEntity newChannel = channelConfigRepository.getOne(entity.getChannelId());
            TmsLogisticsChannelConfigEntity oldChannel = channelConfigRepository.getOne(oldEntity.getChannelId());
            changeLog.add("[渠道]由" + oldChannel.getLogisticsChannelName() + "改为" + newChannel.getLogisticsChannelName());
        }
        if (!Objects.equals(oldEntity.getAccount(), entity.getAccount())) {
            changeLog.add("[账号]由" + StrUtil.toString(StrUtil.isBlank(oldEntity.getAccount()) ? "" : oldEntity.getAccount()) + "改为" + StrUtil.toString(StrUtil.isBlank(entity.getAccount()) ? "" : entity.getAccount()));
        }
        if (!Objects.equals(oldEntity.getBillingType(), entity.getBillingType())) {
            changeLog.add("[计费方式]由" + BillingTypeChannelEnum.getDescByName(oldEntity.getBillingType()) + "改为" + BillingTypeChannelEnum.getDescByName(entity.getBillingType()));
        }
        if (!Objects.equals(oldEntity.getCurrencyCode(), entity.getCurrencyCode())) {
            changeLog.add("[币种]由" + StrUtil.toString(oldEntity.getCurrencyCode()) + "改为" + StrUtil.toString(entity.getCurrencyCode()));
        }
        if (!Objects.equals(oldEntity.getRemark(), entity.getRemark())) {
            changeLog.add("[备注]由" + JsonMapper.toJson(oldEntity.getRemark() == null ? "" : oldEntity.getRemark()) + "改为" + JsonMapper.toJson(entity.getRemark() == null ? "" : entity.getRemark()));
        }
        if (!NumberUtil.equals(oldEntity.getDefaultUnitFreight(), entity.getDefaultUnitFreight())) {
            changeLog.add("[默认单价]由" + StrUtil.toString(oldEntity.getDefaultUnitFreight() == null ? "" : oldEntity.getDefaultUnitFreight()) + "改为" + StrUtil.toString(entity.getDefaultUnitFreight() == null ? "" : entity.getDefaultUnitFreight()));
        }
        if (!NumberUtil.equals(oldEntity.getDefaultDiscountFactor(), entity.getDefaultDiscountFactor())) {
            changeLog.add("[默认折扣系数]由" + StrUtil.toString(oldEntity.getDefaultDiscountFactor() == null ? "" : oldEntity.getDefaultDiscountFactor()) + "改为" + StrUtil.toString(entity.getDefaultDiscountFactor() == null ? "" : entity.getDefaultDiscountFactor()));
        }
        return String.join(",", changeLog);
    }

    public CompanyAndChannelInfo getCompanyAndChannelInfo(Integer channelId) {
        CompanyAndChannelInfo companyAndChannelInfo = new CompanyAndChannelInfo();
        TmsLogisticsChannelConfigEntity channelConfigEntity = channelConfigRepository.findById(channelId).orElseThrow(() -> new BusinessServiceException("找不到物流渠道" + channelId));
        TmsLogisticsCompanyEntity companyEntity = companyRepository.findByLogisticsCompany(channelConfigEntity.getLogisticsCompany());
        if (companyEntity == null) {
            throw new BusinessServiceException("找不到渠道对应的物流公司");
        }
        TmsLogisticsCompany logisticsCompany = new TmsLogisticsCompany();
        TmsLogisticsChannelConfig logisticsChannelConfig = new TmsLogisticsChannelConfig();
        BeanUtilsEx.copyProperties(companyEntity, logisticsCompany);
        BeanUtilsEx.copyProperties(channelConfigEntity, logisticsChannelConfig);
        logisticsCompany.setLogisticsTypeStr(LogisticsTypeEnum.resolve(logisticsCompany.getLogisticsType()));
        companyAndChannelInfo.setLogisticsCompany(logisticsCompany);
        companyAndChannelInfo.setLogisticsChannelConfig(logisticsChannelConfig);
        return companyAndChannelInfo;
    }

    // 修改
    @Transactional(rollbackFor = Exception.class)
    public void update(VpsChannelFreightInsertRequest updateRequest) {
        VpsChannelFreightEntity entity = getById(updateRequest.getId());
        if (Objects.isNull(entity)) {
            throw new InvalidRequestException("没有找到对应数据");
        }
        saveOrEdit(updateRequest);
    }
    
    // 构造列表查询条件
    private LambdaQueryWrapper<VpsChannelFreightEntity> buildPageQueryWrapper(VpsChannelFreightPageRequest request) {
        LambdaQueryWrapper<VpsChannelFreightEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(request.getCompanyId() != null, VpsChannelFreightEntity::getCompanyId, request.getCompanyId());
        wrapper.eq(request.getChannelId() != null, VpsChannelFreightEntity::getChannelId, request.getChannelId());
        String tenant = TenantContext.getTenant();
        if (StrUtil.isNotBlank(tenant)) {
            List<TmsLogisticsCompanyEntity> companyList = companyRepository.findByLocation(tenant);
            if (!CollectionUtils.isEmpty(companyList)) {
                List<Integer> companyIdList = companyList.stream().map(TmsLogisticsCompanyEntity::getId).collect(Collectors.toList());
                wrapper.in(VpsChannelFreightEntity::getCompanyId, companyIdList);
            }
        }
        wrapper.in(!CollectionUtils.isEmpty(request.getStatusList()), VpsChannelFreightEntity::getStatus, request.getStatusList());
        if (request.getCreateDateBegin() != null && request.getCreateDateEnd() != null) {
            wrapper.ge(VpsChannelFreightEntity::getCreateDate, request.getCreateDateBegin());
            wrapper.le(VpsChannelFreightEntity::getCreateDate, request.getCreateDateEnd());
        }
        if (request.getStartDate() != null && request.getEndDate() != null) {
            List<Integer> freightIdList = channelCountryFreightService.getBaseMapper().getByStartDateAndEndDate(request.getStartDate(), request.getEndDate());
            if (CollectionUtils.isEmpty(freightIdList)) {
                wrapper.eq(VpsChannelFreightEntity::getId, 0);
            } else {
                wrapper.in(VpsChannelFreightEntity::getId, freightIdList);
            }
        }
        if (request.getReviewDateBegin() != null && request.getReviewDateEnd() != null) {
            wrapper.ge(VpsChannelFreightEntity::getReviewDate, request.getReviewDateBegin());
            wrapper.le(VpsChannelFreightEntity::getReviewDate, request.getReviewDateEnd());
        }
        if (StringUtils.hasText(request.getLogisticsType())) {
            List<TmsLogisticsCompanyEntity> companyList = companyRepository.findByLogisticsType(request.getLogisticsType());
            wrapper.in(!CollectionUtils.isEmpty(companyList), VpsChannelFreightEntity::getCompanyId, companyList.stream().map(TmsLogisticsCompanyEntity::getId).collect(Collectors.toList()));
        }
        wrapper.orderByDesc(VpsChannelFreightEntity::getId);
        return wrapper;
    }

    // 新增校验
    private void validInsertParams(VpsChannelFreightInsertRequest request, TmsLogisticsCompany companyEntity) {
        Integer companyId = accessControlService.getCompanyId();
        if (companyId != null) {
            request.setCompanyId(companyId);
        }
        if (StrUtil.equals(companyEntity.getLogisticsType(), LogisticsTypeEnum.INTERNATIONAL_EXPRESS.name())) {
            if (!StringUtils.hasText(request.getAccount())) {
                throw new BusinessServiceException("账号不能为空");
            }
        } else {
            request.setAccount(null);
        }
        request.getCountryFreightList().forEach(item -> item.setEndDate(DateUtil.endOfDay(item.getEndDate()).setField(DateField.MILLISECOND, 0)));
        // 校验国家字段必填、重量段是否重复
        request.getCountryFreightList().forEach(each -> {
            each.getFreightList().forEach(vi -> {
                vi.setCountryCode(each.getCountryCode());
                vi.setDiscountFactor(each.getDiscountFactor());
                vi.setCompanyId(request.getCompanyId());
                validBillType(request, vi);
            });
            checkOverlap(each.getFreightList());
        });
    }

    private void validBillType(VpsChannelFreightInsertRequest request, VpsChannelCountryFreightResponse vi) {
        vi.setId(null);
        vi.setCreateBy(accessControlService.getUserName());
        if (StrUtil.equals(BillingTypeEnum.UNIT_PRICE_WEIGHT.name(), request.getBillingType())) {
            if (vi.getStartWeight() == null)
                throw new BusinessServiceException("起始重量必填，国家：" + CountryCodeConstant.getCountryCodeMap().get(vi.getCountryCode()));
            if (vi.getEndWeight() == null)
                throw new BusinessServiceException("终止重量必填，国家：" + CountryCodeConstant.getCountryCodeMap().get(vi.getCountryCode()));
            if (vi.getUnitFreight() == null)
                throw new BusinessServiceException("运费单价必填，国家：" + CountryCodeConstant.getCountryCodeMap().get(vi.getCountryCode()));
            vi.setWeightBillType(request.getBillingType());
            return;
        }
        if (StrUtil.equals(BillingTypeEnum.INITIAL_ADDITIONAL_WEIGHT.name(), request.getBillingType())) {
            if (vi.getInitWeight() == null)
                throw new BusinessServiceException("首重(KG)必填，国家：" + CountryCodeConstant.getCountryCodeMap().get(vi.getCountryCode()));
            if (vi.getIntiFreight() == null)
                throw new BusinessServiceException("首重运费(元)必填，国家：" + CountryCodeConstant.getCountryCodeMap().get(vi.getCountryCode()));
            if (vi.getExtraFreight() == null)
                throw new BusinessServiceException("续重单价(元)必填，国家：" + CountryCodeConstant.getCountryCodeMap().get(vi.getCountryCode()));
            vi.setWeightBillType(request.getBillingType());
            return;
        }
        if (!StringUtils.hasText(vi.getWeightBillType()))
            throw new BusinessServiceException("必须选计费方法，国家：" + CountryCodeConstant.getCountryCodeMap().get(vi.getCountryCode()));
        if (StrUtil.equals(vi.getWeightBillType(), BillingTypeEnum.RANGE_WEIGHT.name())) {
            if (vi.getStartWeight() == null)
                throw new BusinessServiceException("起始重量必填，国家：" + CountryCodeConstant.getCountryCodeMap().get(vi.getCountryCode()));
            if (vi.getEndWeight() == null)
                throw new BusinessServiceException("终止重量必填，国家：" + CountryCodeConstant.getCountryCodeMap().get(vi.getCountryCode()));
            if (vi.getFixedFreight() == null)
                throw new BusinessServiceException("固定费用必填，国家：" + CountryCodeConstant.getCountryCodeMap().get(vi.getCountryCode()));
        } else if (StrUtil.equals(vi.getWeightBillType(), BillingTypeEnum.UNIT_PRICE_WEIGHT.name())) {
            if (vi.getStartWeight() == null)
                throw new BusinessServiceException("起始重量必填，国家：" + CountryCodeConstant.getCountryCodeMap().get(vi.getCountryCode()));
            if (vi.getEndWeight() == null)
                throw new BusinessServiceException("终止重量必填，国家：" + CountryCodeConstant.getCountryCodeMap().get(vi.getCountryCode()));
            if (vi.getUnitFreight() == null)
                throw new BusinessServiceException("运费单价必填，国家：" + CountryCodeConstant.getCountryCodeMap().get(vi.getCountryCode()));
        } else {
            throw new BusinessServiceException("重量段里面只能选 固定费用或者单价*重量，不能选其他，国家：" + CountryCodeConstant.getCountryCodeMap().get(vi.getCountryCode()));
        }
    }

    private void checkOverlap(List<VpsChannelCountryFreightResponse> checkList) {
        for (int i = 0; i < checkList.size(); i++) {
            VpsChannelCountryFreightResponse current = checkList.get(i);
            BigDecimal currentStartWeight = current.getStartWeight();
            BigDecimal currentEndWeight = current.getEndWeight();
            if (currentStartWeight == null || currentEndWeight == null) {
                continue;
            }
            // 与其他重量进行比较
            for (int j = i + 1; j < checkList.size(); j++) {
                VpsChannelCountryFreightResponse other = checkList.get(j);
                BigDecimal otherStartWeight = other.getStartWeight();
                BigDecimal otherEndWeight = other.getEndWeight();
                // 检查是否有重叠部分（左闭右开）, 修改 ==> 左闭右闭
                if (currentStartWeight.compareTo(otherEndWeight) < 0 && otherStartWeight.compareTo(currentEndWeight) < 0) {
                    throw new BusinessServiceException("重量区间覆盖，请检查国家：" + current.getCountryCode());
                }
            }
        }
    }

    public List<SelectModel> channelSelectOpt() {
        Integer companyId = accessControlService.getCompanyId();
        List<TmsLogisticsChannelConfigEntity> channelList = new ArrayList<>();
        if (companyId != null) {
            TmsLogisticsCompanyEntity one = companyRepository.findById(companyId).orElseThrow(() -> new BusinessServiceException("用户物流公司为空"));
            channelList.addAll(channelConfigRepository.findByLogisticsCompany(one.getLogisticsCompany()));
        } else {
            String location = TenantContext.getTenant();
            if (StrUtil.isNotBlank(location)) {
                channelList.addAll(channelConfigRepository.findByStatusAndLocation(StatusRequest.ENABLE, location));
            } else {
                channelList.addAll(channelConfigRepository.findByStatus(StatusRequest.ENABLE));
            }
        }
        List<SelectModel> result = new ArrayList<>();
        channelList.forEach(item -> {
            if (!TmsCommonConstant.DEAL_LOGISTICS.contains(item.getLogisticsChannelName())) {
                SelectModel model = new SelectModel();
                model.setLabel(item.getLogisticsChannelName() + "[" + item.getLogisticsChannelCode() + "]");
                model.setValue(item.getId().toString());
                result.add(model);
            }
        });
        return result;
    }

    public List<SelectModel> companySelectOpt() {
        String location = TenantContext.getTenant();
        List<TmsLogisticsCompanyEntity> all;
        if (StrUtil.isNotBlank(location)) {
            all = companyRepository.findByLocationAndStatus(location, StatusRequest.ENABLE);
        } else {
            all = companyRepository.findByStatus(StatusRequest.ENABLE);
        }
        List<SelectModel> result = new ArrayList<>();
        all.forEach(item -> {
            SelectModel model = new SelectModel();
            model.setLabel(item.getLogisticsCompany());
            model.setValue(item.getId().toString());
            result.add(model);
        });
        return result;
    }

    public void deleteFreight(Integer id) {
        VpsChannelFreightEntity byId = getById(id);
        if (byId == null) {
            throw new BusinessServiceException("找不到渠道运价信息");
        }
        if (!ChannelFreightStatusEnum.INIT.name().equals(byId.getStatus())) {
            throw new BusinessServiceException("待审状态才能删除！");
        }
        LOGGER.info("删除渠道运价，{}", JsonMapper.toJson(byId));
        List<VpsChannelCountryFreightEntity> channelFreights = channelCountryFreightService.getByChannelFreightId(byId.getId());
        LOGGER.info("删除渠道国家运价，{}", JsonMapper.toJson(channelFreights));
        removeById(id);
        channelCountryFreightService.removeByIds(channelFreights.stream().map(VpsChannelCountryFreightEntity::getId).collect(Collectors.toList()));
    }

    @Transactional
    public void audit(VpsChannelFreightAuditRequest request) {
        List<VpsChannelFreightEntity> entityList = listByIds(request.getIdList());
        entityList.forEach(entity -> {
            if (!ChannelFreightStatusEnum.INIT.name().equals(entity.getStatus())) {
                throw new BusinessServiceException("待审状态才能审核！");
            }
            entity.setReviewer(accessControlService.getRealName());
            if (ChannelFreightStatusEnum.PASS.name().equals(request.getStatus())) {
                entity.setReviewRemark(null);
                channelFreightLogService.addLog(entity.getCompanyId(), entity.getId(), "审核", "运费审核");
                LambdaUpdateWrapper<VpsChannelFreightEntity> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(VpsChannelFreightEntity::getReviewRemark, null);
                updateWrapper.eq(VpsChannelFreightEntity::getId, entity.getId());
                update(updateWrapper);
            } else {
                entity.setReviewRemark(request.getReviewMark());
                channelFreightLogService.addLog(entity.getCompanyId(), entity.getId(), "驳回", "运价驳回，驳回备注：" + StrUtil.toString(request.getReviewMark() == null ? "" : request.getReviewMark()));
            }
            entity.setReviewDate(new Date());
            entity.setStatus(request.getStatus());
            updateById(entity);
        });
    }

    public List<SelectModel> accountSelectOpt() {
        Integer companyId = accessControlService.getCompanyIdValid();
        TmsLogisticsCompanyEntity companyEntity = companyRepository.findById(companyId).orElseThrow(() -> new BusinessServiceException("用户物流公司为空"));
        List<TmsLogisticsAccountEntity> account = accountRepository.findByLogisticsCompany(companyEntity.getLogisticsCompany());
        List<SelectModel> list = new ArrayList<>();
        account.forEach(each -> {
            SelectModel model = new SelectModel();
            model.setLabel(each.getLogisticsAccount());
            model.setValue(each.getLogisticsAccount());
            list.add(model);
        });
        return list;
    }

    /**
     * 找出国家下面符合的运价(最新的id)
     * <AUTHOR>
     * 2023-12-04
     */
    public List<VpsChannelCountryFreightEntity> matchByChannelIdAndDateAndAccount(Integer channelId, String countryCode, Date shipDate, String account) {
        LambdaQueryWrapper<VpsChannelCountryFreightEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(VpsChannelCountryFreightEntity::getChannelId, channelId);
        wrapper.eq(StringUtils.hasText(account), VpsChannelCountryFreightEntity::getAccount, account);
        wrapper.le(VpsChannelCountryFreightEntity::getStartDate, shipDate);
        wrapper.ge(VpsChannelCountryFreightEntity::getEndDate, shipDate);
        if (StrUtil.equalsAnyIgnoreCase(countryCode, "GB", "UK")) {
            wrapper.in(VpsChannelCountryFreightEntity::getCountryCode, new ArrayList<>(Arrays.asList("GB", "UK")));
        } else {
            wrapper.eq(VpsChannelCountryFreightEntity::getCountryCode, countryCode);
        }
        wrapper.orderByDesc(VpsChannelCountryFreightEntity::getChannelFreightId);
        List<VpsChannelCountryFreightEntity> list = channelCountryFreightService.list(wrapper);
        List<VpsChannelCountryFreightEntity> result = new ArrayList<>();
        // 取出最近新增的id
        int recentId = 0;
        boolean matchPost = true;
        for (VpsChannelCountryFreightEntity item : list) {
            if (item.getChannelFreightId() < recentId) {
                continue;
            }
            VpsChannelFreightEntity byId = getById(item.getChannelFreightId());
            if (byId != null && ChannelFreightStatusEnum.PASS.name().equals(byId.getStatus())) {
                if (matchPost && StrUtil.equalsIgnoreCase(item.getStartPostCode(), PackageBillConstant.AREA_MAPPING_POST_CODE)) {
                    matchPost = false;
                    result.add(0, item);
                } else {
                    result.add(item);
                }
                recentId = item.getChannelFreightId();
            }
        }
        return result;
    }

    @Transactional
    public void copyFreight(VpsChannelFreightCopyRequest request) {
        VpsChannelFreightEntity channelFreightEntity = getById(request.getId());
        if (channelFreightEntity == null) {
            throw new BusinessServiceException("无法找到对应的报价单据！");
        }
        TmsLogisticsCompanyEntity sourCompany = companyRepository.findById(channelFreightEntity.getCompanyId()).orElseThrow(() -> new BusinessServiceException("物流公司不正确"));
        TmsLogisticsCompanyEntity targetCompany = companyRepository.findById(request.getTargetLogisticsCompanyId()).orElseThrow(() -> new BusinessServiceException("物流公司不正确"));
        if (!StrUtil.equals(sourCompany.getLogisticsType(), targetCompany.getLogisticsType())) {
            throw new BusinessServiceException("不同物流公司类型之间不能复制");
        }
        TmsLogisticsChannelConfigEntity channelConfigEntity = channelConfigRepository.findById(request.getTargetLogisticsChannelId())
                .orElseThrow(() -> new BusinessServiceException(String.format("物流渠道【%s】不存在！", request.getTargetLogisticsChannelId())));
        List<VpsChannelCountryFreightEntity> countryFreightEntityList = channelCountryFreightService.getByChannelFreightId(channelFreightEntity.getId());
        VpsChannelFreightEntity copy = new VpsChannelFreightEntity();
        BeanUtilsEx.copyProperties(channelFreightEntity, copy);
        copy.setId(null);
        copy.setUpdateBy(null);
        copy.setUpdateDate(null);
        copy.setCompanyId(request.getTargetLogisticsCompanyId());
        copy.setChannelId(request.getTargetLogisticsChannelId());
        copy.setCreateBy(accessControlService.getUserName());
        copy.setCreateDate(new Date());
        copy.setReviewDate(new Date());
        copy.setReviewer(copy.getCreateBy());
        save(copy);
        channelFreightLogService.addLog(copy.getCompanyId(), copy.getId(), "新增", "复制运费[" + channelConfigEntity.getLogisticsChannelName() + "]");
        if (!CollectionUtils.isEmpty(countryFreightEntityList)) {
            List<VpsChannelCountryFreightEntity> copyItemList = countryFreightEntityList.stream().map(it -> {
                VpsChannelCountryFreightEntity entity = new VpsChannelCountryFreightEntity();
                BeanUtilsEx.copyProperties(it, entity);
                entity.setCompanyId(request.getTargetLogisticsCompanyId());
                entity.setChannelId(request.getTargetLogisticsChannelId());
                entity.setChannelFreightId(copy.getId());
                return entity;
            }).collect(Collectors.toList());
            channelCountryFreightService.saveBatch(copyItemList);
        }
    }
}
