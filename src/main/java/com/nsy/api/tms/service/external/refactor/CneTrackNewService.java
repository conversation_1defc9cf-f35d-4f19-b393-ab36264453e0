package com.nsy.api.tms.service.external.refactor;

import com.alibaba.fastjson.JSON;
import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.dao.entity.TmsRouteRecordEntity;
import com.nsy.api.tms.enumeration.PackageStatusEnum;
import com.nsy.api.tms.logistics.cne.CneTrackRequest;
import com.nsy.api.tms.logistics.cne.CneTrackResponse;
import com.nsy.api.tms.service.TmsRouteService;
import com.nsy.api.tms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestClientException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class CneTrackNewService extends CneNewService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CneTrackNewService.class);

    @Autowired
    TmsRouteService routeService;

    @Value("${cne.track.url}")
    String cneTrackUrl;

    @Override
    public void doTrack(TmsPackageEntity packageEntity) {
        // 记录日志
        TmsRequestLogEntity logEntity = requestLogService.recordBaseInfoLog(packageEntity);
        //更新tms_package 表路由查询次数
        packageEntity.setRouteSearchTimes(packageEntity.getRouteSearchTimes() + 1);
        packageService.save(packageEntity);
        String logisticsNo = packageEntity.getLogisticsNo();
        CneTrackRequest cneTrackRequest = buildTrackRequest(packageEntity);
        String requestInfo = JSON.toJSONString(cneTrackRequest);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
        HttpEntity<String> entity = new HttpEntity<>(requestInfo, httpHeaders);
        LOGGER.debug("CNE追踪request请求信息:{}", JSON.toJSONString(cneTrackRequest));
        try {
            String responseStr = restTemplate.postForEntity(cneTrackUrl, entity, String.class).getBody();
            LOGGER.debug("CNE追踪response响应信息:{}", responseStr);
            CneTrackResponse response = JsonMapper.fromJson(responseStr, CneTrackResponse.class);
            if (response.getReturnValue() > 0) {
                processSuccessTrackReply(packageEntity, response, cneTrackUrl, logEntity);
            } else {
                LOGGER.info("CNE物流 logisticsNo：{},追踪失败,失败原因：{}", logisticsNo, responseStr);
                processFailTrackReply(responseStr, cneTrackUrl, logisticsNo, logEntity);
            }
        } catch (RestClientException e) {
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, requestInfo, e.getMessage(), logisticsNo);
            LOGGER.error("Cne 追踪异常: {}", e.getMessage(), e);
        }
    }

    private Map<String, String> buildTrackConfig(TmsPackageEntity packageEntity) {
//        String keyGroup = channelConfigEntity.getKeyGroup();
//        if ("stage".equalsIgnoreCase(env.getActiveProfiles()[0])) {
//            keyGroup = keyGroup + "_Track";
//        }
        return logisticsAccountService.buildAccountConfig(packageEntity.getLogisticsAccountId());
    }

    private CneTrackRequest buildTrackRequest(TmsPackageEntity packageEntity) {
        CneTrackRequest request = new CneTrackRequest();
        Map<String, String> configMap = buildTrackConfig(packageEntity);
        request.setLogisticsNo(packageEntity.getLogisticsNo());
        request.setCustomerId(configMap.get(CONFIG_APP_KEY));
        long time = new Date().getTime();
        request.setMd5(configMap, time);
        request.setTimeStamp(time);
        request.setRequestName("ClientTrack");
        return request;
    }

    private void processSuccessTrackReply(TmsPackageEntity packageEntity, CneTrackResponse cneTrackResponse, String requestUrl, TmsRequestLogEntity logEntity) {
        // 1. 更新日志
        requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, requestUrl, JSONUtils.toJSON(cneTrackResponse), packageEntity.getLogisticsNo());
        List<CneTrackResponse.EventInfo> trackingEventList = cneTrackResponse.getTrackingEventList();
        if (CollectionUtils.isEmpty(trackingEventList)) {
            LOGGER.info("logisticsNo:{}, 暂无路由信息", packageEntity.getLogisticsNo());
            return;
        }
        // 2. 更新tms_package状态，同时，写路由表（当前查询路由信息与上一条路由表记录状态一致，则不重新插入）
        List<TmsRouteRecordEntity> routeRecordEntityList = routeService.findByLogisticsNoOrderByCreateDateDesc(packageEntity.getLogisticsNo());
        CneTrackResponse.EventInfo eventInfo = trackingEventList.get(trackingEventList.size() - 1);
        String latestTrackLocation = eventInfo.getPlace();
        Integer state = eventInfo.getState();
        Date latestTrackTime = eventInfo.getDate();
        // 创建或更新路由
        if (routeRecordEntityList.isEmpty()
                || !(state.toString().equalsIgnoreCase(routeRecordEntityList.get(0).getStatus())
                && Objects.nonNull(routeRecordEntityList.get(0).getAcceptAddress())
                && routeRecordEntityList.get(0).getAcceptAddress().equalsIgnoreCase(latestTrackLocation)
                && Objects.nonNull(latestTrackTime)
                && Objects.nonNull(routeRecordEntityList.get(0).getAcceptTime())
                && routeRecordEntityList.get(0).getAcceptTime().compareTo(latestTrackTime) == 0)) {
            persistTmsRouteRecord(packageEntity.getLogisticsNo(), latestTrackTime, latestTrackLocation, trackingEventList);
        }

        //--cne记录状态含义 0：未发送 1：已发送 2：转运中 3：送达 4：超时 5：扣关 6：地址错误 7：快件丢失 8：退件 9：其它异常 10：销毁
        // 20.21:首公里-揽收扫描 到达-揽收点 离开-出口转运中心 等状态 文档查看：https://docs.qq.com/doc/DZFBEckZJUnVmeWlC
        packageEntity.setRouteLastUpdateTime(latestTrackTime);
        if (state == 0 && Objects.nonNull(eventInfo.getDetails()) && eventInfo.getDetails().equalsIgnoreCase("收到预录单电子信息")) {
            packageEntity.setStatus(PackageStatusEnum.SHIPPED.getDesc());
            packageEntity.setRouteLastUpdateTime(null);
        } else if (state == 1 || state == 2 || state == 20 || state == 21
                || (state == 0 && Objects.nonNull(eventInfo.getDetails()) && !eventInfo.getDetails().equalsIgnoreCase("收到预录单电子信息"))) {
            packageEntity.setStatus(PackageStatusEnum.TRANSIT.getDesc());
        } else if (state == 3) {
            packageEntity.setStatus(PackageStatusEnum.DELIVERED.getDesc());
            packageEntity.setArrivalTime(latestTrackTime);
        } else {
            packageEntity.setStatus(PackageStatusEnum.TRANSIT.getDesc());
//            generateAlertTask(packageEntity);
        }
        packageService.save(packageEntity);
    }


    private void persistTmsRouteRecord(String logisticsNo, Date latestTrackTime, String latestTrackLocation, List<CneTrackResponse.EventInfo> trackDataList) {
        TmsRouteRecordEntity routeRecordEntity = new TmsRouteRecordEntity();
        routeRecordEntity.setStatus(trackDataList.get(trackDataList.size() - 1).getState().toString());
        routeRecordEntity.setAcceptAddress(latestTrackLocation);
        routeRecordEntity.setAcceptTime(latestTrackTime);
        routeRecordEntity.setLogisticsNo(logisticsNo);
        routeRecordEntity.setRemark(trackDataList.get(trackDataList.size() - 1).getDetails());
        routeService.save(routeRecordEntity);
    }

    private void processFailTrackReply(String responseStr, String requestUrl, String logisticsNo, TmsRequestLogEntity logEntity) {
        requestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, JsonMapper.toJson(requestUrl), responseStr, logisticsNo);
    }
}
