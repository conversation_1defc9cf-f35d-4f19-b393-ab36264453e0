package com.nsy.api.tms.service.freight;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.dao.entity.TmsLogisticsChannelConfigEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsCompanyEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.freight.PackageBillEntity;
import com.nsy.api.tms.dao.entity.freight.StockoutShipmentEntity;
import com.nsy.api.tms.domain.StockoutShipmentModel;
import com.nsy.api.tms.enumeration.LogisticsTypeEnum;
import com.nsy.api.tms.enumeration.PackageBillStatusEnum;
import com.nsy.api.tms.enumeration.PackageStatusEnum;
import com.nsy.api.tms.listener.TrackingMoreCreateEvent;
import com.nsy.api.tms.mapper.freight.StockoutShipmentMapper;
import com.nsy.api.tms.repository.TmsLogisticsChannelConfigRepository;
import com.nsy.api.tms.repository.TmsLogisticsCompanyRepository;
import com.nsy.api.tms.request.freight.SyncTmsStockoutShipmentRequest;
import com.nsy.api.tms.service.PackageNewService;
import com.nsy.api.tms.service.TmsLogisticsAccountService;
import com.nsy.api.tms.service.TmsLogisticsChannelConfigService;
import com.nsy.api.tms.service.TmsService;
import com.nsy.api.tms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * HXD
 * 2023/9/14
 **/

@Service
public class StockoutShipmentService extends ServiceImpl<StockoutShipmentMapper, StockoutShipmentEntity> {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutShipmentService.class);

    @Autowired
    PackageNewService packageNewService;
    @Autowired
    PackageBillService billService;
    @Autowired
    TmsLogisticsCompanyRepository logisticsCompanyRepository;
    @Autowired
    TmsLogisticsChannelConfigService channelConfigService;
    @Autowired
    TmsLogisticsChannelConfigRepository channelConfigRepository;
    @Inject
    private ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    StockoutShipmentItemService shipmentItemService;
    @Autowired
    TmsLogisticsAccountService accountService;
    @Autowired
    TmsService tmsService;
    @Autowired
    PackageBillPermissionService packageBillPermissionService;

    /**
     * 1、同步装箱清单
     * 2、触发 账单变更
     *
     * <AUTHOR>
     * 2023-09-14
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncShipped(SyncTmsStockoutShipmentRequest request) {
        LOGGER.info("wms发货同步：{}", JsonMapper.toJson(request));
        StockoutShipmentModel shipmentModel = request.getStockoutShipment();
        if (shipmentModel == null || request.getStockoutShipmentItemList() == null) {
            return;
        }
        // 中转fedex 不修改原来的账单，也不在同步任何物流信息
        if (StrUtil.equalsIgnoreCase(shipmentModel.getLogisticsCompany(), "美国空运中转-Fedex")) {
            return;
        }
        // 特殊物流，需要生成包裹信息，并进行对账
        boolean resetPackageInfo = true;
        if (StrUtil.equalsIgnoreCase(shipmentModel.getLogisticsCompany(), "顺丰国际标快+（包裹）") && StrUtil.isNotBlank(shipmentModel.getLogisticsNo())) {
            resetPackageInfo = false;
            generatePackage(request, shipmentModel);
        }
        if (StringUtils.hasText(shipmentModel.getForwarderChannel())) {
            return;
        }
        if (StrUtil.isAllNotBlank(shipmentModel.getLogisticsNo(), shipmentModel.getTransferLogisticsNo())) {
            TmsPackageEntity packageByLogisticsNo = packageNewService.findPackageByLogisticsNoAndSecondaryNumber(shipmentModel.getTransferLogisticsNo(), shipmentModel.getLogisticsNo());
            if (packageByLogisticsNo != null) {
                shipmentModel.setLogisticsNo(packageByLogisticsNo.getLogisticsNo());
                shipmentModel.setTransferLogisticsNo(packageByLogisticsNo.getSecondaryNumber());
            }
        }
        String newLogisticsNo = shipmentModel.getLogisticsNo();
        StockoutShipmentEntity shipment = getById(shipmentModel.getShipmentId());
        String oldLogisticsNo = null;
        if (shipment != null) {
            oldLogisticsNo = shipment.getLogisticsNo();
            BeanUtilsEx.copyProperties(shipmentModel, shipment);
            updateById(shipment);
        } else {
            shipment = new StockoutShipmentEntity();
            BeanUtilsEx.copyProperties(shipmentModel, shipment);
            save(shipment);
        }
        shipmentItemService.deleteAndAdd(shipment, request);
        // 重新整理包裹信息
        if (resetPackageInfo) {
            packageNewService.reSetPackageInfo(request, shipmentModel);
        }
        buildBill(newLogisticsNo);
        setOrderPayTime(newLogisticsNo, request.getOrderPayTime());
        if (!StrUtil.equals(newLogisticsNo, oldLogisticsNo)) {
            buildBill(oldLogisticsNo);
            // 判断该包裹是不是存在装箱清单
            packageNewService.existShipment(oldLogisticsNo);
        }
    }

    public void generatePackage(SyncTmsStockoutShipmentRequest request, StockoutShipmentModel shipmentModel) {
        TmsLogisticsChannelConfigEntity channel = channelConfigRepository.findByLogisticsChannelNameAndLocation(shipmentModel.getLogisticsCompany(), shipmentModel.getLocation());
        if (channel == null) {
            return;
        }
        TmsLogisticsCompanyEntity companyEntity = logisticsCompanyRepository.findByLogisticsCompanyAndLocation(channel.getLogisticsCompany(), channel.getLocation());
        if (companyEntity == null) {
            return;
        }
        TmsPackageEntity packageByLogisticsNo = packageNewService.findPackageByLogisticsNo(shipmentModel.getLogisticsNo());
        if (packageByLogisticsNo != null) {
            return;
        }
        if (StrUtil.equalsIgnoreCase(companyEntity.getLogisticsType(), LogisticsTypeEnum.FREIGHT_FORWARDER.name())) {
            return;
        }
        TmsPackageEntity tmsPackageEntity = new TmsPackageEntity();
        tmsPackageEntity.setLogisticsNo(shipmentModel.getLogisticsNo());
        tmsPackageEntity.setLogisticsMethod(companyEntity.getLogisticsMethod());
        tmsPackageEntity.setLogisticsMethod(companyEntity.getLogisticsMethod());
        tmsPackageEntity.setTid(request.getStockoutShipmentItemList().get(0).getOrderNo());
        tmsPackageEntity.setBusinessKey(request.getStockoutShipmentItemList().get(0).getStockoutOrderNo());
        tmsPackageEntity.setLogisticsChannelCode(channel.getLogisticsChannelCode());
        tmsPackageEntity.setKeyGroup(channel.getKeyGroup());
        tmsPackageEntity.setStatus(PackageStatusEnum.SHIPPED.getDesc());
        tmsPackageEntity.setDeptName(request.getBusinessType());
        tmsPackageEntity.setStoreId(request.getStoreId());
        tmsPackageEntity.setStoreName(request.getStoreName());
        tmsPackageEntity.setLabelUrl("TEST_LABEL_URL");
        tmsPackageEntity.setCountryCode(request.getCountryCode());
        tmsPackageEntity.setShipDate(shipmentModel.getDeliveryDate());
        tmsPackageEntity.setCreateBy("SyncByShipmentShipped");
        tmsPackageEntity.setLogisticsCompany(companyEntity.getLogisticsCompany());
        tmsPackageEntity.setLocation(companyEntity.getLocation());
        tmsPackageEntity.setLogisticsType(companyEntity.getLogisticsType());
        packageNewService.save(tmsPackageEntity);
        applicationEventPublisher.publishEvent(new TrackingMoreCreateEvent(Arrays.asList(tmsPackageEntity)));
    }

    private void setOrderPayTime(String logisticsNo, Date orderPayTime) {
        PackageBillEntity billEntity = packageBillPermissionService.getPackageBillWithPermission(logisticsNo);
        if (billEntity == null) {
            return;
        }
        billEntity.setOrderPayDate(orderPayTime);
        billService.updateById(billEntity);
    }

    // 建立账单
    public void buildBill(String logisticsNo) {
        if (StrUtil.isBlank(logisticsNo)) {
            return;
        }
        TmsPackageEntity packageEntity = packageNewService.findPackageByLogisticsNo(logisticsNo);
        if (packageEntity == null) {
            LOGGER.info("{}无法找到对应的物流包裹，不创建账单！", logisticsNo);
            return;
        }
        if (StrUtil.isBlank(packageEntity.getLogisticsType()) || StrUtil.equalsAnyIgnoreCase(packageEntity.getLogisticsType(), LogisticsTypeEnum.FREIGHT_FORWARDER.getName())) {
            return;
        }
        TmsLogisticsCompanyEntity logisticsCompany = logisticsCompanyRepository.findByLogisticsCompany(packageEntity.getLogisticsCompany());
        if (logisticsCompany == null || !Objects.equals(logisticsCompany.getVerifyPrice(), 1)) {
            return;
        }
        PackageBillEntity billEntity = packageBillPermissionService.getPackageBillWithPermission(logisticsNo);
        if (billEntity != null && !PackageBillStatusEnum.getEnableDelete().contains(billEntity.getStatus())) {
            LOGGER.info("{}该物流包裹已核对，无法修改数据", logisticsNo);
            return;
        }
        // 初始化
        billService.initBill(logisticsNo);
        // 初步计算价格
        billService.evaluateBill(logisticsNo);
    }


    public List<StockoutShipmentEntity> findByLogisticsNo(String logisticsNo) {
        LambdaQueryWrapper<StockoutShipmentEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutShipmentEntity::getLogisticsNo, logisticsNo);
        wrapper.eq(StockoutShipmentEntity::getIsDeleted, 0);
        return list(wrapper);
    }
}
