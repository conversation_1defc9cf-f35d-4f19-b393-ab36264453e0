package com.nsy.api.tms.service.external;

import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.tms.dao.entity.TmsLogisticsChannelConfigEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.dao.entity.TmsRouteRecordEntity;
import com.nsy.api.tms.enumeration.PackageStatusEnum;
import com.nsy.api.tms.logistics.epacket.request.EPacketAuthDetail;
import com.nsy.api.tms.logistics.epacket.response.EPacketTrackResponse;
import com.nsy.api.tms.logistics.epacket.response.Trace;
import com.nsy.api.tms.service.PackageService;
import com.nsy.api.tms.service.TmsConfigService;
import com.nsy.api.tms.service.TmsRequestLogService;
import com.nsy.api.tms.service.TmsRouteService;
import com.nsy.api.tms.utils.Dom4jUtils;
import com.nsy.api.tms.utils.JaxbObjectAndXmlUtil;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class EpacketTrackService extends EpacketService {

    private static final Logger LOGGER = LoggerFactory.getLogger(EpacketTrackService.class);
    private static final String TRACK_LANGUAGE = "cn";

    @Value("${epacket.track.url}")
    String trackUrl;

    @Inject
    RestTemplate restTemplate;

    @Inject
    TmsRequestLogService tmsRequestLogService;

    @Inject
    TmsRouteService tmsRouteService;

    @Inject
    PackageService packageService;

    @Inject
    TmsConfigService tmsConfigService;


    public void doTrack(TmsPackageEntity packageEntity) {
        // 记录日志
        TmsRequestLogEntity logEntity = tmsRequestLogService.recordBaseInfoLog(packageEntity);
        //更新tms_package 表路由查询次数
        packageEntity.setRouteSearchTimes(packageEntity.getRouteSearchTimes() + 1);
        packageService.save(packageEntity);
        TmsLogisticsChannelConfigEntity configEntity = logisticsChannelConfigService.findByLogisticsMethodAndKeyGroupAndLogisticsChannelCodeAndStatus(packageEntity.getLogisticsMethod(), packageEntity.getKeyGroup(), packageEntity.getLogisticsChannelCode(), TmsLogisticsChannelConfigEntity.ACTIVE);
        Map<String, String> configMap = buildTrackConfig(configEntity);
        String url = String.format(trackUrl, TRACK_LANGUAGE, packageEntity.getLogisticsNo());
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("version", EPacketAuthDetail.EPACKET_API_VERSION);
        httpHeaders.add("authenticate", configMap.get(CONFIG_AUTHENTICATE));
        try {
            ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(httpHeaders), String.class);
            LOGGER.debug("responseXml: {}", responseEntity.getBody());
            if (!"null".equals(responseEntity.getBody())) {
                Element element = Dom4jUtils.parseXml(responseEntity.getBody());
                if ("traces".equals(element.getQName().getName())) {
                    processSuccessTrackReply(packageEntity, responseEntity.getBody());
                    tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, String.format("{\"url\":\"%s\"}", url), responseEntity.getBody(), packageEntity.getLogisticsNo());
                } else {
                    // processFailTrackReply(url, responseEntity.getBody(), packageEntity.getLogisticsNo());
                    tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, String.format("{\"url\":\"%s\"}", url), responseEntity.getBody(), packageEntity.getLogisticsNo());
                }
            } else {
                // 1. 写调用日志表
                tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, String.format("{\"url\":\"%s\"}", url), responseEntity.getBody(), packageEntity.getLogisticsNo());
                LOGGER.info("logisticsNo:{}, 暂无路由信息", packageEntity.getLogisticsNo());
            }
        } catch (Exception e) {
            tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, String.format("{\"url\":\"%s\"}", url), e.getMessage(), packageEntity.getLogisticsNo());
            LOGGER.error(e.getMessage(), e);
        }
    }

    private Map<String, String> buildTrackConfig(TmsLogisticsChannelConfigEntity channelConfigEntity) {
        String keyGroup = channelConfigEntity.getKeyGroup();
        if ("stage".equalsIgnoreCase(env.getActiveProfiles()[0])) {
            keyGroup = keyGroup + "_Track";
        }
        return tmsConfigService.getConfigMap(keyGroup);
    }

//    private void processFailTrackReply(String url, String responseStr, String logisticsNo) {
//        TmsRequestLogEntity tmsRequestLogEntity = new TmsRequestLogEntity
//                .Builder(LogisticsMethodEnum.EPACKET.getLogisticsMethod()
//                , "track-api", TmsRequestLogEntity.FAIL, "{\"url\":\"" + url + "\"}"
//                , responseStr).logisticsNo(logisticsNo).build();
//        tmsRequestLogService.save(tmsRequestLogEntity);
//    }

    private void processSuccessTrackReply(TmsPackageEntity packageEntity, String responseStr) {
        EPacketTrackResponse response = JaxbObjectAndXmlUtil.xml2Object(responseStr, EPacketTrackResponse.class);
        // 1. 更新tms_package状态，同时，写路由表（当前查询路由信息与上一条路由表记录状态一致，则不重新插入）
        List<Trace> traceList = response.getTraceList();
        Trace lastTrace = traceList.get(traceList.size() - 1);
        List<TmsRouteRecordEntity> routeRecordEntityList = tmsRouteService.findByLogisticsNoOrderByCreateDateDesc(packageEntity.getLogisticsNo());

        // 新增或更新路由
        if (routeRecordEntityList.isEmpty()
                || !(routeRecordEntityList.get(0).getAcceptTime().compareTo(DateUtils.parse(lastTrace.getAcceptTime(), DateUtils.DATE_FORMAT_DATE4)) == 0
                && routeRecordEntityList.get(0).getAcceptAddress().equalsIgnoreCase(lastTrace.getAcceptAddress())
                && routeRecordEntityList.get(0).getRemark().equalsIgnoreCase(lastTrace.getRemark()))) {
            persistTmsRouteRecord(packageEntity.getLogisticsNo(), lastTrace, packageEntity.getStatus());
        }

        if (traceList.size() == 1) {
            packageEntity.setStatus(PackageStatusEnum.TAKEN.getDesc());
        } else {
            if (lastTrace.getRemark().contains("妥投")) {
                if (lastTrace.getRemark().contains("未")) {
                    packageEntity.setStatus(PackageStatusEnum.TRANSIT.getDesc());
                } else {
                    packageEntity.setArrivalTime(DateUtils.parse(lastTrace.getAcceptTime(), DateUtils.DATE_FORMAT_DATE4));
                    packageEntity.setStatus(PackageStatusEnum.DELIVERED.getDesc());
                }
            } else {
                packageEntity.setStatus(PackageStatusEnum.TRANSIT.getDesc());
            }
        }
        if (Objects.isNull(packageEntity.getReceiveTime())) {
            packageEntity.setReceiveTime(DateUtils.parse(traceList.get(0).getAcceptTime(), DateUtils.DATE_FORMAT_DATE4));
        }
        packageEntity.setRouteLastUpdateTime(DateUtils.parse(lastTrace.getAcceptTime(), DateUtils.DATE_FORMAT_DATE4));
        packageService.save(packageEntity);
    }

    private void persistTmsRouteRecord(String logisticsNo, Trace trace, String status) {
        TmsRouteRecordEntity routeRecordEntity = new TmsRouteRecordEntity();
        routeRecordEntity.setStatus(status);
        routeRecordEntity.setAcceptAddress(trace.getAcceptAddress());
        routeRecordEntity.setAcceptTime(DateUtils.parse(trace.getAcceptTime(), DateUtils.DATE_FORMAT_DATE4));
        routeRecordEntity.setLogisticsNo(logisticsNo);
        routeRecordEntity.setRemark(trace.getRemark());
        tmsRouteService.save(routeRecordEntity);
    }
}
