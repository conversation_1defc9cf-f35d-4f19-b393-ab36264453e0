package com.nsy.api.tms.service.external;

import com.nsy.api.tms.annotation.TmsHandler;
import com.nsy.api.tms.domain.OrderInfo;
import com.nsy.api.tms.domain.OrderItemInfo;
import com.nsy.api.tms.logistics.hualei.HuaLeiLogisticsOrderRequest;
import com.nsy.api.tms.logistics.hualei.HuaLeiLogisticsOrderResponse;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * 华磊系统 （EDL & 捷优 货代 都是使用华磊系统）
 */
@Service
@TmsHandler(logisticsMethod = "JYExpress")
public class JYExpressService extends HuaLeiService implements InitializingBean {

    @Value("${jy.createorder.url}")
    private String jyCreateOrderUrl;

    @Value("${jy.printLabel.url}")
    private String jyPrintLabelUrl;

    @Value("${jy.ip.url}")
    private String jyHostIP;

    @Value("${jy.authentication.url}")
    private String jyAuthenticationUrl;

    @Value("${jy.track.url}")
    private String jyTrackUrl;

    @Value("${jy.trackNumber.url}")
    String jyTrackingNumberUrl;

    @Override
    public void afterPropertiesSet() {
        this.createOrderUrl = jyCreateOrderUrl;
        this.printLabelUrl = jyPrintLabelUrl;
        this.hostIP = jyHostIP;
        this.authenticationUrl = jyAuthenticationUrl;
        this.labelFolder += "JY/";
        this.ossLabelFolder += "JY/";
        this.labelNameTemplate = "%sJY-%s.png";
        this.trackUrl = jyTrackUrl;
        this.trackingNumberUrl = jyTrackingNumberUrl;
    }

    @Override
    public String buildTmsTid(OrderInfo orderInfo) {
        // 该渠道请求单号限制19位
        if ("9621".equals(orderInfo.getLogisticsChannelCode())) {
            String suffixTid = generateRandom(4);
            return trimTid(orderInfo.getBusinessKey()) + "-" + suffixTid;
        }
        return super.buildTmsTid(orderInfo);
    }

    @Override
    protected boolean isResponseOk(HuaLeiLogisticsOrderResponse reply) {
        try {
            return (!reply.getAck().equals("false")) || URLDecoder.decode(reply.getMessage(), "utf-8").contains("等待分配单号");
        } catch (UnsupportedEncodingException e) {
            LOGGER.error(e.getMessage(), e);
        }
        return false;
    }

    @Override
    protected String getSkuByByService(OrderItemInfo orderItem, String channelCode) {
        return orderItem.getCnName() + orderItem.getSku();
    }

    @Override
    protected void buildItemInvoice(OrderItemInfo orderItem, HuaLeiLogisticsOrderRequest.OrderInvoiceParam orderInvoiceParam, String logisticsChannelCode) {
        if (Objects.equals("1842", logisticsChannelCode) || Objects.equals("11981", logisticsChannelCode)) {
            orderInvoiceParam.setInvoiceMaterial(orderItem.getInvoiceMaterial());
            orderInvoiceParam.setInvoicePurpose(orderItem.getInvoicePurpose());
        }
    }

    @Override
    protected void buildIossNumber(OrderInfo orderInfo, HuaLeiLogisticsOrderRequest huaLeiOrderRequest) {
        if (StringUtils.isEmpty(orderInfo.getIossNumber())) {
            return;
        }
        // 大陆DHL(小包) IOSS
        if (Objects.equals("1842", orderInfo.getLogisticsChannelCode())) {
            huaLeiOrderRequest.setShipperTaxnotype("SDT");
        }
        // 广州EUB  IOSS
        if (Objects.equals("1361", orderInfo.getLogisticsChannelCode())) {
            huaLeiOrderRequest.setShipperTaxnotype("IOSS");
        }
        huaLeiOrderRequest.setShipperTaxno(orderInfo.getIossNumber());
        huaLeiOrderRequest.setShipperTaxnocountry("CN");
    }
}
