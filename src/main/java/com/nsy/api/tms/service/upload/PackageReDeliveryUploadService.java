package com.nsy.api.tms.service.upload;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.enumeration.QuartzUploadQueueTypeEnum;
import com.nsy.api.tms.request.upload.TmsPackageReDeliveryImport;
import com.nsy.api.tms.request.upload.UploadRequest;
import com.nsy.api.tms.response.upload.UploadResponse;
import com.nsy.api.tms.service.PackageNewService;
import com.nsy.api.tms.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;


@Service
public class PackageReDeliveryUploadService implements IProcessUploadDataService {
    @Autowired
    private PackageNewService packageNewService;


    @Override
    public QuartzUploadQueueTypeEnum type() {
        return QuartzUploadQueueTypeEnum.TMS_PACKAGE_RE_DELIVERY;
    }

    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        UploadResponse response = new UploadResponse();
        if (!StringUtils.hasText(request.getDataJsonStr())) {
            return response;
        }
        List<TmsPackageReDeliveryImport> importList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), TmsPackageReDeliveryImport.class);
        if (CollectionUtils.isEmpty(importList)) {
            return response;
        }
        List<TmsPackageReDeliveryImport> errorList = new ArrayList<>();
        importList.forEach(row -> {
            try {
                packageNewService.reDelivery(row, request.getCreateBy());
            } catch (BusinessServiceException e) {
                row.setErrorMsg(e.getMessage());
                errorList.add(row);
            }
        });
        if (!CollectionUtils.isEmpty(errorList)) {
            response.setDataJsonStr(JsonMapper.toJson(errorList));
        }
        return response;
    }
}
