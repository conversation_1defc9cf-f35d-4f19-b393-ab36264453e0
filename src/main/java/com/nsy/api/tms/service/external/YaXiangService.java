package com.nsy.api.tms.service.external;

import com.nsy.api.core.apicore.util.MD5Util;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.annotation.TmsHandler;
import com.nsy.api.tms.dao.entity.TmsLogisticsChannelConfigEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.domain.Address;
import com.nsy.api.tms.domain.OrderInfo;
import com.nsy.api.tms.domain.OrderItemInfo;
import com.nsy.api.tms.enumeration.LogisticsMethodEnum;
import com.nsy.api.tms.logistics.yaxiang.Entity;
import com.nsy.api.tms.logistics.yaxiang.ObjJson;
import com.nsy.api.tms.logistics.yaxiang.YaXiangOrderGoodItem;
import com.nsy.api.tms.logistics.yaxiang.YaXiangOrderRequest;
import com.nsy.api.tms.logistics.yaxiang.YaXiangOrderResponse;
import com.nsy.api.tms.logistics.yaxiang.YaXiangOrderWrapperRequest;
import com.nsy.api.tms.request.BaseLogisticsOrderRequest;
import com.nsy.api.tms.request.OrderRequest;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.utils.FileUtils;
import com.nsy.api.tms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import javax.validation.Valid;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@TmsHandler(logisticsMethod = "亚翔国际")
public class YaXiangService extends BaseLogisticsService implements InitializingBean {
    private static final Logger LOGGER = LoggerFactory.getLogger(YaXiangService.class);

    public static final String CONFIG_KEY = "key";
    public static final String CONFIG_CUSTOMER_CODE = "customer_code";
    public static final String LABEL_NAME = "%sYaXiang-%s";

    @Inject
    RestTemplate restTemplate;

    @Value("${yaxiang.order.service.url}")
    private String yaXiangOrderServiceUrl;

    @Override
    public void afterPropertiesSet() throws Exception {
        this.labelFolder += "YaXiang/";
        this.ossLabelFolder += "YaXiang/";
    }

    @Override
    public GenerateOrderResponse syncGenerateOrder(OrderRequest request, TmsLogisticsChannelConfigEntity tmsLogisticsChannelConfigEntity) {
        TmsRequestLogEntity logEntity = tmsRequestLogService.recordBaseInfoLog(request.getOrderInfo());
        GenerateOrderResponse response = new GenerateOrderResponse();
        Map<String, String> configMap = buildConfig(tmsLogisticsChannelConfigEntity);
        YaXiangOrderWrapperRequest yaXiangRequest = null;
        try {
            yaXiangRequest = buildRequestParam(request.getOrderInfo(), configMap, tmsLogisticsChannelConfigEntity);
            YaXiangOrderResponse yaXiangOrderResponse = this.restTemplate.postForObject(yaXiangOrderServiceUrl, yaXiangRequest, YaXiangOrderResponse.class);
            if (isResponseOk(yaXiangOrderResponse)) {
                GenerateOrderResponse.SuccessEntity successEntity = successCallback(request.getOrderInfo(), yaXiangOrderResponse);
                response.setSuccessEntity(successEntity);
                tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, JsonMapper.toJson(yaXiangRequest), JsonMapper.toJson(yaXiangOrderResponse), successEntity.getLogisticsNo());
            } else {
                GenerateOrderResponse.Error error = failCallback(yaXiangOrderResponse);
                response.setError(error);
                tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, JsonMapper.toJson(yaXiangRequest), JsonMapper.toJson(yaXiangOrderResponse), null);
            }
        } catch (RestClientException e) {
            LOGGER.error(e.getMessage(), e);
            tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, JsonMapper.toJson(yaXiangRequest), e.getMessage(), null);
            throw new RuntimeException(e);
        }
        return response;
    }

    private GenerateOrderResponse.Error failCallback(YaXiangOrderResponse yaXiangOrderResponse) {
        String entityStr = yaXiangOrderResponse.getEntity();
        Entity entity = JsonMapper.fromJson(entityStr, Entity.class);
        String code = entity.getCode();
        String message = entity.getMsg();
        return buildError(code, message);
    }

    private GenerateOrderResponse.SuccessEntity successCallback(@Valid OrderInfo orderInfo, YaXiangOrderResponse yaXiangOrderResponse) {
        String entityStr = yaXiangOrderResponse.getEntity();
        Entity entity = JsonMapper.fromJson(entityStr, Entity.class);
        ObjJson objJson = null;
        try {
            objJson = JsonMapper.fromJson(URLDecoder.decode(entity.getObjJson(), "utf-8"), ObjJson.class);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        String logisticsNo = objJson.getOrderNo();
        String originPdfUrl = objJson.getLabelUrl();
        String labelUrl = getLabel(logisticsNo, originPdfUrl);
        return buildSuccessEntity(orderInfo, logisticsNo, labelUrl, LogisticsMethodEnum.YAXIANGGUOJI, null, null);
    }

    private String getLabel(String logisticsNo, String originPdfUrl) {
        byte[] pdfByte = FileUtils.downloadPdf(originPdfUrl, restTemplate);
        String labelFileName = String.format(LABEL_NAME, labelFolder, logisticsNo);
        try {
            return getPdfLabel(pdfByte, labelFileName);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private YaXiangOrderWrapperRequest buildRequestParam(OrderInfo orderInfo, Map<String, String> configMap, TmsLogisticsChannelConfigEntity tmsLogisticsChannelConfigEntity) {
        YaXiangOrderWrapperRequest request = new YaXiangOrderWrapperRequest();
        String key = configMap.get(CONFIG_KEY);
        String customerCode = configMap.get(CONFIG_CUSTOMER_CODE);
        YaXiangOrderRequest orderRequest = buildOrder(orderInfo, tmsLogisticsChannelConfigEntity);
        List<YaXiangOrderGoodItem> orderGoodItemList = buildOrderList(orderInfo);
        String signMsg = buildSignMsg(JsonMapper.toJson(orderRequest), JsonMapper.toJson(orderGoodItemList), customerCode, key);
        request.setYaXiangOrderRequest(orderRequest);
        request.setListRequest(orderGoodItemList);
        request.setCustomerCode(customerCode);
        request.setSignMsg(signMsg.toUpperCase());
        return request;
    }

    private String buildSignMsg(String order, String orderGoodList, String customerCode, String key) {
        return MD5Util.crypt(order + orderGoodList + customerCode + key);
    }

    private List<YaXiangOrderGoodItem> buildOrderList(OrderInfo orderInfo) {
        List<OrderItemInfo> orderItemInfoList = orderInfo.getOrderItemInfoList();
        List<YaXiangOrderGoodItem> yaXiangOrderGoodItemList = new ArrayList<>();
        for (OrderItemInfo orderItemInfo : orderItemInfoList) {
            YaXiangOrderGoodItem orderGoodItem = new YaXiangOrderGoodItem();
            orderGoodItem.setSku(orderItemInfo.getSku());
            orderGoodItem.setGoodsName(orderItemInfo.getEnName());
            orderGoodItem.setGoodsNameCn(orderItemInfo.getCnName());
            orderGoodItem.setDeclPrice(orderItemInfo.getCustomsPrice() / orderItemInfo.getCount());
            orderGoodItem.setDeclCount(orderItemInfo.getCount());
            orderGoodItem.setHscode(orderItemInfo.getHsCode());
            yaXiangOrderGoodItemList.add(orderGoodItem);
        }
        return yaXiangOrderGoodItemList;
    }

    private YaXiangOrderRequest buildOrder(OrderInfo orderInfo, TmsLogisticsChannelConfigEntity tmsLogisticsChannelConfigEntity) {
        YaXiangOrderRequest request = new YaXiangOrderRequest();
        request.setCustomerNo(orderInfo.getTmsTid());
        request.setProdouctId(orderInfo.getLogisticsChannelCode());
        request.setProductName(tmsLogisticsChannelConfigEntity.getLogisticsChannelName());
        Address receiver = orderInfo.getReceiver();
        request.setToCountry(receiver.getCountry());
        request.setToProvince(receiver.getProvince());
        request.setToCity(receiver.getCity());
        request.setToName(receiver.getName());
        request.setToTel(StringUtils.hasText(receiver.getMobile()) ? receiver.getMobile() : receiver.getPhone());
        request.setToZipCode(receiver.getPostCode());
        request.setToAddress(receiver.getStreet());
        request.setValue(orderInfo.getCustomsValueAmount());
        request.setCustomerWeight(orderInfo.getWeight());
        request.setPackageCount(orderInfo.getOrderItemInfoList().stream().mapToInt(OrderItemInfo::getCount).sum());
        Address sender = orderInfo.getSender();
        request.setFrCountry(sender.getCountry());
        request.setFrProvince(sender.getProvince());
        request.setFrCity(sender.getCity());
        request.setFrTel(StringUtils.hasText(sender.getMobile()) ? sender.getMobile() : sender.getPhone());
        request.setfFrAddress(sender.getStreet());
        request.setFrName(sender.getName());
        request.setFrZipcode(sender.getPostCode());
        return request;
    }

    private boolean isResponseOk(YaXiangOrderResponse yaXiangOrderResponse) {
        String entityStr = yaXiangOrderResponse.getEntity();
        Entity entity = JsonMapper.fromJson(entityStr, Entity.class);
        return "0000".equalsIgnoreCase(entity.getCode());
    }

    @Override
    protected BaseLogisticsOrderRequest buildLogisticsOrderRequest(OrderRequest request, Map<String, String> configMap) {
        return null;
    }
}
