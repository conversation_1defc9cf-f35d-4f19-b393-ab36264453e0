package com.nsy.api.tms.service.external.refactor;

import cn.hutool.core.util.StrUtil;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.tms.annotation.LogisticsServiceHandler;
import com.nsy.api.tms.dao.entity.TmsLogisticsAccountEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.domain.order.OrderNewInfo;
import com.nsy.api.tms.enumeration.LogisticsMethodEnum;
import com.nsy.api.tms.enumeration.PackageStatusEnum;
import com.nsy.api.tms.enumeration.StockTransferTrackingStatusEnum;
import com.nsy.api.tms.logistics.cangsou.v2.TmsGetStockInOrderInfo;
import com.nsy.api.tms.logistics.goodcang.stockin.StockTransferTrackingResponse;
import com.nsy.api.tms.logistics.jiufang.JiuFangYunCangNewUtilsService;
import com.nsy.api.tms.logistics.jiufang.request.JiuFangYunCangCancelStockOutOrderRequest;
import com.nsy.api.tms.logistics.jiufang.request.JiuFangYunCangCreateStockOutOrderItemRequest;
import com.nsy.api.tms.logistics.jiufang.request.JiuFangYunCangCreateStockOutOrderRequest;
import com.nsy.api.tms.logistics.jiufang.request.JiuFangYunCangCreateStockinOrderItemRequest;
import com.nsy.api.tms.logistics.jiufang.request.JiuFangYunCangCreateStockinOrderRequest;
import com.nsy.api.tms.logistics.jiufang.request.JiuFangYunCangGetStockOutOrderRequest;
import com.nsy.api.tms.logistics.jiufang.request.JiuFangYunCangGetStockRequest;
import com.nsy.api.tms.logistics.jiufang.request.JiuFangYunCangGetStockinOrderRequest;
import com.nsy.api.tms.logistics.jiufang.request.JiuFangYunCangPrintStockInOrderRequest;
import com.nsy.api.tms.logistics.jiufang.request.WmsCreateStockInOrderRequest;
import com.nsy.api.tms.logistics.jiufang.request.WmsPrintStockInOrderRequest;
import com.nsy.api.tms.logistics.jiufang.request.WmsStockinOrderItemRequest;
import com.nsy.api.tms.logistics.jiufang.response.JiuFangYunCangBaseResponse;
import com.nsy.api.tms.logistics.jiufang.response.JiuFangYunCangCreateStockinOrderResponse;
import com.nsy.api.tms.logistics.jiufang.response.JiuFangYunCangGetStockOutOrderResponse;
import com.nsy.api.tms.logistics.jiufang.response.JiuFangYunCangGetStockResponse;
import com.nsy.api.tms.logistics.jiufang.response.JiuFangYunCangGetStockinOrderResponse;
import com.nsy.api.tms.request.OrderNewRequest;
import com.nsy.api.tms.request.OverseaStockRequest;
import com.nsy.api.tms.request.SkuDTO;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.response.SecondaryNumberResponse;
import com.nsy.api.tms.utils.JsonMapper;
import com.nsy.api.tms.utils.Lists;
import com.nsy.api.tms.utils.TypeUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-03-26 15:13
 */
@Service
@LogisticsServiceHandler(logisticsMethod = LogisticsMethodEnum.JIUFANGYUNCANG)
public class JiuFangYunCangNewService extends BaseLogisticsNewService implements InitializingBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(JiuFangYunCangNewService.class);

    public static final String SPACE_CODE = "space_code";

    public static final String PRINT_BOX_SIZE = "print_size";

    @Autowired
    private JiuFangYunCangNewUtilsService utilsService;

    @Override
    protected JiuFangYunCangCreateStockOutOrderRequest buildLogisticsOrderRequest(OrderNewRequest orderRequest, Map<String, String> configMap) {
        JiuFangYunCangCreateStockOutOrderRequest request = new JiuFangYunCangCreateStockOutOrderRequest();
        OrderNewInfo orderInfo = orderRequest.getOrderInfo();
        request.setWarehouseCode(configMap.get(SPACE_CODE));
        request.setReferenceNo(orderInfo.getBusinessKey());
        request.setName(orderInfo.getReceiver().getName());
        request.setShippingMethod(orderInfo.getLogisticsChannelCode());
        request.setPhone(StringUtils.isNotBlank(orderInfo.getReceiver().getPhone()) ? orderInfo.getReceiver().getPhone() : orderInfo.getReceiver().getMobile());
        request.setEmail(orderInfo.getReceiver().getEmail());
        request.setDistrict(orderInfo.getReceiver().getCounty());
        request.setZipcode(orderInfo.getReceiver().getPostCode());
        request.setCity(orderInfo.getReceiver().getCity());
        request.setProvince(orderInfo.getReceiver().getProvince());
        request.setCountryCode(orderInfo.getReceiver().getCountry());
        request.setVerify(1);
        request.setForceVerify(1);
        if (orderInfo.getReceiver().getStreet().length() <= 500) {
            request.setAddress1(StringUtils.substring(orderInfo.getReceiver().getStreet(), 0, 500));
        } else if (orderInfo.getReceiver().getStreet().length() > 500 && orderInfo.getReceiver().getStreet().length() <= 716) {
            request.setAddress1(StringUtils.substring(orderInfo.getReceiver().getStreet(), 0, 500));
            request.setAddress2(StringUtils.substring(orderInfo.getReceiver().getStreet(), 500, 716));
        } else if (orderInfo.getReceiver().getStreet().length() > 716 && orderInfo.getReceiver().getStreet().length() <= 932) {
            request.setAddress1(StringUtils.substring(orderInfo.getReceiver().getStreet(), 0, 500));
            request.setAddress2(StringUtils.substring(orderInfo.getReceiver().getStreet(), 500, 716));
            request.setAddress3(StringUtils.substring(orderInfo.getReceiver().getStreet(), 716, 932));
        } else if (orderInfo.getReceiver().getStreet().length() > 932) {
            throw new BusinessServiceException("收件人地址不能大于932个字符");
        }
        List<JiuFangYunCangCreateStockOutOrderItemRequest> orderItemList = new ArrayList<>();
        orderInfo.getOrderItemInfoList().forEach(detail -> {
            JiuFangYunCangCreateStockOutOrderItemRequest productDetail = new JiuFangYunCangCreateStockOutOrderItemRequest();
            productDetail.setProductSku(detail.getSku());
            productDetail.setQuantity(detail.getCount());
            productDetail.setProductName(detail.getCnName());
            productDetail.setProductNameEn(detail.getEnName());
            orderItemList.add(productDetail);
        });
        request.setItems(orderItemList);
        return request;
    }

    @Override
    protected String buildSerializedRequest(OrderNewRequest request, TmsLogisticsAccountEntity logisticsAccountEntity) {
        Map<String, String> configMap = buildAccountConfig(logisticsAccountEntity, request.getOrderInfo());
        JiuFangYunCangCreateStockOutOrderRequest soapRequest = buildLogisticsOrderRequest(request, configMap);
        return JsonMapper.toJson(soapRequest);
    }

    @Override
    public GenerateOrderResponse doRequest(OrderNewRequest orderRequest, Map<String, String> configMap, String requestContent) {
        TmsRequestLogEntity logEntity = requestLogService.recordBaseInfoLog(orderRequest.getOrderInfo());
        GenerateOrderResponse response = new GenerateOrderResponse();
        try {
            JiuFangYunCangBaseResponse<?> soapResponse = utilsService.postToJiuFangYunCang(requestContent, configMap, "createOrder", TypeUtils.dynamicType());
            if (Objects.nonNull(soapResponse) && !StringUtils.isEmpty(soapResponse.getOrderCode())) {
                GenerateOrderResponse.SuccessEntity successEntity = buildSuccessEntity(orderRequest.getOrderInfo(),
                        StringUtils.isEmpty(soapResponse.getTrackingNo()) ? soapResponse.getOrderCode() : soapResponse.getTrackingNo(),
                        "createOrder", soapResponse.getOrderCode(), null);
                response.setSuccessEntity(successEntity);
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, requestContent, JsonMapper.toJson(soapResponse), successEntity.getLogisticsNo());
            } else {
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, requestContent, JsonMapper.toJson(soapResponse == null ? "服务器错误！" : soapResponse), null);
                response.setError(buildError("400", ObjectUtils.isEmpty(soapResponse) ? "请求返回为空" : soapResponse.getMessage()));
            }
        } catch (Exception e) {
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, requestContent, e.getMessage(), "");
            LOGGER.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return response;
    }

    @Override
    public SecondaryNumberResponse getSecondaryNumber(TmsPackageEntity packageEntity) {
        if (!StringUtils.isBlank(packageEntity.getSecondaryNumber()) && !PackageStatusEnum.CREATED.getDesc().equalsIgnoreCase(packageEntity.getStatus())) {
            return getSecondaryNumberResponse(packageEntity);
        }
        Map<String, String> configMap = configService.getConfigMap(packageEntity.getKeyGroup());
        JiuFangYunCangGetStockOutOrderRequest request = new JiuFangYunCangGetStockOutOrderRequest();
        request.setOrderCode(packageEntity.getLogisticsTid());

        JiuFangYunCangGetStockOutOrderResponse response = utilsService.postToJiuFangYunCang(JsonMapper.toJson(request),
                configMap, "getOrderStatusByCode", TypeUtils.simpleType(JiuFangYunCangGetStockOutOrderResponse.class));

        if (response == null || StringUtils.isEmpty(response.getOrderStatus())) {
            LOGGER.info("九方云仓系统查询失败,失败原因为{}", ObjectUtils.isEmpty(response) ? "返回结果为空" : "返回信息错误");
            throw new RuntimeException(String.format("九方云仓系统查询订单失败：%s,失败原因：%s", packageEntity.getTid(), ObjectUtils.isEmpty(response) ? "返回结果为空" : "返回信息错误"));
        }
        if ("D".equalsIgnoreCase(response.getOrderStatus()) && PackageStatusEnum.CREATED.getDesc().equals(packageEntity.getStatus())) {
            packageEntity.setLogisticsNo(response.getTrackingNo());
            packageEntity.setSecondaryNumber(packageEntity.getLogisticsTid());
            packageEntity.setStatus(PackageStatusEnum.SHIPPED.getDesc());
            packageService.save(packageEntity);
            return getSecondaryNumberResponse(packageEntity);
        } else if ("X".equalsIgnoreCase(response.getOrderStatus())) {
            packageEntity.setStatus(PackageStatusEnum.CANCEL.getDesc());
            packageService.save(packageEntity);
            return super.getSecondaryNumber(packageEntity);
        }
        if (StrUtil.isNotBlank(packageEntity.getSecondaryNumber())) {
            return getSecondaryNumberResponse(packageEntity);
        } else {
            return super.getSecondaryNumber(packageEntity);
        }
    }


    @Override
    public void cancelOrder(TmsPackageEntity packageEntity) {
        if (StringUtils.isBlank(packageEntity.getLogisticsTid())) {
            throw new BusinessServiceException("查询不到对应的包裹信息,请检查!");
        }
        if (PackageStatusEnum.SHIPPED.getDesc().equalsIgnoreCase(packageEntity.getStatus())) {
            throw new BusinessServiceException("当前订单海外仓已发货无法取消,请检查!");
        }
        if (PackageStatusEnum.CANCEL.getDesc().equalsIgnoreCase(packageEntity.getStatus())) {
            return;
        }
        Map<String, String> configMap = configService.getConfigMap(packageEntity.getKeyGroup());
        JiuFangYunCangCancelStockOutOrderRequest request = new JiuFangYunCangCancelStockOutOrderRequest();
        request.setOrderCode(packageEntity.getLogisticsTid());
        JiuFangYunCangBaseResponse<?> soapResponse = utilsService.postToJiuFangYunCang(JsonMapper.toJson(request), configMap,
                "cancelOrder", TypeUtils.dynamicType());
        if (soapResponse == null || Objects.isNull(soapResponse.getCancelStatus())) {
            LOGGER.info("九方云仓系统取消订单失败,失败原因为{}", ObjectUtils.isEmpty(soapResponse) ? "返回结果为空" : soapResponse.getMessage());
            throw new RuntimeException(String.format("九方云仓系统取消订单失败：%s,失败原因：%s", packageEntity.getTid(), ObjectUtils.isEmpty(soapResponse) ? "返回结果为空" : soapResponse.getMessage()));
        }
        if (!Lists.newArrayList(2, 4).contains(soapResponse.getCancelStatus())) {
            throw new RuntimeException(String.format("九方云仓系统取消订单失败：%s,失败原因：%s", packageEntity.getTid(), this.convertCancelStatus(soapResponse.getCancelStatus())));
        }
        packageEntity.setStatus(PackageStatusEnum.CANCEL.getDesc());
        packageService.save(packageEntity);
    }

    private String convertCancelStatus(Integer cancelStatus) {
        if (cancelStatus == 3) {
            return "拦截失败";
        } else if (cancelStatus == 1) {
            return "订单已发起拦截申请,请通知海外仓人员拦截,如海外仓拦截成功再次取消可取消订单!";
        } else {
            return "海外仓未返回取消状态!";
        }
    }


    @Override
    public void afterPropertiesSet() throws Exception {

    }

    /**
     * 创建入库单
     *
     * @param request
     * @return
     */
    public String createStockInOrder(WmsCreateStockInOrderRequest request) {
        //获取物流信息
        Map<String, String> configMap = configService.getConfigMap(request.getLogisticsCompany());
        JiuFangYunCangCreateStockinOrderRequest soapRequest = new JiuFangYunCangCreateStockinOrderRequest();
        BeanUtils.copyProperties(request, soapRequest);
        //默认自送
        soapRequest.setIncomeType(0);
        //默认自发头程
        soapRequest.setReceivingType(0);
        //设置默认仓库
        soapRequest.setWarehouseCode(configMap.get(SPACE_CODE));
        soapRequest.setBulkCargoType(1);
        soapRequest.setVerify(1);
        List<JiuFangYunCangCreateStockinOrderItemRequest> itemList = new ArrayList<>();
        for (WmsStockinOrderItemRequest detail : request.getItemList()) {
            JiuFangYunCangCreateStockinOrderItemRequest item = new JiuFangYunCangCreateStockinOrderItemRequest();
            BeanUtils.copyProperties(detail, item);
            itemList.add(item);
        }
        //赋值明细信息
        soapRequest.setItems(itemList);
        JiuFangYunCangCreateStockinOrderResponse response = utilsService.postToJiuFangYunCang(JsonMapper.toJson(soapRequest),
                configMap, "createAsn", TypeUtils.simpleType(JiuFangYunCangCreateStockinOrderResponse.class));
        return response.getReceivingCode();
    }

    @Override
    public StockTransferTrackingResponse getStockInOrderInfo(Map<String, String> configMap, TmsGetStockInOrderInfo request) {
        JiuFangYunCangGetStockinOrderRequest soapRequest = new JiuFangYunCangGetStockinOrderRequest();
        soapRequest.setReceivingCode(request.getStockInReferenceNo());
        List<JiuFangYunCangGetStockinOrderResponse> response = utilsService.postToJiuFangYunCang(JsonMapper.toJson(soapRequest),
                configMap, "getAsnList", TypeUtils.listType(JiuFangYunCangGetStockinOrderResponse.class), true);
        StockTransferTrackingResponse trackingResponse = new StockTransferTrackingResponse();
        if (CollectionUtils.isEmpty(response) || ObjectUtils.isEmpty(response.get(0).getReceivingStatus())) {
            return trackingResponse;
        }
        if ("E".equalsIgnoreCase(response.get(0).getReceivingStatus())) {
            trackingResponse.setTrackingStatus(StockTransferTrackingStatusEnum.SHELVED.name());
        }
        response.get(0).getItems().forEach(skuInfo -> {
            StockTransferTrackingResponse.StockTrackingItem dto = new StockTransferTrackingResponse.StockTrackingItem();
            dto.setShelvedQty(skuInfo.getPutawayQuantity());
            dto.setStockOutQty(skuInfo.getQuantity());
            dto.setSku(skuInfo.getProductSku());
            trackingResponse.getTrackingItemList().add(dto);
        });
        return trackingResponse;
    }

    /**
     * 打印入库箱唛、装箱清单
     *
     * @param request
     * @return
     */
    public String printStockinOrderLabel(WmsPrintStockInOrderRequest request) {
        Map<String, String> configMap = configService.getConfigMap(request.getLogisticsCompany());
        JiuFangYunCangPrintStockInOrderRequest soapRequest = new JiuFangYunCangPrintStockInOrderRequest();
        soapRequest.setReceivingCode(request.getPlatformReferenceNo());
        soapRequest.setPdfType(request.getPdfType());
        soapRequest.setPdfSize("box".equalsIgnoreCase(request.getPdfType()) ? configMap.get(PRINT_BOX_SIZE) : "A4");
        soapRequest.setContentType("url");
        JiuFangYunCangBaseResponse<?> response = utilsService.postToJiuFangYunCang(JsonMapper.toJson(soapRequest),
                configMap, "getReceivingBoxPdfByCode", TypeUtils.dynamicType());
        if (StringUtils.isEmpty(response.getBase64())) {
            throw new RuntimeException(String.format("九方云仓系统打印%s失败订单号：%s,失败原因：返回结果为空", "box".equalsIgnoreCase(request.getPdfType()) ? "装箱清单" : "箱唛", request.getPlatformReferenceNo()));
        }
        return response.getBase64();
    }

    private SecondaryNumberResponse getSecondaryNumberResponse(TmsPackageEntity packageEntity) {
        SecondaryNumberResponse response1 = new SecondaryNumberResponse();
        response1.setLogisticsMethod(packageEntity.getLogisticsMethod());
        response1.setLogisticsChannelCode(packageEntity.getLogisticsChannelCode());
        response1.setSecondaryNumber(packageEntity.getLogisticsNo());
        response1.setPackageStatus(packageEntity.getStatus());
        return response1;
    }


    /**
     * 查询库存信息
     *
     * @param map
     * @param request
     * @return
     */
    @Override
    public List<SkuDTO> getOverseaSpaceProductStock(Map<String, String> map, OverseaStockRequest request) {
        List<SkuDTO> result = new ArrayList<>();
        request.setPageSize(100);
        JiuFangYunCangGetStockRequest soapRequest = new JiuFangYunCangGetStockRequest();
        soapRequest.setWarehouseCode(map.get(SPACE_CODE));
        if (!CollectionUtils.isEmpty(request.getSkuList())) {
            soapRequest.setProductSkuArr(request.getSkuList());
            List<JiuFangYunCangGetStockResponse> response = utilsService.postToJiuFangYunCang(JsonMapper.toJson(soapRequest),
                    map, "getProductInventory", TypeUtils.listType(JiuFangYunCangGetStockResponse.class), true);
            if (CollectionUtils.isEmpty(response)) {
                return super.getOverseaSpaceProductStock(map, request);
            }
            buildSkuDto(result, response);
            return result;
        }
        List<JiuFangYunCangGetStockResponse> response = utilsService.postToJiuFangYunCang(JsonMapper.toJson(soapRequest),
                map, "getProductInventory", TypeUtils.listType(JiuFangYunCangGetStockResponse.class), true);
        if (CollectionUtils.isEmpty(response)) {
            return super.getOverseaSpaceProductStock(map, request);
        }
        buildSkuDto(result, response);
        while (result.size() < response.size()) {
            request.setPageIndex(request.getPageIndex() + 1);
            List<JiuFangYunCangGetStockResponse> stockList = utilsService.postToJiuFangYunCang(JsonMapper.toJson(soapRequest),
                    map, "getProductInventory", TypeUtils.listType(JiuFangYunCangGetStockResponse.class), true);
            if (CollectionUtils.isEmpty(stockList)) {
                break;
            }
            buildSkuDto(result, stockList);
        }
        return result;
    }

    private void buildSkuDto(List<SkuDTO> result, List<JiuFangYunCangGetStockResponse> response) {
        response.forEach(item -> {
            SkuDTO dto = new SkuDTO();
            dto.setSku(item.getProductSku());
            dto.setQty(item.getSellable() + item.getReserved());
            dto.setSaleableQty(item.getSellable());
            result.add(dto);
        });
    }
}
