package com.nsy.api.tms.service.external;

import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.annotation.TmsHandler;
import com.nsy.api.tms.domain.OrderItemInfo;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@TmsHandler(logisticsMethod = "EDL")
public class EdlService extends HuaLeiService implements InitializingBean {

    @Value("${edl.createorder.url}")
    private String edlCreateOrderUrl;

    @Value("${edl.printLabel.url}")
    private String edlPrintLabelUrl;

    @Value("${edl.ip.url}")
    private String edlHostIP;

    @Value("${edl.authentication.url}")
    private String edlAuthenticationUrl;

    @Value("${edl.track.url}")
    private String edlTrackUrl;

    @Value("${edl.trackNumber.url}")
    String edlTrackingNumberUrl;
    @Override
    public void afterPropertiesSet() {
        this.createOrderUrl = edlCreateOrderUrl;
        this.printLabelUrl = edlPrintLabelUrl;
        this.hostIP = edlHostIP;
        this.authenticationUrl = edlAuthenticationUrl;
        this.labelFolder += "EDL/";
        this.ossLabelFolder += "EDL/";
        this.labelNameTemplate = "%sEDL-%s";
        this.trackUrl = edlTrackUrl;
        this.trackingNumberUrl = edlTrackingNumberUrl;
    }

    /**
     * 设置sku名称
     * <AUTHOR>
     * 2021-04-23
     */
    @Override
    protected String getSkuByByService(OrderItemInfo orderItem, String channelCode) {
        if (StringUtils.hasText(channelCode) && "3681".equals(channelCode)) {
            //Edl-E速宝小包渠道要求中文
            return orderItem.getCnName() + orderItem.getSku();
        }
        return orderItem.getEnName();
    }
}
