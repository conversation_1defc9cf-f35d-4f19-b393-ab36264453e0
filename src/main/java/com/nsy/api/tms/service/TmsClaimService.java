package com.nsy.api.tms.service;

import com.nsy.api.core.apicore.exception.InvalidRequestException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.dao.entity.TmsClaimEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsTagEntity;
import com.nsy.api.tms.domain.TmsClaim;
import com.nsy.api.tms.domain.TmsTag;
import com.nsy.api.tms.domain.excel.ClaimProcessTemplate;
import com.nsy.api.tms.enumeration.PackageTagEnum;
import com.nsy.api.tms.enumeration.TmsClaimStatusEnum;
import com.nsy.api.tms.repository.TmsClaimRepository;
import com.nsy.api.tms.request.TmsClaimAddRequest;
import com.nsy.api.tms.request.TmsClaimListRequest;
import com.nsy.api.tms.response.base.BaseListResponse;
import com.nsy.api.tms.service.privilege.AccessControlService;
import com.nsy.api.tms.service.privilege.PrivilegeAction;
import com.nsy.api.tms.service.privilege.Privileges;
import com.nsy.api.tms.utils.Validator;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: Woods Lee
 * @Date: 2019/12/19 16:30
 */
@Service
public class TmsClaimService {

    @Autowired
    TmsClaimRepository tmsClaimRepository;

    @Autowired
    AccessControlService accessControlService;
    @Autowired
    TmsTagService tmsTagService;

    @Autowired
    private PackageService packageService;

    public BaseListResponse<TmsClaim> listTmsClaim(TmsClaimListRequest request) {
        BaseListResponse<TmsClaim> response = new BaseListResponse<>();
        Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, "createDate"));
        Pageable pageable = PageRequest.of(request.getPageIndex() - 1, request.getPageSize(), sort);
        //Specification<TmsClaimEntity> specification = buildTmsClaimSpecification(request);
        //数据权限
        Specification<TmsClaimEntity> specification = accessControlService.doPrivileged(new PrivilegeAction<Specification<TmsClaimEntity>>() {
            @Override
            public Specification<TmsClaimEntity> admin() {
                return buildTmsClaimSpecification(request);
            }

            @Override
            public Specification<TmsClaimEntity> employee(Privileges privileges) {
                return buildTmsClaimSpecification(request).and((Specification<TmsClaimEntity>) (root, query, criteriaBuilder) ->
                        CollectionUtils.isEmpty(privileges.getStoreIds()) ? null : root.get("storeId").in(privileges.getStoreIds()));
            }
        });
        Page<TmsClaimEntity> pageClaimEntityList = tmsClaimRepository.findAll(specification, pageable);
        response.setTotalCount(pageClaimEntityList.getTotalElements());
        List<TmsClaimEntity> tmsClaimEntityList = new ArrayList<>(pageClaimEntityList.getContent());
        List<TmsClaim> tmsClaimList = tmsClaimEntityList.stream().map(entity -> {
            List<String> tagList = tmsTagService.findByLogisticsNo(entity.getLogisticsNo())
                    .stream().map(TmsTagEntity::getTag).distinct().collect(Collectors.toList());
            TmsClaim tmsClaim = new TmsClaim();
            BeanUtils.copyProperties(entity, tmsClaim);
            tmsClaim.setTag(tagList);
            return tmsClaim;
        }).collect(Collectors.toList());
        response.setContent(tmsClaimList);
        return response;
    }

    public void addTmsClaim(TmsClaimAddRequest request) {
        TmsClaimEntity tmsClaimEntity = null;
        //如果存在ID，则表示是更新
        if (Objects.nonNull(request.getId())) {
            tmsClaimEntity = tmsClaimRepository.findById(request.getId()).orElseThrow(() -> new InvalidRequestException("没有找到对应索赔记录"));
            tmsClaimEntity.setRemarks(request.getRemarks());
            tmsClaimEntity.setClaimAmount(TmsClaimStatusEnum.CLAIMED.getDesc().equalsIgnoreCase(request.getClaimStatus()) ? new BigDecimal(request.getClaimAmount()) : null);
            tmsClaimEntity.setClaimReason(request.getClaimReason());
            List<TmsTagEntity> tagEntityList = tmsTagService.findByLogisticsNoAndTag(request.getLogisticsNo(), PackageTagEnum.BREAKAGE.getDesc());
            if (PackageTagEnum.BREAKAGE.getDesc().equals(request.getClaimReason()) && tagEntityList.isEmpty()) {
                TmsTag tag = new TmsTag();
                tag.setTag(PackageTagEnum.BREAKAGE.getDesc());
                tag.setRemarks(request.getRemarks());
                tag.setLogisticsMethod(request.getLogisticsMethod());
                tag.setLogisticsType(request.getLogisticsType());
                tag.setLogisticsNo(request.getLogisticsNo());
                tag.setCreateBy(accessControlService.getRealName());
                tag.setTid(request.getTid());
                tmsTagService.saveTag(tag);
            }
            tmsClaimEntity.setUpdateBy(accessControlService.getRealName());
        } else {
            validateRequest(request);
            tmsClaimEntity = new TmsClaimEntity();
            BeanUtilsEx.copyProperties(request, tmsClaimEntity, "id");
            tmsClaimEntity.setCreateBy(accessControlService.getRealName());
            // 添加店铺id
            TmsPackageEntity tmsPackageEntity = packageService.getNoDeletedPackageByLogisticsNo(request.getLogisticsNo());
            if (!Objects.nonNull(tmsPackageEntity)) {
                throw new RuntimeException("找不到运单号为：" + request.getLogisticsNo() + "的包裹");
            }
            tmsClaimEntity.setStoreId(tmsPackageEntity.getStoreId());
        }
        tmsClaimEntity.setClaimStatus(request.getClaimStatus());
        tmsClaimRepository.save(tmsClaimEntity);

    }
    public List<String> findDeptNameList(TmsClaimListRequest request) {
        Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, "createDate"));
        Pageable pageable = PageRequest.of(request.getPageIndex() - 1, request.getPageSize(), sort);
        //数据权限
        Specification<TmsClaimEntity> specification = accessControlService.doPrivileged(new PrivilegeAction<Specification<TmsClaimEntity>>() {
            @Override
            public Specification<TmsClaimEntity> admin() {
                return buildTmsClaimSpecification(request);
            }

            @Override
            public Specification<TmsClaimEntity> employee(Privileges privileges) {
                return buildTmsClaimSpecification(request).and((Specification<TmsClaimEntity>) (root, query, criteriaBuilder) ->
                        CollectionUtils.isEmpty(privileges.getStoreIds()) ? null : root.get("storeId").in(privileges.getStoreIds()));
            }
        });
        Page<TmsClaimEntity> pageClaimEntityList = tmsClaimRepository.findAll(specification, pageable);
        List<TmsClaimEntity> tmsClaimEntityList = new ArrayList<>(pageClaimEntityList.getContent());
        return tmsClaimEntityList.stream().map(TmsClaimEntity::getDeptName).distinct().collect(Collectors.toList());
    }

    private void validateRequest(TmsClaimAddRequest request) {
        if (!StringUtils.hasText(request.getLogisticsNo())
                || !StringUtils.hasText(request.getTid())
                || !StringUtils.hasText(request.getLogisticsMethod())
                || Objects.isNull(request.getClaimAmount())
                || !StringUtils.hasText(request.getClaimReason())) {
            throw new InvalidRequestException("非法参数");
        }

    }

    private Specification<TmsClaimEntity> buildTmsClaimSpecification(TmsClaimListRequest request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.hasText(request.getClaimStatus())) {
                predicates.add(criteriaBuilder.equal(root.get("claimStatus"), request.getClaimStatus()));
            }
            if (StringUtils.hasText(request.getLogisticsNo())) {
                predicates.add(criteriaBuilder.like(root.get("logisticsNo"), request.getLogisticsNo() + "%"));
            }
            if (StringUtils.hasText(request.getTid())) {
                predicates.add(criteriaBuilder.like(root.get("tid"), request.getTid() + "%"));
            }
            if (StringUtils.hasText(request.getLogisticsMethod())) {
                predicates.add(criteriaBuilder.equal(root.get("logisticsMethod"), request.getLogisticsMethod()));
            }
            if (Objects.nonNull(request.getShipStartDate())) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("shipDate"), request.getShipStartDate()));
            }
            if (Objects.nonNull(request.getShipEndDate())) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("shipDate"), request.getShipEndDate()));
            }
            if (StringUtils.hasText(request.getStoreName())) {
                predicates.add(criteriaBuilder.like(root.get("storeName"), "%" + request.getStoreName() + "%"));
            }
            if (StringUtils.hasText(request.getLogisticsType())) {
                predicates.add(criteriaBuilder.equal(root.get("logisticsType"), request.getLogisticsType()));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()]));
        };
    }

    public void claimProcess(List<ClaimProcessTemplate> claimProcessTemplateList) {
        List<String> logisticsNos = new ArrayList<>();
        Validator.isValid(claimProcessTemplateList.size() > 2000 ? null : "yes", Objects::nonNull, "一次最多导2000条");
        for (ClaimProcessTemplate row : claimProcessTemplateList) {
            TmsClaimEntity claimEntity = tmsClaimRepository.findByLogisticsNoAndClaimStatus(row.getLogisticsNo(), TmsClaimStatusEnum.TOCLAIM.getDesc());
            if (claimEntity == null) {
                logisticsNos.add(row.getLogisticsNo());
                continue;
            }
            claimEntity.setClaimAmount(row.getClaimAmount());
            claimEntity.setClaimReason(row.getClaimReason());
            claimEntity.setClaimStatus(TmsClaimStatusEnum.CLAIMED.getDesc());
            claimEntity.setRemarks(row.getRemarks());
            claimEntity.setUpdateBy(accessControlService.getRealName());
            tmsClaimRepository.save(claimEntity);
        }
        Validator.isValid(logisticsNos.size(), attr -> attr == 0, String.format("运单号%s更新失败，找不到未索赔包裹,请检查", logisticsNos.toString()));
    }
}
