package com.nsy.api.tms.service.upload;

import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.enumeration.FreightModeEnum;
import com.nsy.api.tms.enumeration.PriceTypeEnum;
import com.nsy.api.tms.enumeration.QuartzUploadQueueTypeEnum;
import com.nsy.api.tms.enumeration.RecordTypeEnum;
import com.nsy.api.tms.repository.SystemConfigRepository;
import com.nsy.api.tms.request.LogisticsPriceRecordInsertRequest;
import com.nsy.api.tms.request.upload.LogisticsPriceRecordImport;
import com.nsy.api.tms.request.upload.UploadRequest;
import com.nsy.api.tms.response.upload.UploadResponse;
import com.nsy.api.tms.service.LogisticsPriceRecordService;
import com.nsy.api.tms.utils.JsonMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@Service
public class LogisticsPriceRecordUploadService implements IProcessUploadDataService {
    @Resource
    private LogisticsPriceRecordService logisticsPriceRecordService;
    @Resource
    SystemConfigRepository systemConfigRepository;



    @Override
    public QuartzUploadQueueTypeEnum type() {
        return QuartzUploadQueueTypeEnum.TMS_LOGISTICS_PRICE_RECORD;
    }

    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        UploadResponse response = new UploadResponse();
        if (!StringUtils.hasText(request.getDataJsonStr())) {
            return response;
        }
        List<LogisticsPriceRecordImport> importList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), LogisticsPriceRecordImport.class);
        if (CollectionUtils.isEmpty(importList)) {
            return response;
        }
        Map<String, String> map = logisticsPriceRecordService.getPredictConfigMap();
        List<LogisticsPriceRecordImport> errorList = new ArrayList<>();
        importList.forEach(row -> {
            try {
                LogisticsPriceRecordInsertRequest record = new LogisticsPriceRecordInsertRequest();
                record.setPrice(row.getPrice());
                record.setPriceType(PriceTypeEnum.getEnumByDesc(row.getPriceType()).name());
                record.setRecordType(RecordTypeEnum.getEnumByDesc(row.getRecordType()).name());
                record.setFreightMode(FreightModeEnum.getEnumByDesc(row.getFreightMode()).name());
                record.setPriceDateStr(row.getPriceDateStr());
                record.setRemark(row.getRemark());
                logisticsPriceRecordService.insert(record, map, request.getCreateBy());
            } catch (RuntimeException e) {
                row.setErrorMsg(e.getMessage());
                errorList.add(row);
            }
        });
        if (!CollectionUtils.isEmpty(errorList)) {
            response.setDataJsonStr(JsonMapper.toJson(errorList));
        }
        return response;
    }

}
