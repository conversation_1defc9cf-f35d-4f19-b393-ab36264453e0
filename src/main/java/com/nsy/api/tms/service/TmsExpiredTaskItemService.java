package com.nsy.api.tms.service;

import com.nsy.api.tms.dao.entity.TmsExpiredTaskEntity;
import com.nsy.api.tms.dao.entity.TmsExpiredTaskItemEntity;
import com.nsy.api.tms.repository.TmsExpiredTaskItemRepository;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.inject.Inject;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.Subquery;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: Woods Lee
 * @Date: 2019/12/27 16:32
 */
@Service
public class TmsExpiredTaskItemService {
    @Inject
    TmsExpiredTaskItemRepository taskItemRepository;

    public void saveExpiredTaskItemList(List<TmsExpiredTaskItemEntity> itemEntityList) {
        taskItemRepository.saveAll(itemEntityList);
    }

    public List<TmsExpiredTaskItemEntity> findByExpiredTaskId(Integer expiredTaskId) {
        return taskItemRepository.findByExpiredTaskId(expiredTaskId);
    }

    public List<TmsExpiredTaskItemEntity> getUnProcessedExpiredTask(String logisticsNo) {
        Specification<TmsExpiredTaskItemEntity> specification = buildUnProcessedExpiredTask(logisticsNo);
        return taskItemRepository.findAll(specification);
    }

    private Specification<TmsExpiredTaskItemEntity> buildUnProcessedExpiredTask(String logisticsNo) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicateList = new ArrayList<>();
            Predicate predicate = criteriaBuilder.equal(root.get("logisticsNo"), logisticsNo);
            predicateList.add(predicate);
            Subquery<TmsExpiredTaskEntity> subQuery = query.subquery(TmsExpiredTaskEntity.class);
            Root<TmsExpiredTaskEntity> subRoot = subQuery.from(TmsExpiredTaskEntity.class);
            subQuery.where(
                    criteriaBuilder.equal(subRoot.get("id"), root.get("expiredTaskId")),
                    criteriaBuilder.equal(subRoot.get("taskStatus"), TmsExpiredTaskEntity.PENDING_PROCESS)
            );
            predicateList.add(criteriaBuilder.exists(subQuery.select(subRoot)));
            return criteriaBuilder.and(predicateList.toArray(new Predicate[predicateList.size()]));
        };
    }


}
