package com.nsy.api.tms.service.external.refactor;

import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.dao.entity.TmsRouteRecordEntity;
import com.nsy.api.tms.enumeration.PackageStatusEnum;
import com.nsy.api.tms.logistics.zch.TrackItems;
import com.nsy.api.tms.logistics.zch.TrackResponse;
import com.nsy.api.tms.service.TmsRouteService;
import com.nsy.api.tms.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * HXD
 * 2021/5/27
 **/
@Service
public class ZhongChengHangTrackNewService extends ZhongChengHangNewService {

    @Value("${zhongchenghang.server.track}")
    private String zchServiceTrack;

    @Inject
    private RestTemplate restTemplate;

    @Autowired
    private TmsRouteService tmsRouteService;

    public void doTrack(TmsPackageEntity packageEntity) {
        TmsRequestLogEntity logEntity = requestLogService.recordBaseInfoLog(packageEntity);
        //更新tms_package 表路由查询次数
        packageEntity.setRouteSearchTimes(packageEntity.getRouteSearchTimes() + 1);
        packageService.save(packageEntity);
        Map<String, String> configMap = logisticsAccountService.buildAccountConfig(packageEntity.getLogisticsAccountId());
        HttpEntity<Object> httpEntity = new HttpEntity<>(buildHeader(configMap));
        String format = String.format(zchServiceTrack, packageEntity.getLogisticsChannelCode(), packageEntity.getLogisticsNo());
        ResponseEntity<TrackResponse> responseEntity = restTemplate.exchange(format, HttpMethod.GET, httpEntity, TrackResponse.class);
        TrackResponse trackResponse = responseEntity.getBody();
        if (trackResponse == null || !"success".equalsIgnoreCase(trackResponse.getStatusCode())) {
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, packageEntity.getLogisticsNo(), trackResponse == null
                    ? "中成航无法获取物流" : JsonMapper.toJson(trackResponse), packageEntity.getLogisticsNo());
            return;
        }
        processSuccessTrackReply(packageEntity, trackResponse);
        requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, packageEntity.getLogisticsNo(),
                JsonMapper.toJson(trackResponse), packageEntity.getLogisticsNo());
    }

    private void processSuccessTrackReply(TmsPackageEntity packageEntity, TrackResponse response) {
        if (CollectionUtils.isEmpty(response.getReturnDatas().get(0).getItems())) {
            return;
        }
        //1. 更新tms_package状态，同时，写路由表（当前查询路由信息与上一条路由表记录状态一致，则不重新插入）
        List<TmsRouteRecordEntity> routeRecordEntityList = tmsRouteService.findByLogisticsNoOrderByCreateDateDesc(packageEntity.getLogisticsNo());
        List<TrackItems> detailList = Optional.ofNullable(response.getReturnDatas().get(0).getItems()).orElse(new ArrayList<>());
        detailList.sort(Comparator.comparing(TrackItems::getDateTime));
        Collections.reverse(detailList);
        TrackItems latestDetails = detailList.get(0);
        // 新增或更新路由
        if (routeRecordEntityList.isEmpty()
                || !(routeRecordEntityList.get(0).getAcceptTime().compareTo(latestDetails.getDateTime()) == 0
                && routeRecordEntityList.get(0).getAcceptAddress().equalsIgnoreCase(latestDetails.getLocation())
                && routeRecordEntityList.get(0).getRemark().equalsIgnoreCase(latestDetails.getInfo()))) {
            persistTmsRouteRecord(packageEntity.getLogisticsNo(), latestDetails, packageEntity.getStatus());
        }
        packageEntity.setRouteLastUpdateTime(latestDetails.getDateTime());
        if ("派送妥投".equals(latestDetails.getInfo()) || "Delivered".equalsIgnoreCase(latestDetails.getInfo())) {
            packageEntity.setStatus(PackageStatusEnum.DELIVERED.getDesc());
        } else {
            packageEntity.setStatus(PackageStatusEnum.TRANSIT.getDesc());
        }
        packageService.save(packageEntity);

    }

    private void persistTmsRouteRecord(String logisticsNo, TrackItems details, String status) {
        TmsRouteRecordEntity routeRecordEntity = new TmsRouteRecordEntity();
        routeRecordEntity.setStatus(status);
        routeRecordEntity.setAcceptAddress(details.getLocation());
        routeRecordEntity.setAcceptTime(details.getDateTime());
        routeRecordEntity.setLogisticsNo(logisticsNo);
        routeRecordEntity.setRemark(details.getInfo());
        tmsRouteService.save(routeRecordEntity);
    }
}
