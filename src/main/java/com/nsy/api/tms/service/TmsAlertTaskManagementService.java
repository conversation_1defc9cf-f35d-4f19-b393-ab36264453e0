package com.nsy.api.tms.service;

import com.nsy.api.tms.dao.entity.TmsAlertTaskRemarkEntity;
import com.nsy.api.tms.domain.LogisticsMethodCount;
import com.nsy.api.tms.domain.TmsAlertTaskReturnInfo;
import com.nsy.api.tms.request.TmsAlertHandleRequest;
import com.nsy.api.tms.request.TmsAlertTaskListRequest;
import com.nsy.api.tms.response.base.BaseListResponse;

import java.util.List;

public interface TmsAlertTaskManagementService {

    BaseListResponse<TmsAlertTaskReturnInfo> findList(TmsAlertTaskListRequest request);

    void handle(TmsAlertHandleRequest request);

    List<LogisticsMethodCount> getUnHandleTaskCount(TmsAlertTaskListRequest alertTaskListRequest);

    void oneKeyStartTasks(TmsAlertTaskListRequest alertTaskListRequest);

    void batchProcessTasks(TmsAlertTaskListRequest request);

    List<TmsAlertTaskRemarkEntity> getTmsAlertTaskRemarks(Integer taskId);

    void addRemark(TmsAlertTaskRemarkEntity tmsAlertTaskRemarkEntity);
}
