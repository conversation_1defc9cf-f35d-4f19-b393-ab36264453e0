package com.nsy.api.tms.service;

import com.nsy.api.tms.dao.entity.TmsTimelinessConfigEntity;
import com.nsy.api.tms.repository.TmsTimelinessConfigRepository;
import org.springframework.stereotype.Service;

import javax.inject.Inject;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
public class TmsTimelinessConfigService {

    @Inject
    TmsTimelinessConfigRepository configRepository;


    public Integer getConfigDays(String logisticsMethod, String countryCode, String configType) {
        Integer configDays;
        TmsTimelinessConfigEntity tmsTimelinessConfigEntity = configRepository.findByLogisticsMethodAndCountryCodeAndType(logisticsMethod, countryCode, configType);
        if (Objects.isNull(tmsTimelinessConfigEntity)) {
            List<TmsTimelinessConfigEntity> configEntityList = configRepository.findByLogisticsMethodAndType(logisticsMethod, configType);
            configDays = configEntityList.stream()
                    .map(configEntity -> configEntity.getDays())
                    .sorted().findFirst().orElse(null);
        } else {
            configDays = tmsTimelinessConfigEntity.getDays();
        }
        return configDays;
    }
}
