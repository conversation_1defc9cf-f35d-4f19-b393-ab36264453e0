package com.nsy.api.tms.service;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.constants.CountryCodeConstant;
import com.nsy.api.tms.constants.TmsCommonConstant;
import com.nsy.api.tms.dao.entity.TmsAsyncRequestQueueEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsAccountEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsChannelConfigEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsCompanyEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsFreightCountryMappingEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsFreightEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.domain.Address;
import com.nsy.api.tms.domain.OrderItemInfo;
import com.nsy.api.tms.domain.order.OrderNewInfo;
import com.nsy.api.tms.enumeration.AmazonBuyShippingInfoEnum;
import com.nsy.api.tms.enumeration.ChannelTypeEnum;
import com.nsy.api.tms.enumeration.LocationEnum;
import com.nsy.api.tms.enumeration.LogisticsMethodEnum;
import com.nsy.api.tms.enumeration.LogisticsTypeEnum;
import com.nsy.api.tms.enumeration.PackageLogEnum;
import com.nsy.api.tms.external.oms.OmsApiService;
import com.nsy.api.tms.external.oms.response.SaStoreDetailResponse;
import com.nsy.api.tms.filter.TenantContext;
import com.nsy.api.tms.logistics.cangsou.v2.TmsGetStockInOrderInfo;
import com.nsy.api.tms.logistics.goodcang.stockin.StockTransferTrackingResponse;
import com.nsy.api.tms.logistics.goodcang.stockin.WmsCreateStockInOrderRequest;
import com.nsy.api.tms.repository.BdShipperAddressRepository;
import com.nsy.api.tms.repository.TmsLogisticsAccountRepository;
import com.nsy.api.tms.repository.TmsLogisticsChannelConfigRepository;
import com.nsy.api.tms.repository.TmsLogisticsCompanyRepository;
import com.nsy.api.tms.repository.TmsLogisticsFreightCountryMappingRepository;
import com.nsy.api.tms.repository.TmsLogisticsFreightRepository;
import com.nsy.api.tms.request.LabelRequest;
import com.nsy.api.tms.request.OrderNewRequest;
import com.nsy.api.tms.request.OverseaStockRequest;
import com.nsy.api.tms.request.SkuDTO;
import com.nsy.api.tms.request.StatusRequest;
import com.nsy.api.tms.request.address.DeliveryAddressMatchRequest;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.response.PrintLabelResponse;
import com.nsy.api.tms.response.address.LogisticsAddressResponse;
import com.nsy.api.tms.service.address.LogisticsDeliveryAddressMappingService;
import com.nsy.api.tms.service.external.refactor.BaseLogisticsNewService;
import com.nsy.api.tms.service.external.refactor.winit.model.OverseaPrintShipmentLabelRequest;
import com.nsy.api.tms.service.privilege.AccessControlService;
import com.nsy.api.tms.utils.JsonMapper;
import com.nsy.api.tms.utils.Validator;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.StringTokenizer;
import java.util.stream.Collectors;


@Service
public class TmsNewService {
    private static final String COMMA = ",";

    private static final String TMS_METHOD = "tms_method";

    @Autowired
    TmsLogisticsAccountRepository logisticsAccountRepository;
    @Autowired
    TmsLogisticsAccountService logisticsAccountService;
    @Autowired
    TmsLogisticsCompanyRepository logisticsCompanyRepository;
    @Autowired
    BdShipperAddressRepository shipperAddressRepository;
    @Autowired
    LogisticsHelper logisticsHelper;
    @Autowired
    TmsConfigService tmsConfigService;
    @Autowired
    TmsLogisticsChannelConfigRepository logisticsChannelConfigRepository;
    @Autowired
    PackageService packageService;
    @Autowired
    TmsAsyncRequestQueueService asyncRequestQueueService;
    @Autowired
    TmsLogisticsFreightRepository logisticsFreightRepository;
    @Inject
    PackageLogService packageLogService;
    @Inject
    AccessControlService accessControlService;
    @Autowired
    TmsLogisticsFreightCountryMappingRepository logisticsFreightCountryMappingRepository;
    @Resource
    OmsApiService omsApiService;
    @Autowired
    LogisticsDeliveryAddressMappingService addressMappingService;

    private static final org.slf4j.Logger LOGGER = LoggerFactory.getLogger(TmsNewService.class);

    @Transactional
    public GenerateOrderResponse generateOrder(OrderNewRequest request) {
        LOGGER.info("新面单接口请求体：{}", JsonMapper.toJson(request.getOrderInfo()));
        OrderNewInfo orderInfo = request.getOrderInfo();
        validLocation(orderInfo);
        // 是否重置物流公司
        resetChannelName(orderInfo);
        // 最终渠道
        TmsLogisticsChannelConfigEntity byLogisticsChannelName = null;
        TmsLogisticsCompanyEntity logisticsCompanyEntity;
        if (StringUtils.hasText(orderInfo.getLogisticsChannelName())) {
            // 获取渠道配置
            byLogisticsChannelName = logisticsChannelConfigRepository.findByLogisticsChannelNameAndStatusAndLocation(orderInfo.getLogisticsChannelName(), StatusRequest.ENABLE, orderInfo.getLocation());
            if (byLogisticsChannelName == null) {
                logisticsCompanyEntity = getLogisticsCompany(orderInfo);
            } else {
                logisticsCompanyEntity = logisticsCompanyRepository.findByLogisticsCompanyAndStatusAndLocation(byLogisticsChannelName.getLogisticsCompany(), StatusRequest.ENABLE, orderInfo.getLocation());
            }
        } else {
            logisticsCompanyEntity = logisticsCompanyRepository.findByLogisticsCompanyAndStatusAndLocation(orderInfo.getLogisticsCompany(), StatusRequest.ENABLE, orderInfo.getLocation());
        }
        if (Objects.isNull(logisticsCompanyEntity))
            throw new BusinessServiceException(String.format("物流公司/渠道【%s】被禁用或不存在", StringUtils.hasText(orderInfo.getLogisticsChannelName()) ? orderInfo.getLogisticsChannelName() : orderInfo.getLogisticsCompany()));
        if (Objects.nonNull(logisticsCompanyEntity.getLimitWeight()) && orderInfo.getWeight() > logisticsCompanyEntity.getLimitWeight().doubleValue())
            throw new BusinessServiceException(String.format("物流公司【%s】限重【%s】", logisticsCompanyEntity.getLogisticsCompany(), logisticsCompanyEntity.getLimitWeight()));
        validCompanyOrChannel(orderInfo, logisticsCompanyEntity);
        orderInfo.setLogisticsCompany(logisticsCompanyEntity.getLogisticsCompany());
        orderInfo.setLogisticsType(logisticsCompanyEntity.getLogisticsType());
        //判断购买配送
        byLogisticsChannelName = checkAmazonBuyShipping(logisticsCompanyEntity, request, byLogisticsChannelName);

        if (byLogisticsChannelName == null)
            byLogisticsChannelName = matchLogisticsChannel(orderInfo);

        if (Objects.isNull(byLogisticsChannelName)) throw new BusinessServiceException("匹配不到可用渠道,请切换物流");
        // 取账号
        TmsLogisticsAccountEntity logisticsAccountEntity = getLogisticsAccount(orderInfo);
        if (Objects.isNull(logisticsAccountEntity)) throw new RuntimeException("匹配不到物流账号");

        // 匹配发货地址
        if (Objects.equals(orderInfo.getFromErp(), 1) || orderInfo.getSender() == null)
            orderInfo.setSender(buildSenderAddress(orderInfo, byLogisticsChannelName, logisticsAccountEntity));
        // 发件人国家为空则设置国家为CN
        buildOrderInfo(orderInfo, byLogisticsChannelName, logisticsCompanyEntity, logisticsAccountEntity);

        String logisticsMethod = logisticsCompanyEntity.getLogisticsMethod();
        if (orderInfo.getOverseaSpace())
            logisticsMethod = byLogisticsChannelName.getLogisticsMethod();
        BaseLogisticsNewService logisticsService = logisticsHelper.getLogisticsService(logisticsMethod, byLogisticsChannelName.getLogisticsChannelCode());
        orderInfo.setTmsTid(logisticsService.buildTmsTid(orderInfo));

        // 数据预处理
        logisticsService.preDeal(request);
        // 小包重新设置品名
        resetProductName(request.getOrderInfo(), byLogisticsChannelName);
        // 3、VALIDATE REQUEST
        logisticsService.validateOrderRequest(request);

        GenerateOrderResponse response = logisticsService.logisticsNoPushCheck(request);
        if (response != null) {
            return response;
        }

        // 4、GENERATE ORDER
        // A、reorder=1 && async=0 --> 删除旧的包裹，同步获取运单
        // B、reorder=1 && async=1 --> 删除旧的包裹，删除旧的异步队列，生成新的异步队列
        // C、reorder=0 && async=0 --> 获取旧的包裹
        // D、reorder=0 && async=1 --> 从队列获取结果
        if (request.getReOrder() == 1 && request.getAsync() == 0) {
            deleteOldPackage(orderInfo);
            return logisticsService.syncGenerateOrder(request, logisticsAccountEntity);
        } else if (request.getReOrder() == 1 && request.getAsync() == 1) {
            deleteOldPackage(orderInfo);
            deleteOldAsyncRequestQueue(orderInfo);
            return logisticsService.asyncGenerateOrder(request, logisticsAccountEntity);
        } else if (request.getReOrder() == 0 && request.getAsync() == 0) {
            return logisticsService.buildOrderResponse(orderInfo);
        } else if (request.getReOrder() == 0 && request.getAsync() == 1) {
            return logisticsService.buildAsyncOrderResponse(orderInfo);
        }
        return null;
    }

    private void resetProductName(OrderNewInfo orderInfo, TmsLogisticsChannelConfigEntity byLogisticsChannelName) {
        if (!StrUtil.equals(byLogisticsChannelName.getChannelType(), ChannelTypeEnum.INTERNATIONAL_SMALL_PARCEL.name())) {
            // 只有小包渠道的 才设置品名
            return;
        }
        List<OrderItemInfo> orderItemInfoList = orderInfo.getOrderItemInfoList();
        orderItemInfoList.forEach(item -> {
            if (StrUtil.isNotBlank(item.getFabricType())) {
                String mainMaterial = getMainMaterial(item.getFabricType());
                item.setCnName(mainMaterial + (item.getCnName() == null ? "" : item.getCnName()));
            }
            if (StrUtil.isNotBlank(item.getUserGenderCn())) {
                item.setCnName(item.getUserGenderCn() + (item.getCnName() == null ? "" : item.getCnName()));
            }
            if (StrUtil.isNotBlank(item.getFabricTypeEn())) {
                String mainMaterial = getMainMaterial(item.getFabricTypeEn());
                item.setEnName(mainMaterial + (item.getEnName() == null ? " " : item.getEnName()));
            }
            if (StrUtil.isNotBlank(item.getUserGenderEn())) {
                item.setEnName(item.getUserGenderEn() + " " + (item.getEnName() == null ? " " : item.getEnName()));
            }
        });
    }

    // 返回占比最大的成份
    private static String getMainMaterial(String materialString) {
        StringTokenizer tokenizer = new StringTokenizer(materialString, "+");
        String mainMaterial = "";
        int maxPercentage = 0;
        // 如果不包含 +，则只有一个
        while (tokenizer.hasMoreTokens()) {
            String token = tokenizer.nextToken();
            String[] parts = token.split("%");
            if (parts.length == 1) {
                maxPercentage = 0;
                mainMaterial = parts[0];
                continue;
            }
            if (NumberUtil.isNumber(parts[0])) {
                int percentage = NumberUtil.parseInt(parts[0]);
                String material = parts[1];
                if (percentage > maxPercentage) {
                    maxPercentage = percentage;
                    mainMaterial = material;
                }
            } else if (NumberUtil.isNumber(parts[1])) {
                int percentage = NumberUtil.parseInt(parts[1]);
                String material = parts[0];
                if (percentage > maxPercentage) {
                    maxPercentage = percentage;
                    mainMaterial = material;
                }
            }
        }

        return mainMaterial;
    }

    private void validLocation(OrderNewInfo orderInfo) {
        if (StrUtil.isBlank(orderInfo.getLocation())) {
            // 先默认泉州
            orderInfo.setLocation(LocationEnum.QUANZHOU.name());
        }
        orderInfo.setLocation(orderInfo.getLocation().toUpperCase(Locale.ROOT));
    }

    private Address buildSenderAddress(OrderNewInfo orderInfo, TmsLogisticsChannelConfigEntity byLogisticsChannelName, TmsLogisticsAccountEntity logisticsAccountEntity) {
        DeliveryAddressMatchRequest request = new DeliveryAddressMatchRequest();
        request.setLogisticsChannel(byLogisticsChannelName.getLogisticsChannelName());
        request.setLogisticsCompany(byLogisticsChannelName.getLogisticsCompany());
        request.setStoreId(orderInfo.getStoreId());
        request.setLocation(orderInfo.getLocation());
        request.setDefaultIfAbsent(Boolean.TRUE);
        List<LogisticsAddressResponse> logisticsAddressResponses = addressMappingService.matchAddress(request);
        if (CollectionUtils.isEmpty(logisticsAddressResponses)) {
            throw new BusinessServiceException("未配置发货地址！");
        }
        LogisticsAddressResponse response = logisticsAddressResponses.stream()
                .filter(it -> Objects.equals(it.getDefaultShippingAddress(), 1)).findFirst().orElse(logisticsAddressResponses.get(0));
        Address address = new Address();
        address.setStreet(response.getShipperAddress());
        address.setCity(response.getShipperCity());
        address.setCountry(response.getShipperCountryCode());
        address.setCompany(response.getShipperCompanyName());
        address.setCounty(response.getShipperDistrict());
        address.setPostCode(response.getShipperZip());
        address.setMobile(response.getShipperMobile());
        address.setProvince(response.getShipperState());
        address.setName(response.getShipperName());
        address.setPhone(response.getShipperPhone());
        address.setTaxNumber(logisticsAccountEntity.getTaxNumber());
        return address;
    }

    /**
     * 判断返回可走购买配送，否则走其他渠道
     * a. amazon_buy_shipping_flag != DISABLE
     * b. 判断是否存在购买配送渠道
     * c. 店铺配置是可走购买配送
     * d. WMS库存余量大于等于≥5
     *
     * @param logisticsCompanyEntity
     * @param request
     * @param byLogisticsChannelName
     * @return
     */
    private TmsLogisticsChannelConfigEntity checkAmazonBuyShipping(TmsLogisticsCompanyEntity logisticsCompanyEntity, OrderNewRequest request, TmsLogisticsChannelConfigEntity byLogisticsChannelName) {
        LOGGER.info("判断购买配送");
        //a. amazon_buy_shipping_flag != DISABLE
        if (!AmazonBuyShippingInfoEnum.NONE.getValue().equals(request.getOrderInfo().getAmazonBuyShippingInfo())) {
            LOGGER.info("非【未设置】不走购买配送 {} ", request.getOrderInfo().getTid());
            return byLogisticsChannelName;
        }

        //c. 店铺配置是可走购买配送
        SaStoreDetailResponse saStoreInfo = omsApiService.getSaStoreInfo(request.getOrderInfo().getStoreId(), logisticsCompanyEntity.getLocation());
        if (Objects.isNull(saStoreInfo))
            throw new BusinessServiceException(String.format("找不到店铺 %s", request.getOrderInfo().getStoreId()));
        if (1 != saStoreInfo.getIsFbmOrderPurchaseDeliver()) {
            LOGGER.info("店铺未设置购买配送 {} {} ", request.getOrderInfo().getTid(), request.getOrderInfo().getStoreId());
            return byLogisticsChannelName;
        }

        List<TmsLogisticsChannelConfigEntity> logisticsChannelConfigList = logisticsChannelConfigRepository.findByLogisticsCompanyAndStatusAndLocation(logisticsCompanyEntity.getLogisticsCompany(), StatusRequest.ENABLE, request.getOrderInfo().getLocation());
        List<TmsLogisticsChannelConfigEntity> buyShippingChannelList = logisticsChannelConfigList.stream().filter(config -> config.getLogisticsChannelName().contains("购买配送")).collect(Collectors.toList());
        //b. 判断是否存在购买配送渠道
        if (buyShippingChannelList.isEmpty()) {
            LOGGER.info("购买配送渠道未新增 {} {} ", request.getOrderInfo().getTid(), logisticsCompanyEntity.getLogisticsCompany());
            return byLogisticsChannelName;
        }

        TmsLogisticsChannelConfigEntity buyShippingChannelConfig = buyShippingChannelList.get(0);
        if (Objects.isNull(buyShippingChannelConfig.getAmazonShippingServiceId()))
            throw new BusinessServiceException(String.format("%s 的 amazon service id 未配置", buyShippingChannelConfig.getLogisticsChannelName()));
        request.getOrderInfo().setAmazonShippingServiceId(buyShippingChannelConfig.getAmazonShippingServiceId());

        //d. WMS库存余量大于等于≥5
        boolean anyNotEnough = request.getOrderInfo().getOrderItemInfoList().stream().anyMatch(itemInfo -> itemInfo.getStock() < 5);
        if (anyNotEnough) {
            LOGGER.info("WMS库存余量大于等于<5 {} {}", request.getOrderInfo().getTid(), request.getOrderInfo().getStoreId());
            return byLogisticsChannelName;
        }

        return buyShippingChannelConfig;
    }

    private TmsLogisticsCompanyEntity getLogisticsCompany(OrderNewInfo orderInfo) {
        LOGGER.info("无法找到渠道，先找公司");
        if (StringUtils.hasText(orderInfo.getLogisticsCompany())) {
            return logisticsCompanyRepository.findByLogisticsCompanyAndStatusAndLocation(orderInfo.getLogisticsCompany(), StatusRequest.ENABLE, orderInfo.getLocation());
        } else {
            return logisticsCompanyRepository.findByLogisticsCompanyAndStatusAndLocation(orderInfo.getLogisticsChannelName(), StatusRequest.ENABLE, orderInfo.getLocation());
        }
    }

    private void buildOrderInfo(OrderNewInfo orderInfo, TmsLogisticsChannelConfigEntity byLogisticsChannelName, TmsLogisticsCompanyEntity logisticsCompanyEntity, TmsLogisticsAccountEntity logisticsAccountEntity) {
        if (!StringUtils.hasText(orderInfo.getSender().getCountry()))
            orderInfo.getSender().setCountry("CN");
        orderInfo.setLogisticsMethod(logisticsCompanyEntity.getLogisticsMethod());
        if (orderInfo.getOverseaSpace()) {
            orderInfo.setLogisticsMethod(byLogisticsChannelName.getLogisticsMethod());
        }
        orderInfo.setLogisticsAccountId(logisticsAccountEntity.getId());
        orderInfo.setExpressType(byLogisticsChannelName.getExpressType());
        if (!StringUtils.hasText(orderInfo.getLocation())) {
            LOGGER.error("该单的location为空");
            orderInfo.setLocation(byLogisticsChannelName.getLocation());
        } else {
            orderInfo.setLocation(orderInfo.getLocation().toUpperCase(Locale.ROOT));
        }
        orderInfo.setPrintCode(byLogisticsChannelName.getPrintCode());
        orderInfo.setLogisticsChannelCode(byLogisticsChannelName.getLogisticsChannelCode());
        orderInfo.setLogisticsChannelName(byLogisticsChannelName.getLogisticsChannelName());
    }

    /**
     * 特殊的物流渠道，特殊处理
     *
     * <AUTHOR>
     * 2023-08-24
     */
    private void resetChannelName(OrderNewInfo orderInfo) {
        if (StringUtils.hasText(orderInfo.getLogisticsChannelName()) && StrUtil.equalsAnyIgnoreCase(orderInfo.getLogisticsChannelName(),
                TmsCommonConstant.DEAL_LOGISTICS.toArray(new String[TmsCommonConstant.DEAL_LOGISTICS.size()]))) {
            // 特殊处理物流公司
            orderInfo.setLogisticsCompany(orderInfo.getLogisticsChannelName());
            orderInfo.setLogisticsChannelName(null);
        }
    }

    private void validCompanyOrChannel(OrderNewInfo orderInfo, TmsLogisticsCompanyEntity logisticsCompanyEntity) {
        if (StrUtil.contains(orderInfo.getLogisticsChannelName(), "4px-联邮通")) {
            throw new BusinessServiceException("[4px-联邮通]已禁用，请选择[联邮通美国普货专线]");
        }
        // 美线小包只能走云途：1 小包 ，2非海外仓，3美国，4泉州或厦门
        if (StrUtil.equals(logisticsCompanyEntity.getLogisticsType(), LogisticsTypeEnum.INTERNATIONAL_PACKAGE.name()) && !orderInfo.getOverseaSpace()
                && !logisticsCompanyEntity.getLogisticsMethod().equals(LogisticsMethodEnum.YUNTU.getLogisticsMethod()) && !logisticsCompanyEntity.getLogisticsMethod().equals(LogisticsMethodEnum.FOURPX.getLogisticsMethod())
                && StrUtil.equalsAnyIgnoreCase(orderInfo.getLocation(), LocationEnum.QUANZHOU.name(), LocationEnum.XIAMEN.name())
                && (StrUtil.equalsAnyIgnoreCase(orderInfo.getReceiveCountryCode(), CountryCodeConstant.US) || StrUtil.equalsAnyIgnoreCase(orderInfo.getReceiver().getCountry(), CountryCodeConstant.US))) {
            throw new BusinessServiceException("美线小包只能走[云途]或者[4PX联邮通]！");
        }
    }

    /**
     * 渠道匹配
     *
     * @param orderInfo
     * @return
     */
    public TmsLogisticsChannelConfigEntity matchLogisticsChannel(OrderNewInfo orderInfo) {
        LOGGER.info("匹配物流渠道");
        List<TmsLogisticsChannelConfigEntity> logisticsChannelConfigEntityList = logisticsChannelConfigRepository.findByLogisticsCompanyAndStatusAndLocation(orderInfo.getLogisticsCompany(), StatusRequest.ENABLE, orderInfo.getLocation());
        if (CollectionUtils.isEmpty(logisticsChannelConfigEntityList))
            throw new BusinessServiceException(String.format("物流公司【%s】没有配置可用渠道", orderInfo.getLogisticsCompany()));
        // orderInfo.getWeight单位是kg, 物流限重配置的是g
        double weight = orderInfo.getWeight() * 1000;
        if (logisticsChannelConfigEntityList.size() == 1) {
            return singleMatchLogisticsChannel(logisticsChannelConfigEntityList, orderInfo, weight);
        } else {
            return multipleMatchLogisticsChannel(logisticsChannelConfigEntityList, orderInfo, weight);
        }
    }

    /**
     * case1:一个物流公司只有一个渠道
     *
     * @param logisticsChannelConfigEntityList
     * @param orderInfo
     * @param weight
     * @return
     */
    private TmsLogisticsChannelConfigEntity singleMatchLogisticsChannel(List<TmsLogisticsChannelConfigEntity> logisticsChannelConfigEntityList, OrderNewInfo orderInfo, double weight) {
        TmsLogisticsChannelConfigEntity logisticsChannelConfigEntity = logisticsChannelConfigEntityList.get(0);
        if (Objects.nonNull(logisticsChannelConfigEntity.getMaxDeclaredValue()) && orderInfo.getCustomsValueAmount() > logisticsChannelConfigEntity.getMaxDeclaredValue().doubleValue()) {
            throw new RuntimeException(String.format("物流公司【%s】唯一的渠道【%s】,申报价值上限【%s】,本单申报加工【%s】超出，请切换物流重新获取", logisticsChannelConfigEntity.getLogisticsCompany(), logisticsChannelConfigEntity.getLogisticsChannelCode(), logisticsChannelConfigEntity.getMaxDeclaredValue(), orderInfo.getCustomsValueAmount()));
        }
        if ((Objects.nonNull(logisticsChannelConfigEntity.getDefaultMatchMinWeight()) && weight < logisticsChannelConfigEntity.getDefaultMatchMinWeight().doubleValue())
                || (Objects.nonNull(logisticsChannelConfigEntity.getDefaultMatchMaxWeight()) && weight >= logisticsChannelConfigEntity.getDefaultMatchMaxWeight().doubleValue())) {
            throw new BusinessServiceException(String.format("物流公司【%s】唯一的渠道【%s】,重量区间需在【%s-%s】(g)之间，本单重量【%s】(g)不适配，请切换物流重新获取", logisticsChannelConfigEntity.getLogisticsCompany(), logisticsChannelConfigEntity.getLogisticsChannelCode(), logisticsChannelConfigEntity.getDefaultMatchMinWeight(), logisticsChannelConfigEntity.getDefaultMatchMaxWeight(), weight));
        }
        TmsLogisticsFreightEntity logisticsFreightEntity = logisticsFreightRepository.findByLogisticsCompanyAndLogisticsChannelCode(logisticsChannelConfigEntity.getLogisticsCompany(), logisticsChannelConfigEntity.getLogisticsChannelCode());
        if (Objects.isNull(logisticsFreightEntity)) return logisticsChannelConfigEntity;
        List<TmsLogisticsFreightCountryMappingEntity> freightCountryMappingEntityList = logisticsFreightCountryMappingRepository.findByLogisticsFreightId(logisticsFreightEntity.getId());
        if (CollectionUtils.isEmpty(freightCountryMappingEntityList)) return logisticsChannelConfigEntity;
        List<String> countryList = new ArrayList<>();
        freightCountryMappingEntityList.forEach(mappingEntity -> countryList.addAll(Arrays.asList(mappingEntity.getCountries().split(COMMA))));
        if (!countryList.contains(orderInfo.getReceiveCountryCode()))
            throw new BusinessServiceException(String.format("物流公司【%s】唯一的渠道【%s】,不支持走国家【%s】，请切换物流重新获取", orderInfo.getLogisticsCompany(), logisticsChannelConfigEntity.getLogisticsChannelCode(), orderInfo.getReceiveCountryCode()));
        return logisticsChannelConfigEntity;
    }

    /**
     * case2: 一个物流公司对应多个渠道
     *
     * @param logisticsChannelConfigEntityList
     * @param orderInfo
     * @param weight
     * @return
     */
    private TmsLogisticsChannelConfigEntity multipleMatchLogisticsChannel(List<TmsLogisticsChannelConfigEntity> logisticsChannelConfigEntityList, OrderNewInfo orderInfo, double weight) {
        TmsLogisticsChannelConfigEntity logisticsChannelConfigEntity = null;
        // 1.申报价值比较
        List<TmsLogisticsChannelConfigEntity> tempLogisticsChannelConfigEntityList = logisticsChannelConfigEntityList.stream()
                .filter(t -> Objects.isNull(t.getMaxDeclaredValue()) || Objects.nonNull(t.getMaxDeclaredValue()) && orderInfo.getCustomsValueAmount() <= t.getMaxDeclaredValue().doubleValue())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tempLogisticsChannelConfigEntityList)) {
            throw new BusinessServiceException(String.format("物流公司【%s】下的渠道,申报价值均不符合,请切换物流重新获取", orderInfo.getLogisticsCompany()));
        }
        // 2.限重比较
        tempLogisticsChannelConfigEntityList = tempLogisticsChannelConfigEntityList.stream()
                .filter(t -> (Objects.isNull(t.getDefaultMatchMinWeight()) || weight > t.getDefaultMatchMinWeight().doubleValue())
                        && (Objects.isNull(t.getDefaultMatchMaxWeight()) || weight <= t.getDefaultMatchMaxWeight().doubleValue()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tempLogisticsChannelConfigEntityList)) {
            throw new BusinessServiceException(String.format("物流公司【%s】下的渠道,重量范围均不匹配,请切换物流重新获取", orderInfo.getLogisticsCompany()));
        }
        // 3.国家比较
        List<String> allLogisticsChannelCodeList = tempLogisticsChannelConfigEntityList.stream().map(TmsLogisticsChannelConfigEntity::getLogisticsChannelCode).distinct().collect(Collectors.toList());
        List<TmsLogisticsFreightEntity> logisticsFreightEntityList = logisticsFreightRepository.findByLogisticsCompanyAndLogisticsChannelCodeIn(orderInfo.getLogisticsCompany(), allLogisticsChannelCodeList);
        // 有配置物流价格的渠道
        List<String> logisticsChannelCodeList = logisticsFreightEntityList.stream().map(TmsLogisticsFreightEntity::getLogisticsChannelCode).collect(Collectors.toList());
        // 过滤出没有配置物流价格的渠道
        List<TmsLogisticsChannelConfigEntity> noFreightChannelConfigList = tempLogisticsChannelConfigEntityList.stream().filter(t -> !logisticsChannelCodeList.contains(t.getLogisticsChannelCode())).collect(Collectors.toList());
        for (TmsLogisticsFreightEntity logisticsFreightEntity : logisticsFreightEntityList) {
            List<TmsLogisticsFreightCountryMappingEntity> freightCountryMappingEntityList = logisticsFreightCountryMappingRepository.findByLogisticsFreightId(logisticsFreightEntity.getId());
            TmsLogisticsChannelConfigEntity tempChannelConfigEntity = tempLogisticsChannelConfigEntityList.stream().filter(t -> t.getLogisticsChannelCode().equals(logisticsFreightEntity.getLogisticsChannelCode()) && t.getLogisticsCompany().equals(logisticsFreightEntity.getLogisticsCompany())).findFirst().orElse(new TmsLogisticsChannelConfigEntity());
            if (CollectionUtils.isEmpty(freightCountryMappingEntityList)) {
                noFreightChannelConfigList.add(tempChannelConfigEntity);
                continue;
            }
            List<String> countryList = new ArrayList<>();
            freightCountryMappingEntityList.forEach(mappingEntity -> countryList.addAll(Arrays.asList(mappingEntity.getCountries().split(COMMA))));
            if (countryList.contains(orderInfo.getReceiveCountryCode())) {
                logisticsChannelConfigEntity = tempChannelConfigEntity;
                break;
            }
        }
        if (Objects.isNull(logisticsChannelConfigEntity)) {
            if (CollectionUtils.isEmpty(noFreightChannelConfigList)) {
                throw new BusinessServiceException(String.format("物流公司【%s】下的渠道,均不支持走国家【%s】,请切换物流重新获取", orderInfo.getLogisticsCompany(), orderInfo.getReceiveCountryCode()));
            }
            logisticsChannelConfigEntity = noFreightChannelConfigList.get(0);
        }
        return logisticsChannelConfigEntity;
    }

    public PrintLabelResponse printLabel(LabelRequest labelRequest) {
        Validator.isValid(labelRequest, attr -> StringUtils.hasText(labelRequest.getLogisticsNo()), "运单号不能为空");
        TmsPackageEntity packageEntity = null;
        if (!StringUtils.hasText(labelRequest.getLogisticsNo())) {
            throw new BusinessServiceException("物流单号必须有值");
        }
        packageEntity = packageService.getNoDeletedPackageByLogisticsNo(labelRequest.getLogisticsNo());
        if (packageEntity == null) {
            List<TmsPackageEntity> packages = packageService.getNoDeletedPackageBySecondaryNumber(labelRequest.getLogisticsNo());
            if (CollectionUtils.isEmpty(packages)) {
                throw new BusinessServiceException("找不到物流信息");
            }
            packageEntity = packages.stream().sorted(Comparator.comparing(TmsPackageEntity::getId, Comparator.reverseOrder())).collect(Collectors.toList()).get(0);
        }
        if (packageEntity.getLogisticsAccountId() == null && StringUtils.hasText(packageEntity.getLogisticsCompany())) {
            // 获得物流账号id
            buildPackageAccountId(packageEntity);
        }
        BaseLogisticsNewService logisticsService = logisticsHelper.getLogisticsService(packageEntity.getLogisticsMethod(), packageEntity.getLogisticsChannelCode());
        return logisticsService.printLabel(packageEntity);
    }

    public void syncDeliveryInfo(String logisticsNo) {
        try {
            TmsPackageEntity packageEntity = null;
            if (!StringUtils.hasText(logisticsNo)) {
                return;
            }
            packageEntity = packageService.getNoDeletedPackageByLogisticsNo(logisticsNo);
            if (packageEntity == null) {
                List<TmsPackageEntity> packages = packageService.getNoDeletedPackageBySecondaryNumber(logisticsNo);
                if (CollectionUtils.isEmpty(packages)) {
                    LOGGER.info("查询不到包裹信息,物流单号：{}", logisticsNo);
                    //找不到物流信息
                    return;
                }
                packageEntity = packages.stream().sorted(Comparator.comparing(TmsPackageEntity::getId, Comparator.reverseOrder())).collect(Collectors.toList()).get(0);
            }
            if (packageEntity.getLogisticsAccountId() == null && StringUtils.hasText(packageEntity.getLogisticsCompany())) {
                // 获得物流账号id
                buildPackageAccountId(packageEntity);
            }
            BaseLogisticsNewService logisticsService = logisticsHelper.getLogisticsService(packageEntity.getLogisticsMethod(), packageEntity.getLogisticsChannelCode());
            logisticsService.syncDeliveryInfo(packageEntity);
        } catch (Exception e) {
            LOGGER.error("同步发货信息失败", e);
        }
    }

    public void cancelOrderSync(String logisticsNo) {
        TmsPackageEntity packageEntity = null;
        if (!StringUtils.hasText(logisticsNo)) {
            return;
        }
        packageEntity = packageService.getNoDeletedPackageByLogisticsNo(logisticsNo);
        if (packageEntity == null) {
            List<TmsPackageEntity> packages = packageService.getNoDeletedPackageBySecondaryNumber(logisticsNo);
            if (CollectionUtils.isEmpty(packages)) {
                LOGGER.info("查询不到包裹信息,物流单号：{}", logisticsNo);
                //找不到物流信息
                return;
            }
            packageEntity = packages.stream().sorted(Comparator.comparing(TmsPackageEntity::getId, Comparator.reverseOrder())).collect(Collectors.toList()).get(0);
        }
        if (packageEntity.getLogisticsAccountId() == null && StringUtils.hasText(packageEntity.getLogisticsCompany())) {
            // 获得物流账号id
            buildPackageAccountId(packageEntity);
        }
        BaseLogisticsNewService logisticsService = logisticsHelper.getLogisticsService(packageEntity.getLogisticsMethod(), packageEntity.getLogisticsChannelCode());
        logisticsService.cancelOrder(packageEntity);
    }

    private void buildPackageAccountId(TmsPackageEntity packageEntity) {
        List<TmsLogisticsAccountEntity> logisticsAccountEntityList = logisticsAccountRepository.findByLogisticsCompanyAndStatus(packageEntity.getLogisticsCompany(), StatusRequest.ENABLE);
        if (CollectionUtils.isEmpty(logisticsAccountEntityList)) {
            LOGGER.error(packageEntity.getLogisticsCompany() + "该物流公司没有账号");
            // 该物流公司没有账号
            return;
        }
        if (logisticsAccountEntityList.size() == 1) {
            packageEntity.setLogisticsAccountId(logisticsAccountEntityList.get(0).getId());
            LOGGER.info(packageEntity.getLogisticsCompany() + "物流公司匹配账号：" + logisticsAccountEntityList.get(0).getLogisticsAccount());
            return;
        }
        TmsLogisticsAccountEntity defaultAccountByLogisticsCompany = logisticsAccountService.findDefaultAccountByLogisticsCompany(packageEntity.getLogisticsCompany());
        if (defaultAccountByLogisticsCompany != null) {
            packageEntity.setLogisticsAccountId(defaultAccountByLogisticsCompany.getId());
            LOGGER.info(packageEntity.getLogisticsCompany() + "物流公司匹配账号：" + defaultAccountByLogisticsCompany.getLogisticsAccount());
        }
    }

    private TmsLogisticsAccountEntity getLogisticsAccount(OrderNewInfo orderInfo) {
        TmsLogisticsAccountEntity logisticsAccountEntity;
        if (StringUtils.hasText(orderInfo.getLogisticsAccount())) {
            logisticsAccountEntity = logisticsAccountRepository.findByLogisticsAccountAndLogisticsCompanyAndStatusAndLocation(orderInfo.getLogisticsAccount(), orderInfo.getLogisticsCompany(), StatusRequest.ENABLE, orderInfo.getLocation());
        } else {
            List<TmsLogisticsAccountEntity> logisticsAccountEntityList = logisticsAccountRepository.findByLogisticsCompanyAndStatusAndLocation(orderInfo.getLogisticsCompany(), StatusRequest.ENABLE, orderInfo.getLocation());
            if (CollectionUtils.isEmpty(logisticsAccountEntityList)) {
                throw new BusinessServiceException(String.format("物流公司【%s】没有配置账号信息", orderInfo.getLogisticsCompany()));
            }
            if (logisticsAccountEntityList.size() == 1) {
                logisticsAccountEntity = logisticsAccountEntityList.get(0);
            } else {
                logisticsAccountEntity = logisticsAccountService.findDefaultAccountByLogisticsCompany(orderInfo.getLogisticsCompany());
            }
        }
        return logisticsAccountEntity;
    }

    private void deleteOldAsyncRequestQueue(OrderNewInfo orderInfo) {
        List<TmsAsyncRequestQueueEntity> asyncRequestQueueEntityList = asyncRequestQueueService.getByBusinessKey(orderInfo.getBusinessKey());
        asyncRequestQueueEntityList = asyncRequestQueueEntityList.stream().peek(entity -> entity.setDeleteFlag(TmsAsyncRequestQueueEntity.DELETED)).collect(Collectors.toList());
        asyncRequestQueueService.updateAsyncRequestQueueEntityList(asyncRequestQueueEntityList);
    }

    // 左海和易境通不删除package
    private void deleteOldPackage(OrderNewInfo orderInfo) {
        List<TmsPackageEntity> packageEntityList = packageService.findByBusinessKey(orderInfo.getBusinessKey());
        List<TmsPackageEntity> packageEntityUpdateList = new ArrayList<>();
        packageEntityList.forEach(entity -> {
            if (StrUtil.equals(entity.getLogisticsMethod(), LogisticsMethodEnum.ZUOHAI_YIJINGTONG.getLogisticsMethod())) {
                return;
            }
            entity.setDeleteFlag(TmsPackageEntity.DELETED);
            packageLogService.addLog(entity, PackageLogEnum.DELETE_PACKAGE, accessControlService.getRealName(), accessControlService.getIpAddressHeader());
            packageEntityUpdateList.add(entity);
            // 删除51tracking的导入?
        });
        if (!CollectionUtils.isEmpty(packageEntityUpdateList)) {
            packageService.updatePackageList(packageEntityUpdateList);
        }
    }


    // 查询所有的sku库存
    public List<SkuDTO> getOverseaSpaceProductStock(OverseaStockRequest request) {
        Map<String, String> map = tmsConfigService.getConfigMap(request.getSpaceName());
        request.setLocation(TenantContext.getTenant());
        String method = map.get(TMS_METHOD);
        if (StrUtil.isBlank(method)) {
            throw new BusinessServiceException("未配置对应海外仓的方法");
        }
        BaseLogisticsNewService logisticsService = logisticsHelper.getLogisticsService(method, "");
        return logisticsService.getOverseaSpaceProductStock(map, request);
    }

    public String printShipmentLabel(OverseaPrintShipmentLabelRequest request) {
        Map<String, String> map = tmsConfigService.getConfigMap(request.getLogisticsCompany());
        String method = map.get(TMS_METHOD);
        if (StrUtil.isBlank(method)) {
            throw new BusinessServiceException("未配置对应海外仓的方法");
        }
        BaseLogisticsNewService logisticsService = logisticsHelper.getLogisticsService(method, "");
        return logisticsService.printShipmentLabel(map, request);
    }

    public String createStockInOrder(WmsCreateStockInOrderRequest request) {
        Map<String, String> map = tmsConfigService.getConfigMap(request.getSpace());
        String method = map.get(TMS_METHOD);
        if (StrUtil.isBlank(method)) {
            throw new BusinessServiceException("未配置对应海外仓的方法");
        }
        BaseLogisticsNewService logisticsService = logisticsHelper.getLogisticsService(method, "");
        return logisticsService.createStockInOrder(map, request);
    }


    public StockTransferTrackingResponse getStockInOrderInfo(TmsGetStockInOrderInfo request) {
        Map<String, String> map = tmsConfigService.getConfigMap(request.getSpace());
        String method = map.get(TMS_METHOD);
        if (StrUtil.isBlank(method)) {
            throw new BusinessServiceException("未配置对应海外仓的方法");
        }
        BaseLogisticsNewService logisticsService = logisticsHelper.getLogisticsService(method, "");
        return logisticsService.getStockInOrderInfo(map, request);
    }
}
