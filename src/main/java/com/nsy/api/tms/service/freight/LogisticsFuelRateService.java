package com.nsy.api.tms.service.freight;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.tms.dao.entity.TmsLogisticsCompanyEntity;
import com.nsy.api.tms.dao.entity.freight.LogisticsFuelRateEntity;
import com.nsy.api.tms.mapper.freight.LogisticsFuelRateMapper;
import com.nsy.api.tms.repository.TmsLogisticsCompanyRepository;
import com.nsy.api.tms.request.freight.LogisticsFuelRatePageRequest;
import com.nsy.api.tms.request.upload.LogisticsFuelRateImport;
import com.nsy.api.tms.response.PageResponse;
import com.nsy.api.tms.response.freight.LogisticsFuelRateResponse;
import com.nsy.api.tms.service.privilege.AccessControlService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 快递燃油费率表(LogisticsFuelRate)服务层
 *
 * <AUTHOR>
 * @since 2023-09-14 14:54:15
 */
@Service
public class LogisticsFuelRateService extends ServiceImpl<LogisticsFuelRateMapper, LogisticsFuelRateEntity> {

    @Resource
    AccessControlService accessControlService;
    @Autowired
    TmsLogisticsCompanyRepository logisticsCompanyRepository;

    /**
     * 分页查询
     */
    public PageResponse<LogisticsFuelRateResponse> queryByPage(LogisticsFuelRatePageRequest request) {
        PageResponse<LogisticsFuelRateResponse> pageResponse = new PageResponse<>();
        Page<LogisticsFuelRateEntity> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<LogisticsFuelRateEntity> iPage = page(page, buildPageQueryWrapper(request));
        List<LogisticsFuelRateResponse> list = iPage.getRecords().stream().map(entity -> {
            LogisticsFuelRateResponse resp = new LogisticsFuelRateResponse();
            BeanUtils.copyProperties(entity, resp);
            TmsLogisticsCompanyEntity logisticsCompanyEntity = logisticsCompanyRepository.findById(entity.getLogisticsCompanyId())
                    .orElseThrow(() -> new BusinessServiceException(String.format("物流公司【%s】不存在！", entity.getLogisticsCompanyId())));
            resp.setLogisticsCompanyName(logisticsCompanyEntity.getLogisticsCompany());
            return resp;
        }).collect(Collectors.toList());
        pageResponse.setContent(list);
        pageResponse.setTotalCount(iPage.getTotal());
        return pageResponse;
    }


    // 构造列表查询条件
    private LambdaQueryWrapper<LogisticsFuelRateEntity> buildPageQueryWrapper(LogisticsFuelRatePageRequest request) {
        LambdaQueryWrapper<LogisticsFuelRateEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(request.getLogisticsCompanyId() != null, LogisticsFuelRateEntity::getLogisticsCompanyId, request.getLogisticsCompanyId());
        if (request.getStartDate() != null) {
            wrapper.ge(LogisticsFuelRateEntity::getStartDate, request.getStartDate());
        }
        if (request.getEndDate() != null) {
            wrapper.le(LogisticsFuelRateEntity::getEndDate, request.getEndDate());
        }
        wrapper.orderByDesc(LogisticsFuelRateEntity::getId);
        return wrapper;
    }

    public LogisticsFuelRateEntity getByDateAndCompanyId(Integer logisticsCompanyId, Date deliveryDate) {
        LambdaQueryWrapper<LogisticsFuelRateEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LogisticsFuelRateEntity::getLogisticsCompanyId, logisticsCompanyId);
        wrapper.le(LogisticsFuelRateEntity::getStartDate, deliveryDate);
        wrapper.ge(LogisticsFuelRateEntity::getEndDate, deliveryDate);
        wrapper.orderByDesc(LogisticsFuelRateEntity::getId).last("limit 1");
        return getOne(wrapper);
    }

    public void importFuel(LogisticsFuelRateImport row) {
        TmsLogisticsCompanyEntity logisticsCompany = logisticsCompanyRepository.findByLogisticsCompany(row.getLogisticsCompany());
        if (logisticsCompany == null) {
            throw new BusinessServiceException("找不到该物流公司：" + row.getLogisticsCompany());
        }
        LogisticsFuelRateEntity entity = new LogisticsFuelRateEntity();
        BeanUtilsEx.copyProperties(row, entity);
        entity.setLogisticsCompanyId(logisticsCompany.getId());
        entity.setCreateBy(accessControlService.getUserName());
        save(entity);
    }
}
