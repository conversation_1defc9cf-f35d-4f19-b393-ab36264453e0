package com.nsy.api.tms.service.external;

import cn.hutool.core.codec.Base64;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nsy.api.core.apicore.exception.InvalidRequestException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.annotation.TmsHandler;
import com.nsy.api.tms.dao.entity.TmsLogisticsChannelConfigEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.domain.Address;
import com.nsy.api.tms.domain.OrderInfo;
import com.nsy.api.tms.domain.OrderItemInfo;
import com.nsy.api.tms.domain.UpsInfo;
import com.nsy.api.tms.enumeration.LogisticsMethodEnum;
import com.nsy.api.tms.enumeration.LogisticsTypeEnum;
import com.nsy.api.tms.logistics.ups.ship.request.BillShipper;
import com.nsy.api.tms.logistics.ups.ship.request.Contact;
import com.nsy.api.tms.logistics.ups.ship.request.Email;
import com.nsy.api.tms.logistics.ups.ship.request.FreightCharges;
import com.nsy.api.tms.logistics.ups.ship.request.InternationalForm;
import com.nsy.api.tms.logistics.ups.ship.request.InvoiceLineTotal;
import com.nsy.api.tms.logistics.ups.ship.request.LabelImageFormat;
import com.nsy.api.tms.logistics.ups.ship.request.LabelSpecification;
import com.nsy.api.tms.logistics.ups.ship.request.Notification;
import com.nsy.api.tms.logistics.ups.ship.request.OtherCharges;
import com.nsy.api.tms.logistics.ups.ship.request.PackageWeight;
import com.nsy.api.tms.logistics.ups.ship.request.Packaging;
import com.nsy.api.tms.logistics.ups.ship.request.PaymentInformation;
import com.nsy.api.tms.logistics.ups.ship.request.Phone;
import com.nsy.api.tms.logistics.ups.ship.request.Product;
import com.nsy.api.tms.logistics.ups.ship.request.ReferenceNumber;
import com.nsy.api.tms.logistics.ups.ship.request.Request;
import com.nsy.api.tms.logistics.ups.ship.request.ShipFrom;
import com.nsy.api.tms.logistics.ups.ship.request.ShipPackage;
import com.nsy.api.tms.logistics.ups.ship.request.ShipServiceType;
import com.nsy.api.tms.logistics.ups.ship.request.ShipTo;
import com.nsy.api.tms.logistics.ups.ship.request.Shipment;
import com.nsy.api.tms.logistics.ups.ship.request.ShipmentCharge;
import com.nsy.api.tms.logistics.ups.ship.request.ShipmentRequest;
import com.nsy.api.tms.logistics.ups.ship.request.ShipmentServiceOption;
import com.nsy.api.tms.logistics.ups.ship.request.Shipper;
import com.nsy.api.tms.logistics.ups.ship.request.TransactionReference;
import com.nsy.api.tms.logistics.ups.ship.request.Unit;
import com.nsy.api.tms.logistics.ups.ship.request.UnitOfMeasurement;
import com.nsy.api.tms.logistics.ups.ship.request.UpsShipmentRequest;
import com.nsy.api.tms.logistics.ups.ship.response.PackageResults;
import com.nsy.api.tms.logistics.ups.ship.response.PrimaryErrorCode;
import com.nsy.api.tms.logistics.ups.ship.response.ShipFaultResponse;
import com.nsy.api.tms.logistics.ups.ship.response.multiplepieces.MultiplePiecesShipmentResults;
import com.nsy.api.tms.logistics.ups.ship.response.multiplepieces.UpsMultiplePiecesShipmentResponse;
import com.nsy.api.tms.logistics.ups.ship.response.onepiece.OnePieceShipmentResults;
import com.nsy.api.tms.logistics.ups.ship.response.onepiece.UpsOnePieceShipmentResponse;
import com.nsy.api.tms.logistics.ups.track.ServiceAccessToken;
import com.nsy.api.tms.logistics.ups.track.UpsSecurity;
import com.nsy.api.tms.logistics.ups.track.UpsUsernameToken;
import com.nsy.api.tms.request.OrderRequest;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.service.AliyunOssService;
import com.nsy.api.tms.service.PackageService;
import com.nsy.api.tms.service.TmsConfigService;
import com.nsy.api.tms.service.TmsRequestLogService;
import com.nsy.api.tms.utils.FileUtils;
import com.nsy.api.tms.utils.Img2Base64Utils;
import com.nsy.api.tms.utils.JsonMapper;
import com.nsy.api.tms.utils.Validator;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: Woods Lee
 * @Date: 2019/1/8 11:24
 */
@Service
@TmsHandler(logisticsMethod = "UPS")
public class UpsService extends BaseLogisticsService implements InitializingBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(UpsService.class);
    //测试环境
    @Value("${ups.server.url}")
    private String serverUrl;

    @Inject
    TmsRequestLogService tmsRequestLogService;

    @Inject
    PackageService packageService;

    @Inject
    AliyunOssService aliyunOssService;

    @Inject
    UpsTrackService upsTrackService;

    @Inject
    TmsConfigService tmsConfigService;

    @Inject
    RestTemplate restTemplate;

    @Value("${label.folder}")
    String labelFolder;

    @Value("${oss.label.folder}")
    public String ossLabelFolder;

    public static final String LABEL_NAME = "%sUPS-%s.png";
    public static final String CONFIG_TOKEN = "ups_token";
    public static final String CONFIG_USER_NAME = "ups_userName";
    public static final String CONFIG_PASSWORD = "ups_password";
    public static final String LABEL_NAME_TEMPLATE = "%sUPS-%s";



    protected UpsShipmentRequest buildLogisticsOrderRequest(OrderRequest request, Map<String, String> configMap) {
        OrderInfo orderInfo = request.getOrderInfo();
        UpsShipmentRequest upsShipmentRequest = new UpsShipmentRequest();
        //身份验证
        UpsSecurity security = new UpsSecurity();
        ServiceAccessToken accessToken = new ServiceAccessToken();
        accessToken.setAccessLicenseNumber(configMap.get(CONFIG_TOKEN));
        security.setServiceAccessToken(accessToken);
        UpsUsernameToken usernameToken = new UpsUsernameToken();
        usernameToken.setUsername(configMap.get(CONFIG_USER_NAME));
        usernameToken.setPassword(configMap.get(CONFIG_PASSWORD));
        security.setUsernameToken(usernameToken);
//        upsShipmentRequest.setUpsSecurity(security);
        ShipmentRequest shipmentRequest = new ShipmentRequest();
        upsShipmentRequest.setShipmentRequest(shipmentRequest);
        //request
        addRequestInfos(shipmentRequest);
        //labelSpecification
        addLabelSpecificationInfos(shipmentRequest);
        Shipment shipment = new Shipment();
        //ShipTo
        shipment.setShipTo(addShipToInfo(orderInfo));
        shipment.setDescription(orderInfo.getUpsInfo().getPackageDescription());
        shipmentRequest.setShipment(shipment);
        //Shipper
        addShipperInfos(shipment, orderInfo);
        //ShipFrom
        addShipFromInfo(shipment, orderInfo);
        //PaymentInformation
        addPaymentInformationInfo(shipment, orderInfo.getUpsInfo());
        //ShipServiceType
        addShipServiceTypeInfo(shipment, orderInfo.getUpsInfo());
        //package
        addPackageInfo(shipment, orderInfo);
        // 发票
        if (orderInfo.getUpsInfo().getUseUpsInvoice()) {
            addInvoiceLineTotalInfo(shipment, orderInfo.getUpsInfo());
        }
        addShipmentServiceOptionInfo(shipment, orderInfo);
        addReferenceNumberInfo(shipment, orderInfo);
        return upsShipmentRequest;
    }

    private void addReferenceNumberInfo(Shipment shipment, OrderInfo orderInfo) {
        ReferenceNumber referenceNumber = new ReferenceNumber();
        referenceNumber.setValue(orderInfo.getUpsInfo().getInvoiceNumber());
        shipment.setReferenceNumber(referenceNumber);
    }


    private void addShipmentServiceOptionInfo(Shipment shipment, OrderInfo orderInfo) {
        ShipmentServiceOption shipmentServiceOption = new ShipmentServiceOption();
        shipmentServiceOption.setInternationalForms(buildInternationalForm(orderInfo));
        shipmentServiceOption.setNotification(addNotification(orderInfo));
        shipment.setShipmentServiceOptions(shipmentServiceOption);
    }

    private InternationalForm buildInternationalForm(OrderInfo orderInfo) {
        if (orderInfo.getUpsInfo().getUseUpsInvoice()) {
            InternationalForm internationalForm = new InternationalForm();
            internationalForm.setFormType("01");
            internationalForm.setCurrencyCode("USD");
            internationalForm.setInvoiceDate(DateFormatUtils.format(new Date(), "yyyyMMdd"));
            internationalForm.setInvoiceNumber(orderInfo.getUpsInfo().getInvoiceNumber());
            internationalForm.setReasonForExport("Sale");
            addProductInfo(internationalForm, orderInfo.getOrderItemInfoList());
            addContactInfo(internationalForm, orderInfo);
            addFreightCharges(internationalForm, orderInfo);
            addOtherCharges(internationalForm, orderInfo);
            return internationalForm;
        }
        return null;
    }

    private List<Notification> addNotification(OrderInfo orderInfo) {
        List<Notification> notifications = new ArrayList<>();
        Notification notification1 = new Notification();
        notification1.setNotificationCode("6"); //6发运通知，7异常通知，8派送通知
        notification1.setEmail(addEmail(orderInfo));
        notifications.add(notification1);

        Notification notification2 = new Notification();
        notification2.setNotificationCode("7"); //6发运通知，7异常通知，8派送通知
        notification2.setEmail(addEmail(orderInfo));
        notifications.add(notification2);

        Notification notification3 = new Notification();
        notification3.setNotificationCode("8"); //6发运通知，7异常通知，8派送通知
        notification3.setEmail(addEmail(orderInfo));
        notifications.add(notification3);
        return notifications;
    }

    private Email addEmail(OrderInfo orderInfo) {
        Email email = new Email();
        email.setEmailAddress(orderInfo.getReceiver().getEmail());
        return email;
    }

    private void addContactInfo(InternationalForm internationalForm, OrderInfo orderInfo) {
        Contact contact = new Contact();
        contact.setSoldTo(addShipToInfo(orderInfo));
        internationalForm.setContacts(contact);
    }

    private void addFreightCharges(InternationalForm internationalForm, OrderInfo orderInfo) {
        if (StringUtils.hasText(orderInfo.getUpsInfo().getFreightCharges())) {
            FreightCharges freightCharges = new FreightCharges();
            freightCharges.setMonetaryValue(orderInfo.getUpsInfo().getFreightCharges());
            internationalForm.setFreightCharges(freightCharges);
        }
    }

    private void addOtherCharges(InternationalForm internationalForm, OrderInfo orderInfo) {
        if (StringUtils.hasText(orderInfo.getUpsInfo().getHandingFee())) {
            OtherCharges otherCharges = new OtherCharges();
            otherCharges.setMonetaryValue(orderInfo.getUpsInfo().getHandingFee());
            otherCharges.setDescription("handingFee");
            internationalForm.setOtherCharges(otherCharges);
        }
    }

    private void addProductInfo(InternationalForm internationalForm, List<OrderItemInfo> orderItemInfoList) {
        List<Product> productList = new ArrayList<>();
        for (OrderItemInfo orderItemInfo : orderItemInfoList) {
            Product product = new Product();
            product.setDescriptionList(getDescriptionList(orderItemInfo.getDescription()));
            product.setOriginCountryCode("CN");
            Unit unit = new Unit();
            unit.setNumber(orderItemInfo.getCount().toString());
            unit.setValue(orderItemInfo.getCustomsUnitPrice().toString());
            Unit.UnitOfMeasurement unitOfMeasurement = new Unit.UnitOfMeasurement();
            unitOfMeasurement.setCode("PCS");
            unit.setUnitOfMeasurement(unitOfMeasurement);
            product.setUnit(unit);
            product.setCommodityCode(orderItemInfo.getHsCode());
            productList.add(product);
        }
        internationalForm.setProductList(productList);
    }

    private List<String> getDescriptionList(String description) {
        if (description.length() > 105) {
            throw new InvalidRequestException("商品描述长度超过105个字符限制");
        }
        List<String> descriptionList = new ArrayList<>();
        int count = (int) Math.ceil(description.length() / 35.0);
        for (int i = 0; i < count; i++) {
            descriptionList.add(description.substring(i * 35, i == count - 1 ? description.length() : (i + 1) * 35) );
        }
        return descriptionList;
    }

    private void addInvoiceLineTotalInfo(Shipment shipment, UpsInfo upsInfo) {
        InvoiceLineTotal invoiceLineTotal = new InvoiceLineTotal();
        invoiceLineTotal.setCurrencyCode("RMB");
        invoiceLineTotal.setMonetaryValue(upsInfo.getTotalMoney());
        shipment.setInvoiceLineTotal(invoiceLineTotal);
    }

    private void addPackageInfo(Shipment shipment, OrderInfo orderInfo) {
        List<ShipPackage> packages = new ArrayList<>();
        for (int i = 0; i < orderInfo.getUpsInfo().getPackageCount(); i++) {
            ShipPackage shipPackage = new ShipPackage();
            shipPackage.setDescription(orderInfo.getUpsInfo().getPackageDescription());
            Packaging packaging = new Packaging();
            packaging.setCode("02");
            shipPackage.setPackaging(packaging);
            PackageWeight packageWeight = new PackageWeight();
            UnitOfMeasurement unitOfWeight = new UnitOfMeasurement();
            unitOfWeight.setCode("KGS");
            packageWeight.setUnitOfMeasurement(unitOfWeight);
            packageWeight.setWeight(String.valueOf(orderInfo.getWeight()));
            shipPackage.setPackageWeight(packageWeight);
            packages.add(shipPackage);
        }
        shipment.setShipPackages(packages);
    }

    private void addShipServiceTypeInfo(Shipment shipment, UpsInfo upsInfo) {
        ShipServiceType shipServiceType = new ShipServiceType();
        shipServiceType.setCode(upsInfo.getServiceType());
        shipment.setShipServiceType(shipServiceType);
    }

    private void addPaymentInformationInfo(Shipment shipment, UpsInfo upsInfo) {
        PaymentInformation paymentInformation = new PaymentInformation();
        ShipmentCharge shipmentCharge = new ShipmentCharge();
        shipmentCharge.setType("01");
        BillShipper billShipper = new BillShipper();
        billShipper.setAccountNumber(upsInfo.getAccountNumber());
        shipmentCharge.setBillShipper(billShipper);
        paymentInformation.setShipmentCharge(Collections.singletonList(shipmentCharge));
        shipment.setPaymentInformation(paymentInformation);
    }

    private void addShipFromInfo(Shipment shipment, OrderInfo orderInfo) {
        ShipFrom shipFrom = new ShipFrom();
        Address sender = orderInfo.getSender();
        shipFrom.setName(sender.getCompany());
        shipFrom.setAttentionName(sender.getName());
        Phone shipFromPhone = new Phone();
        shipFromPhone.setNumber(StringUtils.hasText(sender.getPhone()) ? sender.getPhone() : sender.getMobile());
        shipFrom.setPhone(shipFromPhone);
        com.nsy.api.tms.logistics.ups.ship.request.Address shipFromAddress = new com.nsy.api.tms.logistics.ups.ship.request.Address();
        shipFromAddress.setAddressLineList(getStrList(sender.getStreet(), 34));
        shipFromAddress.setCity(sender.getCity());
        // shipFromAddress.setStateProvinceCode("");
        shipFromAddress.setPostalCode(sender.getPostCode());
        shipFromAddress.setCountryCode(sender.getCountry());
        shipFrom.setAddress(shipFromAddress);
        shipFrom.setTaxIdentificationNumber(orderInfo.getTaxNum());
        shipment.setShipFrom(shipFrom);
    }

    public List<String> getStrList(String inputString, int length) {
        int size = inputString.length() / length;
        if (inputString.length() % length != 0) {
            size += 1;
        }
        return getStrList(inputString, length, size);
    }

    public List<String> getStrList(String inputString, int length,
                                   int size) {
        List<String> list = new ArrayList<>();
        if (inputString.length() < length) {
            list.add(inputString);
            return list;
        }
        for (int index = 0; index < size; index++) {
            String childStr = "";
            if (inputString.length() < (index + 1) * length) {
                childStr = inputString.substring(index * length, inputString.length());
            } else {
                childStr = inputString.substring(index * length, (index + 1) * length);
            }
            list.add(childStr);
        }
        return list;
    }

    private ShipTo addShipToInfo(OrderInfo orderInfo) {
        ShipTo shipTo = new ShipTo();
        Address receiver = orderInfo.getReceiver();
        shipTo.setName(receiver.getCompany());
        shipTo.setAttentionName(receiver.getName());
        Phone shipToPhone = new Phone();
        shipToPhone.setNumber(StringUtils.hasText(receiver.getPhone()) ? receiver.getPhone() : receiver.getMobile());
        shipTo.setPhone(shipToPhone);
        shipTo.setEmailAddress(receiver.getEmail());
        com.nsy.api.tms.logistics.ups.ship.request.Address shipToAddress = new com.nsy.api.tms.logistics.ups.ship.request.Address();
        shipToAddress.setAddressLineList(getStrList(receiver.getStreet(), 34));
        shipToAddress.setCity(receiver.getCity());
        shipToAddress.setStateProvinceCode(receiver.getProvince());
        shipToAddress.setPostalCode(receiver.getPostCode());
        shipToAddress.setCountryCode(receiver.getCountry());
        shipTo.setAddress(shipToAddress);
        return shipTo;
    }

    private void addShipperInfos(Shipment shipment, OrderInfo orderInfo) {
        Shipper shipper = new Shipper();
        Address sender = orderInfo.getSender();
        shipper.setName(sender.getCompany());
        shipper.setAttentionName(sender.getName());
        Phone phone = new Phone();
        phone.setNumber(sender.getPhone());
        shipper.setPhone(phone);
        shipper.setShipperNumber(orderInfo.getUpsInfo().getAccountNumber());
        com.nsy.api.tms.logistics.ups.ship.request.Address address = new com.nsy.api.tms.logistics.ups.ship.request.Address();
        address.setAddressLineList(getStrList(sender.getStreet(), 34));
        address.setCity(sender.getCity());
        address.setPostalCode(sender.getPostCode());
        address.setCountryCode(sender.getCountry());
        shipper.setAddress(address);
        shipper.setTaxIdentificationNumber(orderInfo.getTaxNum());
        shipment.setShipper(shipper);
    }


    private void addLabelSpecificationInfos(ShipmentRequest shipmentRequest) {
        LabelSpecification labelSpecification = new LabelSpecification();
        labelSpecification.sethTTPUserAgent("Mozilla/4.5");
        LabelImageFormat labelImageFormat = new LabelImageFormat();
        labelImageFormat.setCode("PNG");
        labelImageFormat.setDescription("PNG");
        labelSpecification.setLabelImageFormat(labelImageFormat);
        shipmentRequest.setLabelSpecification(labelSpecification);
    }


    private void addRequestInfos(ShipmentRequest shipmentRequest) {
        Request request = new Request();
        TransactionReference transactionReference = new TransactionReference();
        transactionReference.setCustomerContext("Shiying Test CLient");
        request.setRequestOption("nonvalidate");
        request.setTransactionReference(transactionReference);
        shipmentRequest.setRequest(request);
    }

    public GenerateOrderResponse syncGenerateOrder(OrderRequest request, TmsLogisticsChannelConfigEntity tmsLogisticsChannelConfigEntity) {
        TmsRequestLogEntity logEntity = tmsRequestLogService.recordBaseInfoLog(request.getOrderInfo());
        GenerateOrderResponse response = new GenerateOrderResponse();
        Map<String, String> configMap = buildConfig(tmsLogisticsChannelConfigEntity);
        UpsShipmentRequest shipmentRequest = null;
        try {
            shipmentRequest = buildLogisticsOrderRequest(request, configMap);
            LOGGER.info("start invoke UPS API");
            LOGGER.info("Request:{" + JsonMapper.toJson(shipmentRequest) + "}");
            String shipmentResponseStr = restTemplate.postForObject(serverUrl, shipmentRequest, String.class);
            LOGGER.info("End invoke UPS API");
            LOGGER.info("Response:{" + shipmentResponseStr + "}");
            if (shipmentResponseStr != null && shipmentResponseStr.contains("Success")) {
                GenerateOrderResponse.SuccessEntity successEntity = processSuccessResponse(request.getOrderInfo(), shipmentResponseStr);
                response.setSuccessEntity(successEntity);
                tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, JsonMapper.toJson(shipmentRequest), shipmentResponseStr, successEntity.getLogisticsNo());
            } else {
                //解析fault返回结果
                ShipFaultResponse shipFaultResponse = new ObjectMapper().readValue(shipmentResponseStr, ShipFaultResponse.class);
                GenerateOrderResponse.Error error = processFailedResponse(shipFaultResponse);
                response.setError(error);
                tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, JsonMapper.toJson(shipmentRequest), JsonMapper.toJson(shipFaultResponse), null);
            }
        } catch (Exception e) {
            tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, JsonMapper.toJson(shipmentRequest), e.getMessage(), null);
            throw new RuntimeException(e);
        }
        return response;
    }

    private GenerateOrderResponse.Error processFailedResponse(ShipFaultResponse shipmentFaultResponse) {
        GenerateOrderResponse.Error error = new GenerateOrderResponse.Error();
        PrimaryErrorCode primaryErrorCode = shipmentFaultResponse.getFault().getDetail().getErrors().getErrorDetail().getPrimaryErrorCode();
        error.setMessage(primaryErrorCode.getDescription());
        error.setCode(primaryErrorCode.getCode());
        return error;
    }


    private GenerateOrderResponse.SuccessEntity processSuccessResponse(OrderInfo orderInfo, String shipmentResponseStr) throws IOException {
        String trackingNumber;
        String labelOssUrl;
        StringBuilder labelUrl = new StringBuilder();
        String invoiceOssUrl;
        String logisticsNos;
        boolean supportPaperless = true;
        byte[] invoiceImageByte;
        UpsInfo upsInfo = orderInfo.getUpsInfo();
        try {
            if (upsInfo.getPackageCount() > 1) { // 多票
                UpsMultiplePiecesShipmentResponse multiplePiecesResponse = new ObjectMapper().readValue(shipmentResponseStr, UpsMultiplePiecesShipmentResponse.class);
                MultiplePiecesShipmentResults shipmentResults = multiplePiecesResponse.getShipmentResponse().getShipmentResults();
                trackingNumber = shipmentResults.getShipmentIdentificationNumber();
                List<PackageResults> packageResultList = shipmentResults.getPackageResults();
                for (PackageResults packageResult : packageResultList) {
                    labelOssUrl = getLabelOssUrl(packageResult);
                    labelUrl.append(packageResultList.indexOf(packageResult) == 0 ? labelOssUrl : "," + labelOssUrl);
                }
                logisticsNos = StringUtils.join(packageResultList.stream().map(PackageResults::getTrackingNumber).collect(Collectors.toList()), ',');
                invoiceImageByte = upsInfo.getUseUpsInvoice() ? Base64.decode(shipmentResults.getForm().getImage().getGraphicImage()) : null;
            } else {  // 一票
                UpsOnePieceShipmentResponse onePieceResponse = new ObjectMapper().readValue(shipmentResponseStr, UpsOnePieceShipmentResponse.class);
                OnePieceShipmentResults shipmentResults = onePieceResponse.getShipmentResponse().getShipmentResults();
                trackingNumber = shipmentResults.getShipmentIdentificationNumber();
                labelUrl.append(getLabelOssUrl(shipmentResults.getPackageResults()));
                logisticsNos = trackingNumber;
                invoiceImageByte = upsInfo.getUseUpsInvoice() ? Base64.decode(shipmentResults.getForm().getImage().getGraphicImage()) : null;
            }
            invoiceOssUrl = upsInfo.getUseUpsInvoice() ? getPdfLabel(invoiceImageByte, String.format(LABEL_NAME_TEMPLATE, labelFolder, trackingNumber + "-invoice")) : null;
        } catch (Exception exception) {
            throw new RuntimeException("save label failed", exception);
        }
        TmsPackageEntity packageEntity = packageService.buildBaseTmsPackageEntityBuilder(LogisticsMethodEnum.UPS, trackingNumber, orderInfo)
                .labelUrl(labelUrl.toString()).invoiceUrl(invoiceOssUrl).logisticsType(LogisticsTypeEnum.INTERNATIONAL_EXPRESS.getName()).build();
        TmsPackageEntity tmsPackageEntity = packageService.save(packageEntity);
        if (!upsInfo.getUseUpsInvoice() || shipmentResponseStr.contains("\"Code\":\"120372\"") || shipmentResponseStr.contains("\"Code\":\"120373\"")) {
            supportPaperless = false;
        }
        GenerateOrderResponse.SuccessEntity successEntity = new GenerateOrderResponse.SuccessEntity();
        successEntity.setLabelUrl(labelUrl.toString());
        successEntity.setLogisticsNo(trackingNumber);
        successEntity.setId(tmsPackageEntity.getId());
        successEntity.setLogisticsNos(logisticsNos);
        successEntity.setSupportPaperless(supportPaperless);
        return successEntity;
    }

    private String getLabelOssUrl(PackageResults packageResult) {
        String labelOssUrl = "";
        try {
            String labelFileName = String.format(LABEL_NAME, labelFolder, packageResult.getTrackingNumber());
            File labelFile = FileUtils.createFile(labelFileName);
            //base64解码成图片，储存本地
            Img2Base64Utils.generateImage(packageResult.getShippingLabel().getGraphicImage(), labelFileName);
            labelOssUrl = aliyunOssService.uploadToOss(ossLabelFolder, labelFile);
        } catch (IOException e) {
            LOGGER.info(e.getMessage(), e);
        }
        return labelOssUrl;
    }

    public Map<String, String> serviceTypes() {
        Map map = new HashMap();
        map.put("01", "UPS_NEXT_DAY_AIR");
        map.put("02", "UPS_2ND_DAY_AIR");
        map.put("03", "UPS_GROUND");
        map.put("07", "UPS_WORLDWIDE_EXPRESS");
        map.put("08", "UPS_WORLDWIDE_EXPEDITED");
        map.put("11", "UPS_STANDARD");
        map.put("12", "UPS_3_DAY_SELECT");
        map.put("13", "UPS_NEXT_DAY_AIR_SAVER");
        map.put("14", "UPS_NEXT_DAY_AIR_EARLY_AM");
        map.put("54", "UPS_WORLDWIDE_EXPRESS_PLUS");
        map.put("59", "UPS_2ND_DAY_AIR_AM");
        map.put("65", "UPS_EXPRESS_SAVER");
        return map;
    }

    public Map<String, String> packingCodes() {
        Map map = new HashMap();
        map.put("00", "UNKNOWN");
        map.put("01", "UPS_LETTER");
        map.put("02", "CUSTOMER_SUPPLIED_PACKAGE");
        map.put("03", "TUBE");
        map.put("04", "PAK");
        map.put("21", "UPS_EXPRESS_BOX");
        map.put("24", "UPS_25KG_BOX");
        map.put("25", "UPS_10KG_BOX");
        return map;
    }

    public void afterPropertiesSet() {
        this.labelFolder += "Ups/";
        this.ossLabelFolder += "ups/";
    }

    public void doTrack(TmsPackageEntity tmsPackageEntity) {
        upsTrackService.doTrack(tmsPackageEntity);
    }

    public void validateOrderRequest(OrderRequest request) {
        OrderInfo orderInfo = request.getOrderInfo();
        super.validateOrderRequest(request);
        Validator.isValid(orderInfo.getLength(), attr -> !Objects.isNull(attr), "length不能为空");
        Validator.isValid(orderInfo, attr -> !attr.getUpsInfo().getUseUpsInvoice() || attr.getOrderItemInfoList().size() < 100, "使用ups无纸化发票品类不能超过100种");
    }
}
