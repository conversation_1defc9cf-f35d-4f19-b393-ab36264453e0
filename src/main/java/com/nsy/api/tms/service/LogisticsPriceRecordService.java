package com.nsy.api.tms.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Validator;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.constants.TrackingConstant;
import com.nsy.api.tms.dao.entity.LogisticsPriceRecordEntity;
import com.nsy.api.tms.dao.entity.SystemConfigEntity;
import com.nsy.api.tms.enumeration.FreightModeEnum;
import com.nsy.api.tms.enumeration.PriceTypeEnum;
import com.nsy.api.tms.enumeration.RecordTypeEnum;
import com.nsy.api.tms.mapper.LogisticsPriceRecordMapper;
import com.nsy.api.tms.repository.SystemConfigRepository;
import com.nsy.api.tms.request.LogisticsPriceRecordInsertRequest;
import com.nsy.api.tms.request.LogisticsPriceRecordPageRequest;
import com.nsy.api.tms.request.LogisticsPriceRecordRequest;
import com.nsy.api.tms.request.LogisticsPriceRecordTableRequest;
import com.nsy.api.tms.response.LogisticsPriceRecordResponse;
import com.nsy.api.tms.response.LogisticsPriceRecordTableResponse;
import com.nsy.api.tms.response.PageResponse;
import com.nsy.api.tms.service.privilege.AccessControlService;
import com.nsy.api.tms.utils.IntervalUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 物流价格记录(LogisticsPriceRecord)服务层
 *
 * <AUTHOR>
 * @since 2023-02-01 13:54:19
 */
@Service
public class LogisticsPriceRecordService extends ServiceImpl<LogisticsPriceRecordMapper, LogisticsPriceRecordEntity> {

    @Resource
    AccessControlService accessControlService;
    @Resource
    SystemConfigRepository systemConfigRepository;
    @Autowired
    ApplicationContext applicationContext;

    /**
     * 分页查询
     */
    public PageResponse<LogisticsPriceRecordResponse> queryByPage(LogisticsPriceRecordPageRequest pageRequest) {
        PageResponse<LogisticsPriceRecordResponse> pageResponse = new PageResponse<>();
        Page<LogisticsPriceRecordEntity> page = new Page<>(pageRequest.getPageIndex(), pageRequest.getPageSize());
        IPage<LogisticsPriceRecordEntity> iPage = page(page, buildPageQueryWrapper(pageRequest));
        pageResponse.setTotalCount(iPage.getTotal());
        List<LogisticsPriceRecordResponse> list = iPage.getRecords().stream().map(entity -> {
            LogisticsPriceRecordResponse resp = new LogisticsPriceRecordResponse();
            BeanUtils.copyProperties(entity, resp);
            resp.setRecordTypeCn(RecordTypeEnum.getDescByName(entity.getRecordType()));
            resp.setPriceTypeCn(PriceTypeEnum.getDescByName(entity.getPriceType()));
            resp.setFreightModeCn(FreightModeEnum.getDescByName(entity.getFreightMode()));
            // 处理日期类型，每月 / 每日
            resp.setPriceDateStr(RecordTypeEnum.formatDate(entity.getRecordType(), entity.getPriceDate(), Boolean.FALSE));
            return resp;
        }).collect(Collectors.toList());
        pageResponse.setContent(list);
        return pageResponse;
    }

    public LogisticsPriceRecordResponse getOneById(Integer id) {
        LogisticsPriceRecordEntity entity = getById(id);
        LogisticsPriceRecordResponse resp = new LogisticsPriceRecordResponse();
        BeanUtils.copyProperties(entity, resp);
        resp.setPriceTypeCn(PriceTypeEnum.getDescByName(entity.getPriceType()));
        resp.setRecordTypeCn(RecordTypeEnum.getDescByName(entity.getRecordType()));
        resp.setFreightModeCn(FreightModeEnum.getDescByName(entity.getFreightMode()));
        resp.setPriceDateStr(RecordTypeEnum.formatDate(entity.getRecordType(), entity.getPriceDate(), Boolean.FALSE));
        return resp;
    }

    /**
     * 新增运价记录
     * 1、先校验之前有没有，没有的话就新增；有的话就要覆盖
     * 2、如果新增的是实际价格, 那要新增实际业务价格
     *    如果新增的是预测价格，那要新增预测业务价格
     *
     * <AUTHOR>
     * 2023-02-01
     */
    @Transactional
    public void insert(LogisticsPriceRecordInsertRequest logisticsPriceRecord, Map<String, String> configMap, String createBy) {
        // 校验、并把前端的日期做转化
        DateTime parseDate = validParams(logisticsPriceRecord);
        LogisticsPriceRecordRequest request = new LogisticsPriceRecordRequest();
        BeanUtilsEx.copyProperties(logisticsPriceRecord, request);
        // 先查询之前的是否存在
        List<LogisticsPriceRecordEntity> logisticsPriceRecordEntities = getLogisticsPriceRecordEntities(request, parseDate);
        if (CollectionUtils.isEmpty(logisticsPriceRecordEntities)) {
            // 新增
            LogisticsPriceRecordEntity entity = new LogisticsPriceRecordEntity();
            BeanUtilsEx.copyProperties(logisticsPriceRecord, entity);
            entity.setPriceDate(parseDate);
            entity.setCreateBy(createBy);
            save(entity);
        } else {
            // 更新金额
            logisticsPriceRecordEntities.forEach(en -> {
                en.setPrice(logisticsPriceRecord.getPrice());
                if (StringUtils.hasText(logisticsPriceRecord.getRemark())) {
                    en.setRemark(logisticsPriceRecord.getRemark());
                }
                en.setUpdateBy(createBy);
            });
            updateBatchById(logisticsPriceRecordEntities);
        }
        // 2、如果是实际价格, 那要新增实际业务价格，是预测价格，那要新增预测业务价格
        addBusinessPrice(logisticsPriceRecord, configMap, createBy);
    }

    private DateTime validParams(LogisticsPriceRecordInsertRequest logisticsPriceRecord) {
        if (Validator.isEmpty(logisticsPriceRecord.getPrice())) throw new BusinessServiceException("金额不能为空");
        if (Validator.isEmpty(logisticsPriceRecord.getPriceDateStr())) throw new BusinessServiceException("日期不能为空");
        if (Validator.isEmpty(logisticsPriceRecord.getRecordType())) throw new BusinessServiceException("日期类型不能为空");
        if (Validator.isEmpty(logisticsPriceRecord.getPriceType())) throw new BusinessServiceException("金额类型不能为空");
        if (Validator.isEmpty(logisticsPriceRecord.getFreightMode())) throw new BusinessServiceException("物流方式不能为空");
//        if (logisticsPriceRecord.getPrice().stripTrailingZeros().scale() > 0)
//            throw new BusinessServiceException("请输入整数");
        RecordTypeEnum.validEnumByName(logisticsPriceRecord.getRecordType());
        PriceTypeEnum.validEnumByName(logisticsPriceRecord.getPriceType());
        FreightModeEnum.validEnumByName(logisticsPriceRecord.getFreightMode());
        if (logisticsPriceRecord.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessServiceException("金额必须大于0");
        }
        return RecordTypeEnum.formatDateIn(logisticsPriceRecord.getPriceDateStr(), logisticsPriceRecord.getRecordType());
    }



    private void addBusinessPrice(LogisticsPriceRecordInsertRequest logisticsPriceRecord, Map<String, String> configMap, String createBy) {
        if (!PriceTypeEnum.isBusinessByName(logisticsPriceRecord.getPriceType())) {
            logisticsPriceRecord.setPriceType(PriceTypeEnum.reverseByName(logisticsPriceRecord.getPriceType()));
            configMap.forEach((k, v) -> {
                if (IntervalUtil.isInTheInterval(String.valueOf(logisticsPriceRecord.getPrice()), k)) {
                    logisticsPriceRecord.setPrice(logisticsPriceRecord.getPrice().multiply(new BigDecimal(v)).setScale(0, BigDecimal.ROUND_HALF_UP));
                }
            });
            insert(logisticsPriceRecord, configMap, createBy);
        }
    }


    // 构造列表查询条件
    private LambdaQueryWrapper<LogisticsPriceRecordEntity> buildPageQueryWrapper(LogisticsPriceRecordPageRequest request) {
        LambdaQueryWrapper<LogisticsPriceRecordEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.hasText(request.getFreightMode()), LogisticsPriceRecordEntity::getFreightMode, request.getFreightMode());
        wrapper.eq(StringUtils.hasText(request.getPriceType()), LogisticsPriceRecordEntity::getPriceType, request.getPriceType());
        wrapper.eq(StringUtils.hasText(request.getRecordType()), LogisticsPriceRecordEntity::getRecordType, request.getRecordType());
        wrapper.ge(StringUtils.hasText(request.getPriceDateStrBegin()), LogisticsPriceRecordEntity::getPriceDate, DateUtil.parse(request.getPriceDateStrBegin()));
        wrapper.le(StringUtils.hasText(request.getPriceDateStrEnd()), LogisticsPriceRecordEntity::getPriceDate, DateUtil.parse(request.getPriceDateStrEnd()));
        wrapper.orderByDesc(LogisticsPriceRecordEntity::getPriceDate);
        return wrapper;
    }

    public List<LogisticsPriceRecordResponse> queryList(LogisticsPriceRecordRequest request) {
        LogisticsPriceRecordInsertRequest logisticsPriceRecord = new LogisticsPriceRecordInsertRequest();
        BeanUtilsEx.copyProperties(request, logisticsPriceRecord);
        DateTime dateTime = validParams(logisticsPriceRecord);
        List<LogisticsPriceRecordEntity> list = getLogisticsPriceRecordEntities(request, dateTime);
        return list.stream().map(entity -> {
            LogisticsPriceRecordResponse resp = new LogisticsPriceRecordResponse();
            BeanUtils.copyProperties(entity, resp);
            resp.setRecordTypeCn(RecordTypeEnum.getDescByName(entity.getRecordType()));
            resp.setPriceTypeCn(PriceTypeEnum.getDescByName(entity.getPriceType()));
            resp.setFreightModeCn(FreightModeEnum.getDescByName(entity.getFreightMode()));
            // 处理日期类型，每月 / 每日
            resp.setPriceDateStr(RecordTypeEnum.formatDate(entity.getRecordType(), entity.getPriceDate(), Boolean.FALSE));
            return resp;
        }).collect(Collectors.toList());
    }

    // 查询某天/某月
    private List<LogisticsPriceRecordEntity> getLogisticsPriceRecordEntities(LogisticsPriceRecordRequest request, Date date) {
        LambdaQueryWrapper<LogisticsPriceRecordEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.hasText(request.getFreightMode()), LogisticsPriceRecordEntity::getFreightMode, request.getFreightMode());
        wrapper.eq(StringUtils.hasText(request.getPriceType()), LogisticsPriceRecordEntity::getPriceType, request.getPriceType());
        wrapper.eq(StringUtils.hasText(request.getRecordType()), LogisticsPriceRecordEntity::getRecordType, request.getRecordType());
        wrapper.eq(LogisticsPriceRecordEntity::getPriceDate, date);
        return list(wrapper);
    }

    // 查询图表
    private List<LogisticsPriceRecordEntity> getEntityForTable(LogisticsPriceRecordTableRequest request) {
        LambdaQueryWrapper<LogisticsPriceRecordEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.hasText(request.getFreightMode()), LogisticsPriceRecordEntity::getFreightMode, request.getFreightMode());
        wrapper.in(LogisticsPriceRecordEntity::getPriceType, PriceTypeEnum.tableType(request.getPriceType()));
        wrapper.eq(StringUtils.hasText(request.getRecordType()), LogisticsPriceRecordEntity::getRecordType, request.getRecordType());
        wrapper.ge(StringUtils.hasText(request.getPriceDateStrBegin()), LogisticsPriceRecordEntity::getPriceDate, RecordTypeEnum.formatDateIn(request.getPriceDateStrBegin(), request.getRecordType()));
        wrapper.le(StringUtils.hasText(request.getPriceDateStrEnd()), LogisticsPriceRecordEntity::getPriceDate, RecordTypeEnum.formatDateIn(request.getPriceDateStrEnd(), request.getRecordType()));
        wrapper.orderByAsc(LogisticsPriceRecordEntity::getPriceDate);
        return list(wrapper);
    }

    @Transactional
    public void insertBatch(List<LogisticsPriceRecordInsertRequest> insertRequests) {
        if (CollectionUtils.isEmpty(insertRequests)) {
            throw new BusinessServiceException("新增运价不能为空");
        }
        Map<String, String> map = getPredictConfigMap();
        insertRequests.forEach(it -> insert(it, map, accessControlService.getRealName()));
    }

    /**
     * 查询折线数据、需要同时展示实际与预测曲线
     * 1、需要按时间排序
     * 2、需要格式化时间（月份 只显示月、日期只显示日）
     */
    public LogisticsPriceRecordTableResponse queryForShowTable(LogisticsPriceRecordTableRequest request) {
        LogisticsPriceRecordTableResponse response = new LogisticsPriceRecordTableResponse();
        // 找出该时段的 已经排序好的记录
        List<LogisticsPriceRecordEntity> recordEntities = getEntityForTable(request);
        recordEntities.forEach(entity -> {
            LogisticsPriceRecordResponse resp = new LogisticsPriceRecordResponse();
            BeanUtils.copyProperties(entity, resp);
            resp.setPriceTypeCn(PriceTypeEnum.getDescByName(entity.getPriceType()));
            resp.setRecordTypeCn(RecordTypeEnum.getDescByName(entity.getRecordType()));
            resp.setFreightModeCn(FreightModeEnum.getDescByName(entity.getFreightMode()));
            resp.setPriceDateStr(RecordTypeEnum.formatDate(entity.getRecordType(), entity.getPriceDate(), Boolean.TRUE));
            if (PriceTypeEnum.isPredictByName(entity.getPriceType())) {
                response.getPredictList().add(resp);
            } else {
                response.getRealList().add(resp);
            }
        });
        return response;
    }

    public Map<String, String> getPredictConfigMap() {
        SystemConfigEntity predictConfig = systemConfigRepository.findFirstByConfigName(TrackingConstant.LOGISTICS_PRICE_PREDICT);
        if (predictConfig == null || !StringUtils.hasText(predictConfig.getConfigValue())) {
            throw new BusinessServiceException("价格预测 配置项不能为空");
        }
        return (Map<String, String>) JSON.parse(predictConfig.getConfigValue());
    }

}
