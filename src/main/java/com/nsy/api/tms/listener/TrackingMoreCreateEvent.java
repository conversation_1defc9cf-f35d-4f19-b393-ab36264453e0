package com.nsy.api.tms.listener;

import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import org.springframework.context.ApplicationEvent;

import java.util.List;

public class TrackingMoreCreateEvent extends ApplicationEvent {

    private final List<TmsPackageEntity> tmsPackageEntityList;

    /**
     * Create a new ApplicationEvent.
     *
     * @param tmsPackageEntityList the object on which the event initially occurred (never {@code null})
     */
    public TrackingMoreCreateEvent(List<TmsPackageEntity> tmsPackageEntityList) {
        super(tmsPackageEntityList);
        this.tmsPackageEntityList = tmsPackageEntityList;
    }

    public List<TmsPackageEntity> getTmsPackageEntityList() {
        return tmsPackageEntityList;
    }
}
