package com.nsy.api.tms.domain;


import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 1.0
 */
public interface TmsLogisticsFreightPriceInfo {

    Integer getLogisticsFreightId();
    // 物流公司
    String getLogisticsCompany();

    // 物流渠道
    String getLogisticsChannelName();

    // 国家
    String getCountries();

    // 挂号费
    BigDecimal getRegistrationFee();

    // 续重费用
    BigDecimal getRenewalFee();

    // 邮费单价
    BigDecimal getUnitPrice();

    // 最低重量
    BigDecimal getWeightMin();

    // 折扣
    BigDecimal getDiscount();

    // 时效
    Integer getAging();

    // 区间费用
    String getIntervalFee();
}
