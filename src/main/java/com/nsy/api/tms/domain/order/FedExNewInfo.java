package com.nsy.api.tms.domain.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(description = "FedEx额外信息")
public class FedExNewInfo {

    @ApiModelProperty(value = "服务类型，INTERNATIONAL_PRIORITY，INTERNATIONAL_ECONOMY .....")
    private String serviceType;

    @ApiModelProperty(value = "包装类型，FEDEX_BOX • FEDEX_ENVELOPE • FEDEX_PAK • FEDEX_TUBE • YOUR_PACKAGING")
    private String packagingType;

    @ApiModelProperty(value = "包裹数")
    private int packageCount;

    @ApiModelProperty(value = "主运单号")
    private String trackingNumber;

    @ApiModelProperty(value = "箱号")
    private int sequenceNumber;

    @ApiModelProperty(value = "包裹总重量(kg)")
    private BigDecimal packageTotalWeight;

    @ApiModelProperty(value = "寄件人参考信息")
    private String customerReference;

    @ApiModelProperty(value = "运费")
    private Double freightCharges;

    @ApiModelProperty(value = "手续费")
    private Double handingFee;

    @ApiModelProperty(value = "收件人支付税金账号")
    private String paymentAccount;

    @ApiModelProperty(value = "新接口，包裹单个重量", hidden = true)
    private BigDecimal eachPackageWeight;

    /*---------------------------------tms内部处理-----------------------------------*/
    @ApiModelProperty(value = "包裹重量集合", hidden = true)
    private List<BigDecimal> packageWeight;

    public String getPaymentAccount() {
        return paymentAccount;
    }

    public void setPaymentAccount(String paymentAccount) {
        this.paymentAccount = paymentAccount;
    }


    public BigDecimal getEachPackageWeight() {
        return eachPackageWeight;
    }

    public void setEachPackageWeight(BigDecimal eachPackageWeight) {
        this.eachPackageWeight = eachPackageWeight;
    }

    public BigDecimal getPackageTotalWeight() {
        return packageTotalWeight;
    }

    public void setPackageTotalWeight(BigDecimal packageTotalWeight) {
        this.packageTotalWeight = packageTotalWeight;
    }

    public Double getHandingFee() {
        return handingFee;
    }

    public void setHandingFee(Double handingFee) {
        this.handingFee = handingFee;
    }

    public Double getFreightCharges() {
        return freightCharges;
    }

    public void setFreightCharges(Double freightCharges) {
        this.freightCharges = freightCharges;
    }

    public String getCustomerReference() {
        return customerReference;
    }

    public void setCustomerReference(String customerReference) {
        this.customerReference = customerReference;
    }

    public List<BigDecimal> getPackageWeight() {
        return packageWeight;
    }

    public void setPackageWeight(List<BigDecimal> packageWeight) {
        this.packageWeight = packageWeight;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public int getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(int packageCount) {
        this.packageCount = packageCount;
    }

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }

    public int getSequenceNumber() {
        return sequenceNumber;
    }

    public void setSequenceNumber(int sequenceNumber) {
        this.sequenceNumber = sequenceNumber;
    }

    public String getPackagingType() {
        return packagingType;
    }

    public void setPackagingType(String packagingType) {
        this.packagingType = packagingType;
    }
}
