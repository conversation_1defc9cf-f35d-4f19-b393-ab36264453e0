package com.nsy.api.tms.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @since 2024-07-09 18:18:44
 */
@ApiModel(value = "SpaceSkuMappingUpdateRequest", description = "更新request")
public class SpaceSkuMappingUpdateRequest {
    @ApiModelProperty("")
    private Integer id;

    @ApiModelProperty("")
    private String location;

    /**
     * 仓库id
     */
    @ApiModelProperty("仓库id")
    private Integer spaceId;

    /**
     * 仓库名
     */
    @ApiModelProperty("仓库名")
    private String spaceName;

    /**
     * 仓库平台
     */
    @ApiModelProperty("仓库平台")
    private String platform;

    /**
     * 系统sku
     */
    @ApiModelProperty("系统sku")
    private String sku;

    /**
     * 平台仓库sku
     */
    @ApiModelProperty("平台仓库sku")
    private String spaceSku;

    /**
     * 平台仓库条码
     */
    @ApiModelProperty("平台仓库条码")
    private String spaceBarcode;

    /**
     * 平台仓库sku信息
     */
    @ApiModelProperty("平台仓库sku信息")
    private String spaceSkuInfo;



    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getSpaceSku() {
        return spaceSku;
    }

    public void setSpaceSku(String spaceSku) {
        this.spaceSku = spaceSku;
    }

    public String getSpaceBarcode() {
        return spaceBarcode;
    }

    public void setSpaceBarcode(String spaceBarcode) {
        this.spaceBarcode = spaceBarcode;
    }

    public String getSpaceSkuInfo() {
        return spaceSkuInfo;
    }

    public void setSpaceSkuInfo(String spaceSkuInfo) {
        this.spaceSkuInfo = spaceSkuInfo;
    }

}

