package com.nsy.api.tms.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-09 18:18:44
 */
@ApiModel(value = "SpaceSkuMappingInsertRequest", description = "新增request")
public class SpaceSkuMappingInsertBatchRequest {

    /**
     * 仓库id
     */
    @ApiModelProperty("仓库id")
    @NotNull
    private Integer spaceId;

    /**
     * 仓库名
     */
    @ApiModelProperty("仓库名")
    @NotBlank
    private String spaceName;

    @ApiModelProperty("数据集合")
    private List<SpaceSkuMappingInsertRequest> insertList = new ArrayList<>();

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public List<SpaceSkuMappingInsertRequest> getInsertList() {
        return insertList;
    }

    public void setInsertList(List<SpaceSkuMappingInsertRequest> insertList) {
        this.insertList = insertList;
    }
}

