package com.nsy.api.tms.domain;

/**
 * @Author: <PERSON>
 * @Date: 2019/7/29 16:09
 */
public class TmsBoardStatistics {
    private Long globalLogisticsAlertCount;
    private Long globalLogisticsUnCollectCount;
    private Long domesticLogisticsAlertCount;
    private Long domesticLogisticsUnCollectCount;

    public Long getGlobalLogisticsAlertCount() {
        return globalLogisticsAlertCount;
    }

    public void setGlobalLogisticsAlertCount(Long globalLogisticsAlertCount) {
        this.globalLogisticsAlertCount = globalLogisticsAlertCount;
    }

    public Long getGlobalLogisticsUnCollectCount() {
        return globalLogisticsUnCollectCount;
    }

    public void setGlobalLogisticsUnCollectCount(Long globalLogisticsUnCollectCount) {
        this.globalLogisticsUnCollectCount = globalLogisticsUnCollectCount;
    }

    public Long getDomesticLogisticsAlertCount() {
        return domesticLogisticsAlertCount;
    }

    public void setDomesticLogisticsAlertCount(Long domesticLogisticsAlertCount) {
        this.domesticLogisticsAlertCount = domesticLogisticsAlertCount;
    }

    public Long getDomesticLogisticsUnCollectCount() {
        return domesticLogisticsUnCollectCount;
    }

    public void setDomesticLogisticsUnCollectCount(Long domesticLogisticsUnCollectCount) {
        this.domesticLogisticsUnCollectCount = domesticLogisticsUnCollectCount;
    }
}
