package com.nsy.api.tms.dao.entity.freight;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.api.tms.dao.entity.BaseMpEntity;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 物流渠道价格表(VpsChannelFreight)实体类
 *
 * <AUTHOR>
 * @since 2023-09-04 17:05:25
 */
@Entity
@Table(name = "vps_channel_freight")
@TableName("vps_channel_freight")
public class VpsChannelFreightEntity extends BaseMpEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 公司id
     */
    private Integer companyId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 账号
     */
    @ApiModelProperty("账号")
    private String account;

    /**
     * 状态(待审核INIT、审核通过PASS、驳回REJECT)
     */
    @ApiModelProperty("状态")
    private String status;

    /**
     * 计费方式(单价*重量UNIT_PRICE_WEIGHT，首重+续重INITIAL_ADDITIONAL_WEIGHT，重量段RANGE_WEIGHT )
     */
    @ApiModelProperty("计费方式")
    private String billingType;


    /**
     * 币种
     */
    @ApiModelProperty("币种")
    private String currencyCode;


    @ApiModelProperty("备注")
    private String remark;


    /**
     * 默认单价
     */
    @ApiModelProperty("默认单价")
    private BigDecimal defaultUnitFreight;

    /**
     * 默认折扣系数(0-1)
     */
    @ApiModelProperty("默认折扣系数(0-1)")
    private BigDecimal defaultDiscountFactor;

    /**
     * 审核日期
     */
    private Date reviewDate;

    /**
     * 审核人
     */
    private String reviewer;


    /**
     * 审核备注
     */
    private String reviewRemark;

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getReviewer() {
        return reviewer;
    }

    public void setReviewer(String reviewer) {
        this.reviewer = reviewer;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getBillingType() {
        return billingType;
    }

    public void setBillingType(String billingType) {
        this.billingType = billingType;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }


    public BigDecimal getDefaultUnitFreight() {
        return defaultUnitFreight;
    }

    public void setDefaultUnitFreight(BigDecimal defaultUnitFreight) {
        this.defaultUnitFreight = defaultUnitFreight;
    }

    public BigDecimal getDefaultDiscountFactor() {
        return defaultDiscountFactor;
    }

    public void setDefaultDiscountFactor(BigDecimal defaultDiscountFactor) {
        this.defaultDiscountFactor = defaultDiscountFactor;
    }

    public Date getReviewDate() {
        return reviewDate;
    }

    public void setReviewDate(Date reviewDate) {
        this.reviewDate = reviewDate;
    }

    public String getReviewRemark() {
        return reviewRemark;
    }

    public void setReviewRemark(String reviewRemark) {
        this.reviewRemark = reviewRemark;
    }
}

