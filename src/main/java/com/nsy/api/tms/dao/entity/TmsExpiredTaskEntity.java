package com.nsy.api.tms.dao.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: <PERSON>
 * @Date: 2019/12/27 16:09
 */
@Entity
@Table(name = "tms_expired_task")

public class TmsExpiredTaskEntity extends BaseDataManipulationEntity {


    public static final Integer PENDING_PROCESS = 0;
    public static final Integer PROCESSED = 1;

    public static final Integer NOT_CLAIM = 0;
    public static final Integer CLAIM = 1;

    @Column(name = "logistics_method")
    private String logisticsMethod;

    /*** 物流公司*/
    @Column(name = "logistics_company")
    private String logisticsCompany;

    @Column(name = "logistics_type")
    private String logisticsType;

    /*** 触发条件*/
    @Column(name = "trigger_condition")
    private String triggerCondition;

    /*** 任务状态: 0---待处理，1---已处理*/
    @Column(name = "task_status")
    private int taskStatus = PENDING_PROCESS;

    /*** 状态*/
    @Column(name = "process_status")
    private String processStatus;

    /*任务状态: 0---索赔，1---不索赔*/
    @Column(name = "is_claim")
    private int isClaim = NOT_CLAIM;

    /*预计索赔金额*/
    @Column(name = "claim_amount")
    private BigDecimal claimAmount = BigDecimal.ZERO;

    /*超期率阀值*/
    @Column(name = "threshold")
    private String threshold;

    /*** 统计时长*/
    @Column(name = "statistic_duration")
    private Integer statisticDuration;

    /*统计开始时间*/
    @Column(name = "statistic_start_date")
    private Date statisticStartDate;

    /*统计结束时间*/
    @Column(name = "statistic_end_date")
    private Date statisticEndDate;

    /*** 包裹数*/
    @Column(name = "package_count")
    private int packageCount;

    /*** 超期包裹数*/
    @Column(name = "expired_package_count")
    private int expiredPackageCount;

    /*** 超期率*/
    @Column(name = "expired_rate")
    private String expiredRate;

    /*** 备注*/
    @Column(name = "remarks")
    private String remarks;

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getLogisticsMethod() {
        return logisticsMethod;
    }

    public void setLogisticsMethod(String logisticsMethod) {
        this.logisticsMethod = logisticsMethod;
    }

    public String getTriggerCondition() {
        return triggerCondition;
    }

    public void setTriggerCondition(String triggerCondition) {
        this.triggerCondition = triggerCondition;
    }

    public int getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(int taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
    }

    public int getIsClaim() {
        return isClaim;
    }

    public void setIsClaim(int isClaim) {
        this.isClaim = isClaim;
    }

    public String getThreshold() {
        return threshold;
    }

    public void setThreshold(String threshold) {
        this.threshold = threshold;
    }


    public Integer getStatisticDuration() {
        return statisticDuration;
    }

    public void setStatisticDuration(Integer statisticDuration) {
        this.statisticDuration = statisticDuration;
    }

    public int getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(int packageCount) {
        this.packageCount = packageCount;
    }

    public int getExpiredPackageCount() {
        return expiredPackageCount;
    }

    public void setExpiredPackageCount(int expiredPackageCount) {
        this.expiredPackageCount = expiredPackageCount;
    }

    public String getExpiredRate() {
        return expiredRate;
    }

    public void setExpiredRate(String expiredRate) {
        this.expiredRate = expiredRate;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public Date getStatisticStartDate() {
        return statisticStartDate;
    }

    public void setStatisticStartDate(Date statisticStartDate) {
        this.statisticStartDate = statisticStartDate;
    }

    public Date getStatisticEndDate() {
        return statisticEndDate;
    }

    public void setStatisticEndDate(Date statisticEndDate) {
        this.statisticEndDate = statisticEndDate;
    }

    public String getLogisticsType() {
        return logisticsType;
    }

    public void setLogisticsType(String logisticsType) {
        this.logisticsType = logisticsType;
    }

    public BigDecimal getClaimAmount() {
        return claimAmount;
    }

    public void setClaimAmount(BigDecimal claimAmount) {
        this.claimAmount = claimAmount;
    }
}
