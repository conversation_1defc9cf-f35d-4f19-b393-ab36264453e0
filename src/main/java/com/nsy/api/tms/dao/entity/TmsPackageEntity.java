package com.nsy.api.tms.dao.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.tms.enumeration.LogisticsTypeEnum;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: Woods Lee
 * @Date: 2018/12/21 10:02
 */
@Entity
@Table(name = "tms_package")
@TableName("tms_package")
public class TmsPackageEntity extends BaseMpEntity {

    public static final Integer NOT_DELETE = 0;
    public static final Integer DELETED = 1;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;

    /*** 包裹编码*/
    @Column(name = "logistics_no")
    private String logisticsNo;
    /*** 面单URL*/
    @Column(name = "label_url", length = 1000)
    private String labelUrl;
    /*** 发票URL*/
    @Column(name = "invoice_url", length = 1000)
    private String invoiceUrl;
    /*** 菜鸟打印数据*/
    @Column(name = "print_data")
    private String printData;
    /*** ERP 订单号*/
    @Column(name = "tid")
    private String tid;
    /*** ERP 业务单号*/
    @Column(name = "business_key")
    private String businessKey;
    /*** 订单ID变种, tid + 时间戳， 用于重复生成订单场景*/
    @Column(name = "tms_tid")
    private String tmsTid;
    /*** 物流商返回的订单号*/
    @Column(name = "logistics_tid")
    private String logisticsTid;

    @Column(name = "ship_date")
    private Date shipDate;

    /*** 部门*/
    @Column(name = "dept_name")
    private String deptName;
    /*** 物流方式*/
    @Column(name = "logistics_method")
    private String logisticsMethod;
    
    /*** 物流账号*/
    @Column(name = "logistics_account_id")
    private Integer logisticsAccountId;

    /*** 物流公司*/
    @Column(name = "logistics_company")
    private String logisticsCompany;

    /*** 物流渠道编码*/
    @Column(name = "logistics_channel_code")
    private String logisticsChannelCode;

    @Column(name = "key_group")
    private String keyGroup;

    /*** 收取费用*/
    @Column(name = "erp_charge_freight")
    private BigDecimal erpChargeFreight;
    /*** 实际运费*/
    @Column(name = "actual_freight")
    private BigDecimal actualFreight;
    /*** 状态: 创建、已收、运输、送达、异常*/
    @Column(name = "status")
    private String status = "创建";
    /*** 收件时间*/
    @Column(name = "receive_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receiveTime;
    /**
     * 送达时间
     */
    @Column(name = "arrival_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date arrivalTime;
    /*** 物流流转最后更新时间*/
    @Column(name = "route_last_update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date routeLastUpdateTime;
    /*** 物流路由查询次数*/
    @Column(name = "route_search_times")
    private Integer routeSearchTimes = 0;
    /*** 删除与否：1--删除，0--未删除*/
    @Column(name = "delete_flag")
    private Integer deleteFlag = 0;
    /*** 店铺id*/
    @Column(name = "store_id")
    private Integer storeId;
    /*** 店铺名称*/
    @Column(name = "store_name")
    private String storeName;
    /*** 目的国家*/
    @Column(name = "country_code")
    private String countryCode;

    @Column(name = "post_code")
    private String postCode;
    @Column(name = "track_ignore")
    private Integer trackIgnore = 0;
    @Column(name = "logistics_type")
    private String logisticsType = LogisticsTypeEnum.INTERNATIONAL_PACKAGE.getName();

    @Column(name = "track_type")
    private String trackType;

    @Column(name = "secondary_number")
    private String secondaryNumber;

    @Column(name = "location")
    private String location;

    public static class Builder {
        // required parameters
        private String logisticsNo;
        private String tid;
        private String businessKey;
        private String tmsTid;
        private String logisticsTid;
        private String logisticsMethod;
        private Integer logisticsAccountId;
        private String logisticsCompany;
        private String logisticsChannelCode;
        private String keyGroup;
        private Date shipDate;
        //optional
        private String labelUrl;
        private String invoiceUrl;
        private String location;
        private String printData;
        private String deptName;
        private Date routeLastUpdateTime;
        private Integer trackIgnore = 0;
        private Integer routeSearchTimes = 0;
        private BigDecimal erpChargeFreight;
        private BigDecimal actualFreight;
        private String status = "创建";
        private Date receiveTime;
        private Date arrivalTime;
        private Integer deleteFlag = 0;
        private Integer storeId;
        private String storeName;
        private String countryCode;
        private String logisticsType = LogisticsTypeEnum.INTERNATIONAL_PACKAGE.getName();
        private String secondaryNumber;

        public String getSecondaryNumber() {
            return secondaryNumber;
        }

        public void setSecondaryNumber(String secondaryNumber) {
            this.secondaryNumber = secondaryNumber;
        }

        public String getLogisticsType() {
            return logisticsType;
        }

        public void setLogisticsType(String logisticsType) {
            this.logisticsType = logisticsType;
        }

        public String getLogisticsNo() {
            return logisticsNo;
        }

        public void setLogisticsNo(String logisticsNo) {
            this.logisticsNo = logisticsNo;
        }

        public String getTid() {
            return tid;
        }

        public void setTid(String tid) {
            this.tid = tid;
        }

        public String getTmsTid() {
            return tmsTid;
        }

        public void setTmsTid(String tmsTid) {
            this.tmsTid = tmsTid;
        }

        public String getLogisticsTid() {
            return logisticsTid;
        }

        public void setLogisticsTid(String logisticsTid) {
            this.logisticsTid = logisticsTid;
        }

        public Date getShipDate() {
            return shipDate;
        }

        public Integer getLogisticsAccountId() {
            return logisticsAccountId;
        }

        public void setLogisticsAccountId(Integer logisticsAccountId) {
            this.logisticsAccountId = logisticsAccountId;
        }

        public String getLogisticsCompany() {
            return logisticsCompany;
        }

        public void setLogisticsCompany(String logisticsCompany) {
            this.logisticsCompany = logisticsCompany;
        }

        public void setShipDate(Date shipDate) {
            this.shipDate = shipDate;
        }

        public String getLogisticsMethod() {
            return logisticsMethod;
        }

        public void setLogisticsMethod(String logisticsMethod) {
            this.logisticsMethod = logisticsMethod;
        }

        public String getLogisticsChannelCode() {
            return logisticsChannelCode;
        }

        public void setLogisticsChannelCode(String logisticsChannelCode) {
            this.logisticsChannelCode = logisticsChannelCode;
        }

        public String getLabelUrl() {
            return labelUrl;
        }

        public void setLabelUrl(String labelUrl) {
            this.labelUrl = labelUrl;
        }

        public String getPrintData() {
            return printData;
        }

        public void setPrintData(String printData) {
            this.printData = printData;
        }

        public Date getRouteLastUpdateTime() {
            return routeLastUpdateTime;
        }

        public void setRouteLastUpdateTime(Date routeLastUpdateTime) {
            this.routeLastUpdateTime = routeLastUpdateTime;
        }

        public Integer getRouteSearchTimes() {
            return routeSearchTimes;
        }

        public void setRouteSearchTimes(Integer routeSearchTimes) {
            this.routeSearchTimes = routeSearchTimes;
        }

        public BigDecimal getErpChargeFreight() {
            return erpChargeFreight;
        }

        public void setErpChargeFreight(BigDecimal erpChargeFreight) {
            this.erpChargeFreight = erpChargeFreight;
        }

        public BigDecimal getActualFreight() {
            return actualFreight;
        }

        public void setActualFreight(BigDecimal actualFreight) {
            this.actualFreight = actualFreight;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public Date getReceiveTime() {
            return receiveTime;
        }

        public void setReceiveTime(Date receiveTime) {
            this.receiveTime = receiveTime;
        }

        public Date getArrivalTime() {
            return arrivalTime;
        }

        public void setArrivalTime(Date arrivalTime) {
            this.arrivalTime = arrivalTime;
        }

        public Integer getDeleteFlag() {
            return deleteFlag;
        }

        public void setDeleteFlag(Integer deleteFlag) {
            this.deleteFlag = deleteFlag;
        }

        public String getDeptName() {
            return deptName;
        }

        public void setDeptName(String deptName) {
            this.deptName = deptName;
        }

        public Integer getStoreId() {
            return storeId;
        }

        public void setStoreId(Integer storeId) {
            this.storeId = storeId;
        }

        public String getStoreName() {
            return storeName;
        }

        public void setStoreName(String storeName) {
            this.storeName = storeName;
        }

        public Integer getTrackIgnore() {
            return trackIgnore;
        }

        public void setTrackIgnore(Integer trackIgnore) {
            this.trackIgnore = trackIgnore;
        }

        public String getCountryCode() {
            return countryCode;
        }

        public void setCountryCode(String countryCode) {
            this.countryCode = countryCode;
        }

        public String getInvoiceUrl() {
            return invoiceUrl;
        }

        public void setInvoiceUrl(String invoiceUrl) {
            this.invoiceUrl = invoiceUrl;
        }

        public String getLocation() {
            return location;
        }

        public void setLocation(String location) {
            this.location = location;
        }

        public String getBusinessKey() {
            return businessKey;
        }

        public void setBusinessKey(String businessKey) {
            this.businessKey = businessKey;
        }

        public String getKeyGroup() {
            return keyGroup;
        }

        public void setKeyGroup(String keyGroup) {
            this.keyGroup = keyGroup;
        }

        public Builder(String logisticsMethod, String keyGroup, String logisticsChannelCode, String logisticsNo, String tid, String tmsTid) {
            this.logisticsNo = logisticsNo;
            this.tid = tid;
            this.tmsTid = tmsTid;
            this.logisticsMethod = logisticsMethod;
            this.logisticsChannelCode = logisticsChannelCode;
            this.keyGroup = keyGroup;
        }

        public Builder businessKey(String businessKey) {
            this.businessKey = businessKey;
            return this;
        }


        public Builder logisticsCompany(String logisticsCompany) {
            this.logisticsCompany = logisticsCompany;
            return this;
        }

        public Builder logisticsAccountId(Integer logisticsAccountId) {
            this.logisticsAccountId = logisticsAccountId;
            return this;
        }

        public Builder labelUrl(String labelUrl) {
            this.labelUrl = labelUrl;
            return this;
        }

        public Builder printData(String printData) {
            this.printData = printData;
            return this;
        }

        public Builder deptName(String deptName) {
            this.deptName = deptName;
            return this;
        }

        public Builder status(String status) {
            this.status = status;
            return this;
        }

        public Builder logisticsTid(String logisticsTid) {
            this.logisticsTid = logisticsTid;
            return this;
        }

        public Builder secondaryNumber(String secondaryNumber) {
            this.secondaryNumber = secondaryNumber;
            return this;
        }

        public Builder shipDate(Date shipDate) {
            this.shipDate = shipDate;
            return this;
        }

        public Builder erpChargeFreight(BigDecimal erpChargeFreight) {
            this.erpChargeFreight = erpChargeFreight;
            return this;
        }

        public Builder actualFreight(BigDecimal actualFreight) {
            this.actualFreight = actualFreight;
            return this;
        }

        public Builder deleteFlag(Integer deleteFlag) {
            this.deleteFlag = deleteFlag;
            return this;
        }

        public Builder storeId(Integer storeId) {
            this.storeId = storeId;
            return this;
        }

        public Builder storeName(String storeName) {
            this.storeName = storeName;
            return this;
        }

        public Builder countryCode(String countryCode) {
            this.countryCode = countryCode;
            return this;
        }

        public Builder routeSearchTimes(Integer routeSearchTimes) {
            this.routeSearchTimes = routeSearchTimes;
            return this;
        }

        public Builder trackIgnore(Integer trackIgnore) {
            this.trackIgnore = trackIgnore;
            return this;
        }

        public Builder logisticsType(String logisticsType) {
            this.logisticsType = logisticsType;
            return this;
        }

        public Builder invoiceUrl(String invoiceUrl) {
            this.invoiceUrl = invoiceUrl;
            return this;
        }

        public Builder location(String location) {
            this.location = location;
            return this;
        }

        public TmsPackageEntity build() {
            return new TmsPackageEntity(this);
        }
    }


    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getSecondaryNumber() {
        return secondaryNumber;
    }

    public void setSecondaryNumber(String secondaryNumber) {
        this.secondaryNumber = secondaryNumber;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getLabelUrl() {
        return labelUrl;
    }

    public void setLabelUrl(String labelUrl) {
        this.labelUrl = labelUrl;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getLogisticsTid() {
        return logisticsTid;
    }

    public void setLogisticsTid(String logisticsTid) {
        this.logisticsTid = logisticsTid;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getLogisticsMethod() {
        return logisticsMethod;
    }

    public void setLogisticsMethod(String logisticsMethod) {
        this.logisticsMethod = logisticsMethod;
    }

    public BigDecimal getErpChargeFreight() {
        return erpChargeFreight;
    }

    public void setErpChargeFreight(BigDecimal erpChargeFreight) {
        this.erpChargeFreight = erpChargeFreight;
    }

    public BigDecimal getActualFreight() {
        return actualFreight;
    }

    public void setActualFreight(BigDecimal actualFreight) {
        this.actualFreight = actualFreight;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
    }

    public Date getArrivalTime() {
        return arrivalTime;
    }

    public void setArrivalTime(Date arrivalTime) {
        this.arrivalTime = arrivalTime;
    }

    public Date getRouteLastUpdateTime() {
        return routeLastUpdateTime;
    }

    public void setRouteLastUpdateTime(Date routeLastUpdateTime) {
        this.routeLastUpdateTime = routeLastUpdateTime;
    }

    public Integer getRouteSearchTimes() {
        return routeSearchTimes;
    }

    public void setRouteSearchTimes(Integer routeSearchTimes) {
        this.routeSearchTimes = routeSearchTimes;
    }

    public String getPrintData() {
        return printData;
    }

    public void setPrintData(String printData) {
        this.printData = printData;
    }

    public String getLogisticsChannelCode() {
        return logisticsChannelCode;
    }

    public void setLogisticsChannelCode(String logisticsChannelCode) {
        this.logisticsChannelCode = logisticsChannelCode;
    }

    public String getKeyGroup() {
        return keyGroup;
    }

    public void setKeyGroup(String keyGroup) {
        this.keyGroup = keyGroup;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public String getTmsTid() {
        return tmsTid;
    }

    public void setTmsTid(String tmsTid) {
        this.tmsTid = tmsTid;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public Integer getTrackIgnore() {
        return trackIgnore;
    }

    public void setTrackIgnore(Integer trackIgnore) {
        this.trackIgnore = trackIgnore;
    }

    public String getLogisticsType() {
        return logisticsType;
    }

    public void setLogisticsType(String logisticsType) {
        this.logisticsType = logisticsType;
    }

    public String getInvoiceUrl() {
        return invoiceUrl;
    }

    public void setInvoiceUrl(String invoiceUrl) {
        this.invoiceUrl = invoiceUrl;
    }

    public Date getShipDate() {
        return shipDate;
    }

    public void setShipDate(Date shipDate) {
        this.shipDate = shipDate;
    }

    public Integer getLogisticsAccountId() {
        return logisticsAccountId;
    }

    public void setLogisticsAccountId(Integer logisticsAccountId) {
        this.logisticsAccountId = logisticsAccountId;
    }

    public String getTrackType() {
        return trackType;
    }

    public void setTrackType(String trackType) {
        this.trackType = trackType;
    }

    public TmsPackageEntity() {
    }

    public TmsPackageEntity(Builder builder) {
        this.logisticsNo = builder.logisticsNo;
        this.labelUrl = builder.labelUrl;
        this.printData = builder.printData;
        this.tid = builder.tid;
        this.businessKey = builder.businessKey;
        this.tmsTid = builder.tmsTid;
        this.logisticsTid = builder.logisticsTid;
        this.secondaryNumber = builder.secondaryNumber;
        this.deptName = builder.deptName;
        this.logisticsMethod = builder.logisticsMethod;
        this.logisticsAccountId = builder.logisticsAccountId;
        this.logisticsCompany = builder.logisticsCompany;
        this.logisticsChannelCode = builder.logisticsChannelCode;
        this.erpChargeFreight = builder.erpChargeFreight;
        this.actualFreight = builder.actualFreight;
        this.status = builder.status;
        this.receiveTime = builder.receiveTime;
        this.arrivalTime = builder.arrivalTime;
        this.routeLastUpdateTime = builder.routeLastUpdateTime;
        this.routeSearchTimes = builder.routeSearchTimes;
        this.deleteFlag = builder.deleteFlag;
        this.storeId = builder.storeId;
        this.storeName = builder.storeName;
        this.location = builder.location;
        this.countryCode = builder.countryCode;
        this.trackIgnore = builder.trackIgnore;
        this.logisticsType = builder.logisticsType;
        this.invoiceUrl = builder.invoiceUrl;
        this.keyGroup = builder.keyGroup;
        this.shipDate = builder.shipDate;
    }
}
