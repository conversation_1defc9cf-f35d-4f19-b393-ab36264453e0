package com.nsy.api.tms.dao.entity;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Entity
@Table(name = "tms_third_part_token")
public class TmsThirdPartTokenEntity extends BaseDataManipulationEntity {

    /**
     * 平台
     */
    @Column(name = "platform")
    private String platform;

    /**
     * token
     */
    @Column(name = "token")
    private String token;


    /**
     * 开始时间
     */
    @Column(name = "start_date")
    private Date startDate;

    /**
     * 超期（过期）时间
     */
    @Column(name = "expire_date")
    private Date expireDate;


    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }
}
