package com.nsy.api.tms.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "tms_extended_area_surcharge")
@TableName("tms_extended_area_surcharge")
public class TmsExtendedAreaSurchargeEntity extends BaseMpEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;

    /*** 物流公司*/
    @Column(name = "logistics_company")
    private String logisticsCompany;
    /*** 国家编码缩写*/
    @Column(name = "country_code")
    private String countryCode;
    /*** 邮编起始*/
    @Column(name = "postal_code_low")
    private String postalCodeLow;
    /*** 邮编结束*/
    @Column(name = "postal_code_high")
    private String postalCodeHigh;
    /*** 目的地附加费*/
    @Column(name = "destination_surcharge")
    private String destinationSurcharge;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getPostalCodeLow() {
        return postalCodeLow;
    }

    public void setPostalCodeLow(String postalCodeLow) {
        this.postalCodeLow = postalCodeLow;
    }

    public String getPostalCodeHigh() {
        return postalCodeHigh;
    }

    public void setPostalCodeHigh(String postalCodeHigh) {
        this.postalCodeHigh = postalCodeHigh;
    }

    public String getDestinationSurcharge() {
        return destinationSurcharge;
    }

    public void setDestinationSurcharge(String destinationSurcharge) {
        this.destinationSurcharge = destinationSurcharge;
    }
}
