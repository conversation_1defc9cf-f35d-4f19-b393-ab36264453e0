package com.nsy.api.tms.dao.entity.notic;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.api.tms.dao.entity.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * (NoticeTarget)表实体类
 *
 * <AUTHOR>
 * @since 2022-08-08 13:56:43
 */
@Entity
@Table(name = "notice_target")
@TableName("notice_target")
public class NoticeTargetEntity extends BaseMpEntity {

    //主键id
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer noticeTargetId;

    /**
     * 发送对象类型
     */
    private String noticeTargetType;

    /**
     * 发送对象名称
     */
    private String noticeTargetName;

    /**
     * 发送对象id
     */
    private Long noticeTargetUserId;
    /**
     * 发送对象名称
     */
    private String noticeTargetUserName;

    /**
     * 公告id
     */
    private Integer noticeMessageId;

    /**
     * 是否删除
     */
    private Integer isDeleted;


    public Integer getNoticeTargetId() {
        return noticeTargetId;
    }

    public void setNoticeTargetId(Integer noticeTargetId) {
        this.noticeTargetId = noticeTargetId;
    }

    public String getNoticeTargetType() {
        return noticeTargetType;
    }

    public void setNoticeTargetType(String noticeTargetType) {
        this.noticeTargetType = noticeTargetType;
    }

    public String getNoticeTargetName() {
        return noticeTargetName;
    }

    public void setNoticeTargetName(String noticeTargetName) {
        this.noticeTargetName = noticeTargetName;
    }

    public Long getNoticeTargetUserId() {
        return noticeTargetUserId;
    }

    public void setNoticeTargetUserId(Long noticeTargetUserId) {
        this.noticeTargetUserId = noticeTargetUserId;
    }

    public String getNoticeTargetUserName() {
        return noticeTargetUserName;
    }

    public void setNoticeTargetUserName(String noticeTargetUserName) {
        this.noticeTargetUserName = noticeTargetUserName;
    }

    public Integer getNoticeMessageId() {
        return noticeMessageId;
    }

    public void setNoticeMessageId(Integer noticeMessageId) {
        this.noticeMessageId = noticeMessageId;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}

