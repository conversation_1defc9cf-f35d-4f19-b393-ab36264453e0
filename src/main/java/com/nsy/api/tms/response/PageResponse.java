package com.nsy.api.tms.response;

import java.util.ArrayList;
import java.util.List;

public class PageResponse<T>  {
    private Long totalCount;

    private Integer number;

    private List<T> content = new ArrayList<>();

    public static <T> PageResponse<T> of(Long totalCount, Integer number) {
        PageResponse<T> pageResponse = new PageResponse<>();
        pageResponse.setTotalCount(totalCount);
        pageResponse.setNumber(number);
        return pageResponse;
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public List<T> getContent() {
        return content;
    }

    public void setContent(List<T> content) {
        this.content = content;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }
}
