package com.nsy.api.tms.response.download;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

public class TmsLogisticsChannelExportResponse {

    @ApiModelProperty(value = "id", name = "id")
    private Integer id;

    @ApiModelProperty(value = "物流渠道", name = "logisticsChannelName")
    private String logisticsChannelName;

    @ApiModelProperty(value = "渠道编码", name = "logistics_channel_code")
    private String logisticsChannelCode;

    @ApiModelProperty(value = "物流公司", name = "logistics_company")
    private String logisticsCompany;

    @ApiModelProperty(value = "是否启用:1--启用； 0--未启用", name = "statusStr")
    private String statusStr;

    @ApiModelProperty(value = "默认匹配最小重量", name = "defaultMatchMinWeight")
    private BigDecimal defaultMatchMinWeight;

    @ApiModelProperty(value = "默认匹配最大重量", name = "defaultMatchMaxWeight")
    private BigDecimal defaultMatchMaxWeight;

    @ApiModelProperty(value = "申报价值上限", name = "max_declared_value")
    private BigDecimal maxDeclaredValue;

    @ApiModelProperty(value = "备注", name = "description")
    private String description;

    @ApiModelProperty(value = "创建时间", name = "createDate")
    private String createDate;

    @ApiModelProperty(value = "更新时间", name = "updateDate")
    private String updateDate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLogisticsChannelName() {
        return logisticsChannelName;
    }

    public void setLogisticsChannelName(String logisticsChannelName) {
        this.logisticsChannelName = logisticsChannelName;
    }

    public String getLogisticsChannelCode() {
        return logisticsChannelCode;
    }

    public void setLogisticsChannelCode(String logisticsChannelCode) {
        this.logisticsChannelCode = logisticsChannelCode;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public BigDecimal getDefaultMatchMinWeight() {
        return defaultMatchMinWeight;
    }

    public void setDefaultMatchMinWeight(BigDecimal defaultMatchMinWeight) {
        this.defaultMatchMinWeight = defaultMatchMinWeight;
    }

    public BigDecimal getDefaultMatchMaxWeight() {
        return defaultMatchMaxWeight;
    }

    public void setDefaultMatchMaxWeight(BigDecimal defaultMatchMaxWeight) {
        this.defaultMatchMaxWeight = defaultMatchMaxWeight;
    }

    public BigDecimal getMaxDeclaredValue() {
        return maxDeclaredValue;
    }

    public void setMaxDeclaredValue(BigDecimal maxDeclaredValue) {
        this.maxDeclaredValue = maxDeclaredValue;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }
}
