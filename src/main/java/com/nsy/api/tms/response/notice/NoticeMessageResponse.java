package com.nsy.api.tms.response.notice;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.tms.enumeration.NoticeTargetTypeEnum;
import com.nsy.api.tms.enumeration.NoticeTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-08-05 15:40:50
 */
@ApiModel(value = "NoticeMessageResponse", description = "公告信息")
public class NoticeMessageResponse implements Serializable {

    private static final long serialVersionUID = 2290450332653718887L;
    @ApiModelProperty(value = "公告id", name = "noticeMessageId")
    private Integer noticeMessageId;

    @ApiModelProperty(value = "标题", name = "title")
    private String title;

    @ApiModelProperty(value = "公告内容", name = "content")
    private String content;

    @ApiModelProperty(value = "公告类型（1：通知公告；2：规章制度；3：操作指引）", name = "noticeType")
    private String noticeType;

    @ApiModelProperty(value = "标签", name = "tag")
    private String tag;

    @ApiModelProperty(value = "文档链接", name = "contentUrl")
    private String contentUrl;

    //发送对象-数据字典
    @ApiModelProperty(value = "发送对象编码-多个使用英文逗号拼接", name = "noticeTargetType")
    private String noticeTargetType;

    //发送对象（1：成衣供应商；2：面料供应商；3：时颖内部），多个以英文逗号分隔，如（1,2,3）
    @ApiModelProperty(value = "发送对象列表", name = "targetTypeList")
    private List<String> targetTypeList;

    @ApiModelProperty(value = "版本号", name = "version")
    private Integer version;

    @ApiModelProperty(value = "是否已读（0：未读；1：已读）", name = "read")
    private Integer read;

    @ApiModelProperty(value = "创建时间", name = "createDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty(value = "创建人", name = "createBy")
    private String createBy;

    @ApiModelProperty(value = "修改时间", name = "updateDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    @ApiModelProperty(value = "修改人", name = "updateBy")
    private String updateBy;

    @ApiModelProperty(value = "附件列表", name = "attachList")
    List<AttachResponse> attachList;

    public Integer getNoticeMessageId() {
        return noticeMessageId;
    }

    public void setNoticeMessageId(Integer noticeMessageId) {
        this.noticeMessageId = noticeMessageId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getNoticeType() {
        return noticeType;
    }

    public void setNoticeType(String noticeType) {
        this.noticeType = noticeType;
    }

    public String getNoticeTargetType() {
        return noticeTargetType;
    }

    public void setNoticeTargetType(String noticeTargetType) {
        this.noticeTargetType = noticeTargetType;
    }

    public String getNoticeTypeName() {
        if (Objects.nonNull(this.getNoticeType())) {
            return NoticeTypeEnum.getDescByCode(this.getNoticeType());
        }
        return null;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getContentUrl() {
        return contentUrl;
    }

    public void setContentUrl(String contentUrl) {
        this.contentUrl = contentUrl;
    }

    public List<String> getTargetTypeList() {
        return targetTypeList;
    }

    public void setTargetTypeList(List<String> targetTypeList) {
        this.targetTypeList = targetTypeList;
    }

    public List<String> getTargetTypeNameList() {
        if (CollectionUtils.isEmpty(this.getTargetTypeList())) {
            return null;
        }
        return this.getTargetTypeList().stream().map(targetType -> NoticeTargetTypeEnum.getDescByCode(targetType)).collect(Collectors.toList());
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getRead() {
        return read;
    }

    public void setRead(Integer read) {
        this.read = read;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public List<AttachResponse> getAttachList() {
        return attachList;
    }

    public void setAttachList(List<AttachResponse> attachList) {
        this.attachList = attachList;
    }
}
