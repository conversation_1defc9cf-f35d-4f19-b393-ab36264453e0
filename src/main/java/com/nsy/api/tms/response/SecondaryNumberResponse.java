package com.nsy.api.tms.response;

import java.math.BigDecimal;

public class SecondaryNumberResponse {

    private String secondaryNumber;

    private String logisticsMethod;

    private String logisticsChannelCode;

    private String packageStatus;

    private BigDecimal packageWeight;

    private String logisticsPlatformCarrier;

    public String getLogisticsPlatformCarrier() {
        return logisticsPlatformCarrier;
    }

    public void setLogisticsPlatformCarrier(String logisticsPlatformCarrier) {
        this.logisticsPlatformCarrier = logisticsPlatformCarrier;
    }

    public BigDecimal getPackageWeight() {
        return packageWeight;
    }

    public void setPackageWeight(BigDecimal packageWeight) {
        this.packageWeight = packageWeight;
    }

    public String getPackageStatus() {
        return packageStatus;
    }

    public void setPackageStatus(String packageStatus) {
        this.packageStatus = packageStatus;
    }

    public String getSecondaryNumber() {
        return secondaryNumber;
    }

    public void setSecondaryNumber(String secondaryNumber) {
        this.secondaryNumber = secondaryNumber;
    }

    public String getLogisticsMethod() {
        return logisticsMethod;
    }

    public void setLogisticsMethod(String logisticsMethod) {
        this.logisticsMethod = logisticsMethod;
    }

    public String getLogisticsChannelCode() {
        return logisticsChannelCode;
    }

    public void setLogisticsChannelCode(String logisticsChannelCode) {
        this.logisticsChannelCode = logisticsChannelCode;
    }
}
