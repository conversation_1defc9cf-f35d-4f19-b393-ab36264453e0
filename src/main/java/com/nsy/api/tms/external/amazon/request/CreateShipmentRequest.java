package com.nsy.api.tms.external.amazon.request;

import com.nsy.api.tms.external.amazon.enums.UnitOfLength;

import java.math.BigDecimal;
import java.util.List;

public class CreateShipmentRequest {

    private ShipmentRequestDetails shipmentRequestDetails;

    private String shippingServiceId;

    private List<AdditionalSellerInputs> shipmentLevelSellerInputsList;

    public ShipmentRequestDetails getShipmentRequestDetails() {
        return shipmentRequestDetails;
    }

    public void setShipmentRequestDetails(ShipmentRequestDetails shipmentRequestDetails) {
        this.shipmentRequestDetails = shipmentRequestDetails;
    }

    public String getShippingServiceId() {
        return shippingServiceId;
    }

    public void setShippingServiceId(String shippingServiceId) {
        this.shippingServiceId = shippingServiceId;
    }

    public List<AdditionalSellerInputs> getShipmentLevelSellerInputsList() {
        return shipmentLevelSellerInputsList;
    }

    public void setShipmentLevelSellerInputsList(List<AdditionalSellerInputs> shipmentLevelSellerInputsList) {
        this.shipmentLevelSellerInputsList = shipmentLevelSellerInputsList;
    }

    public static class AdditionalSellerInputs {
        private String additionalInputFieldName;

        private AdditionalSellerInput additionalSellerInput;

        public String getAdditionalInputFieldName() {
            return additionalInputFieldName;
        }

        public void setAdditionalInputFieldName(String additionalInputFieldName) {
            this.additionalInputFieldName = additionalInputFieldName;
        }

        public AdditionalSellerInput getAdditionalSellerInput() {
            return additionalSellerInput;
        }

        public void setAdditionalSellerInput(AdditionalSellerInput additionalSellerInput) {
            this.additionalSellerInput = additionalSellerInput;
        }
    }

    public static class AdditionalSellerInput {
        private String dataType;

        private String valueAsString;

        private Boolean valueAsBoolean;

        private Integer valueAsInteger;

        private String valueAsTimestamp;

        private Address valueAsAddress;

        private Weight valueAsWeight;

        private Length valueAsDimension;

        private CurrencyAmount valueAsCurrency;

        public String getDataType() {
            return dataType;
        }

        public void setDataType(String dataType) {
            this.dataType = dataType;
        }

        public String getValueAsString() {
            return valueAsString;
        }

        public void setValueAsString(String valueAsString) {
            this.valueAsString = valueAsString;
        }

        public Boolean getValueAsBoolean() {
            return valueAsBoolean;
        }

        public void setValueAsBoolean(Boolean valueAsBoolean) {
            this.valueAsBoolean = valueAsBoolean;
        }

        public Integer getValueAsInteger() {
            return valueAsInteger;
        }

        public void setValueAsInteger(Integer valueAsInteger) {
            this.valueAsInteger = valueAsInteger;
        }

        public String getValueAsTimestamp() {
            return valueAsTimestamp;
        }

        public void setValueAsTimestamp(String valueAsTimestamp) {
            this.valueAsTimestamp = valueAsTimestamp;
        }

        public Address getValueAsAddress() {
            return valueAsAddress;
        }

        public void setValueAsAddress(Address valueAsAddress) {
            this.valueAsAddress = valueAsAddress;
        }

        public Weight getValueAsWeight() {
            return valueAsWeight;
        }

        public void setValueAsWeight(Weight valueAsWeight) {
            this.valueAsWeight = valueAsWeight;
        }

        public Length getValueAsDimension() {
            return valueAsDimension;
        }

        public void setValueAsDimension(Length valueAsDimension) {
            this.valueAsDimension = valueAsDimension;
        }

        public CurrencyAmount getValueAsCurrency() {
            return valueAsCurrency;
        }

        public void setValueAsCurrency(CurrencyAmount valueAsCurrency) {
            this.valueAsCurrency = valueAsCurrency;
        }
    }

    public static class Address {

        private String name;


        private String addressLine1;


        private String addressLine2;


        private String addressLine3;


        private String districtOrCounty;


        private String email;


        private String city;


        private String stateOrProvinceCode;


        private String postalCode;


        private String countryCode;


        private String phone;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getAddressLine1() {
            return addressLine1;
        }

        public void setAddressLine1(String addressLine1) {
            this.addressLine1 = addressLine1;
        }

        public String getAddressLine2() {
            return addressLine2;
        }

        public void setAddressLine2(String addressLine2) {
            this.addressLine2 = addressLine2;
        }

        public String getAddressLine3() {
            return addressLine3;
        }

        public void setAddressLine3(String addressLine3) {
            this.addressLine3 = addressLine3;
        }

        public String getDistrictOrCounty() {
            return districtOrCounty;
        }

        public void setDistrictOrCounty(String districtOrCounty) {
            this.districtOrCounty = districtOrCounty;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getStateOrProvinceCode() {
            return stateOrProvinceCode;
        }

        public void setStateOrProvinceCode(String stateOrProvinceCode) {
            this.stateOrProvinceCode = stateOrProvinceCode;
        }

        public String getPostalCode() {
            return postalCode;
        }

        public void setPostalCode(String postalCode) {
            this.postalCode = postalCode;
        }

        public String getCountryCode() {
            return countryCode;
        }

        public void setCountryCode(String countryCode) {
            this.countryCode = countryCode;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }
    }

    public static class Weight {

        private BigDecimal value;


        private UnitOfWeight unit;

        public BigDecimal getValue() {
            return value;
        }

        public void setValue(BigDecimal value) {
            this.value = value;
        }

        public UnitOfWeight getUnit() {
            return unit;
        }

        public void setUnit(UnitOfWeight unit) {
            this.unit = unit;
        }
    }

    public static class Length {

        private BigDecimal value;


        private UnitOfLength unit;

        public BigDecimal getValue() {
            return value;
        }

        public void setValue(BigDecimal value) {
            this.value = value;
        }

        public UnitOfLength getUnit() {
            return unit;
        }

        public void setUnit(UnitOfLength unit) {
            this.unit = unit;
        }
    }

    public static class CurrencyAmount {

        private String currencyCode;


        private Double amount;

        public String getCurrencyCode() {
            return currencyCode;
        }

        public void setCurrencyCode(String currencyCode) {
            this.currencyCode = currencyCode;
        }

        public Double getAmount() {
            return amount;
        }

        public void setAmount(Double amount) {
            this.amount = amount;
        }
    }

    public static class ShipmentRequestDetails {

        private String amazonOrderId;


        private List<Item> itemList;


        private Address shipFromAddress;


        private PackageDimensions packageDimensions;


        private Weight weight;


        private ShippingServiceOptions shippingServiceOptions;

        public String getAmazonOrderId() {
            return amazonOrderId;
        }

        public void setAmazonOrderId(String amazonOrderId) {
            this.amazonOrderId = amazonOrderId;
        }

        public List<Item> getItemList() {
            return itemList;
        }

        public void setItemList(List<Item> itemList) {
            this.itemList = itemList;
        }

        public Address getShipFromAddress() {
            return shipFromAddress;
        }

        public void setShipFromAddress(Address shipFromAddress) {
            this.shipFromAddress = shipFromAddress;
        }

        public PackageDimensions getPackageDimensions() {
            return packageDimensions;
        }

        public void setPackageDimensions(PackageDimensions packageDimensions) {
            this.packageDimensions = packageDimensions;
        }

        public Weight getWeight() {
            return weight;
        }

        public void setWeight(Weight weight) {
            this.weight = weight;
        }

        public ShippingServiceOptions getShippingServiceOptions() {
            return shippingServiceOptions;
        }

        public void setShippingServiceOptions(ShippingServiceOptions shippingServiceOptions) {
            this.shippingServiceOptions = shippingServiceOptions;
        }
    }

    public static class Item {

        private String orderItemId;


        private Integer quantity;


        private List<AdditionalSellerInputs> itemLevelSellerInputsList;

        public String getOrderItemId() {
            return orderItemId;
        }

        public void setOrderItemId(String orderItemId) {
            this.orderItemId = orderItemId;
        }

        public Integer getQuantity() {
            return quantity;
        }

        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }

        public List<AdditionalSellerInputs> getItemLevelSellerInputsList() {
            return itemLevelSellerInputsList;
        }

        public void setItemLevelSellerInputsList(List<AdditionalSellerInputs> itemLevelSellerInputsList) {
            this.itemLevelSellerInputsList = itemLevelSellerInputsList;
        }
    }

    public static class PackageDimensions {

        private BigDecimal length;


        private BigDecimal width;


        private BigDecimal height;


        private UnitOfLength unit;

        public BigDecimal getLength() {
            return length;
        }

        public void setLength(BigDecimal length) {
            this.length = length;
        }

        public BigDecimal getWidth() {
            return width;
        }

        public void setWidth(BigDecimal width) {
            this.width = width;
        }

        public BigDecimal getHeight() {
            return height;
        }

        public void setHeight(BigDecimal height) {
            this.height = height;
        }

        public UnitOfLength getUnit() {
            return unit;
        }

        public void setUnit(UnitOfLength unit) {
            this.unit = unit;
        }
    }

    public static class ShippingServiceOptions {

        private DeliveryExperienceType deliveryExperience;


        private Boolean carrierWillPickUp;

        public DeliveryExperienceType getDeliveryExperience() {
            return deliveryExperience;
        }

        public void setDeliveryExperience(DeliveryExperienceType deliveryExperience) {
            this.deliveryExperience = deliveryExperience;
        }

        public Boolean getCarrierWillPickUp() {
            return carrierWillPickUp;
        }

        public void setCarrierWillPickUp(Boolean carrierWillPickUp) {
            this.carrierWillPickUp = carrierWillPickUp;
        }
    }

    public enum UnitOfWeight {
        OZ("oz"),

        G("g");

        private String value;

        UnitOfWeight(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum DeliveryExperienceType {
        DELIVERYCONFIRMATIONWITHADULTSIGNATURE("DeliveryConfirmationWithAdultSignature"),

        DELIVERYCONFIRMATIONWITHSIGNATURE("DeliveryConfirmationWithSignature"),

        DELIVERYCONFIRMATIONWITHOUTSIGNATURE("DeliveryConfirmationWithoutSignature"),

        NOTRACKING("NoTracking");

        private String value;

        DeliveryExperienceType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }
}
