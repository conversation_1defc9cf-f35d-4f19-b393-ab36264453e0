package com.nsy.api.tms.external.wms.response;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "ProductSpecInfo", description = "商品规格信息实体")
public class ProductSpecInfo {

    @ApiModelProperty(value = "商品规格信息主键Id", name = "productSpecInfoId")
    private Integer productSpecInfoId;

    @ApiModelProperty(value = "商品系统的product.id", name = "productId")
    private Integer productId;

    @ApiModelProperty(value = "商品系统的product_spec.id", name = "specId")
    private Integer specId;

    @ApiModelProperty(value = "ERP的EGProduct_ProductSpec.specID", name = "erpSpecId")
    private Integer erpSpecId;

    @ApiModelProperty(value = "商品sku", name = "sku")
    private String sku;

    @ApiModelProperty(value = "颜色", name = "color")
    private String color;

    @ApiModelProperty(value = "颜色代码", name = "colorCode")
    private String colorCode;

    @ApiModelProperty(value = "商品skc", name = "skc")
    private String skc;

    @ApiModelProperty(value = "尺寸", name = "size")
    private String size;

    @ApiModelProperty(value = "商品编码", name = "barcode")
    private String barcode;

    @ApiModelProperty(value = "原图地址", name = "imageUrl")
    private String imageUrl;

    @ApiModelProperty(value = "缩略图地址", name = "thumbnailImageUrl")
    private String thumbnailImageUrl;

    @ApiModelProperty(value = "预览图地址", name = "previewImageUrl")
    private String previewImageUrl;

    @ApiModelProperty(value = "预估重量", name = "weight")
    private BigDecimal weight;

    @ApiModelProperty(value = "实际重量", name = "actualWeight")
    private BigDecimal actualWeight;

    @ApiModelProperty(value = "体积", name = "volume")
    private BigDecimal volume;

    @ApiModelProperty(value = "体积重", name = "volumeWeight")
    private BigDecimal volumeWeight;

    @ApiModelProperty(value = "包装袋高度", name = "packageHeight")
    private BigDecimal packageHeight;

    @ApiModelProperty(value = "内部结算价", name = "settlementPrice")
    private BigDecimal settlementPrice;

    @ApiModelProperty(value = "网上售价", name = "userPrice")
    private BigDecimal userPrice;

    @ApiModelProperty(value = "吊牌价", name = "price")
    private BigDecimal price;

    @ApiModelProperty(value = "采购价", name = "purchasePrice")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "创建时间", name = "createDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty(value = "创建人", name = "createBy")
    private String createBy;

    @ApiModelProperty(value = "更新时间", name = "updateDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    @ApiModelProperty(value = "更新人", name = "updateBy")
    private String updateBy;

    @ApiModelProperty(value = "商品标签", name = "productTag")
    private List<String> productTag;

    @ApiModelProperty(value = "包装标识", name = "packageVacuum")
    private String packageVacuum;

    public List<String> getProductTag() {
        return productTag;
    }


    public String getPackageVacuum() {
        return packageVacuum;
    }

    public void setPackageVacuum(String packageVacuum) {
        this.packageVacuum = packageVacuum;
    }

    public void setProductTag(List<String> productTag) {
        this.productTag = productTag;
    }

    public BigDecimal getActualWeight() {
        return actualWeight;
    }

    public void setActualWeight(BigDecimal actualWeight) {
        this.actualWeight = actualWeight;
    }

    public Integer getErpSpecId() {
        return erpSpecId;
    }

    public void setErpSpecId(Integer erpSpecId) {
        this.erpSpecId = erpSpecId;
    }

    public BigDecimal getSettlementPrice() {
        return settlementPrice;
    }

    public void setSettlementPrice(BigDecimal settlementPrice) {
        this.settlementPrice = settlementPrice;
    }

    public BigDecimal getUserPrice() {
        return userPrice;
    }

    public void setUserPrice(BigDecimal userPrice) {
        this.userPrice = userPrice;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getPurchasePrice() {
        return purchasePrice;
    }

    public void setPurchasePrice(BigDecimal purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public Integer getProductSpecInfoId() {
        return this.productSpecInfoId;
    }

    public void setProductSpecInfoId(Integer productSpecInfoId) {
        this.productSpecInfoId = productSpecInfoId;
    }

    public Integer getProductId() {
        return this.productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getSpecId() {
        return this.specId;
    }

    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    public String getSku() {
        return this.sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getColor() {
        return this.color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getColorCode() {
        return this.colorCode;
    }

    public void setColorCode(String colorCode) {
        this.colorCode = colorCode;
    }

    public String getSkc() {
        return this.skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public String getSize() {
        return this.size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getBarcode() {
        return this.barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getImageUrl() {
        return this.imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getThumbnailImageUrl() {
        return this.thumbnailImageUrl;
    }

    public void setThumbnailImageUrl(String thumbnailImageUrl) {
        this.thumbnailImageUrl = thumbnailImageUrl;
    }

    public String getPreviewImageUrl() {
        return this.previewImageUrl;
    }

    public void setPreviewImageUrl(String previewImageUrl) {
        this.previewImageUrl = previewImageUrl;
    }

    public BigDecimal getWeight() {
        return this.weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getVolumeWeight() {
        return this.volumeWeight;
    }

    public void setVolumeWeight(BigDecimal volumeWeight) {
        this.volumeWeight = volumeWeight;
    }

    public BigDecimal getPackageHeight() {
        return this.packageHeight;
    }

    public void setPackageHeight(BigDecimal packageHeight) {
        this.packageHeight = packageHeight;
    }

    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateBy() {
        return this.createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return this.updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }
}
