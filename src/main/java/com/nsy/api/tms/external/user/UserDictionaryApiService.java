package com.nsy.api.tms.external.user;


import com.nsy.api.tms.external.user.response.BdDictionaryItem;
import com.nsy.api.tms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 *
 */
@Service
public class UserDictionaryApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(UserDictionaryApiService.class);
    @Autowired
    private RestTemplate restTemplate;
    @Value("${user.api.service.url}")
    private String userServiceUrl;

    public List<BdDictionaryItem> getDictionaryValues(String key) {
        String uri = String.format("%s/bd-dictionary-item/%s", userServiceUrl, key);
        return baseRequest(uri, key);
    }

    public List<BdDictionaryItem> getDictionaryItemLikeDictionaryName(String dictionaryName) {
        String url = String.format("%s/bd-dictionary-item/like/%s", userServiceUrl, dictionaryName);
        return baseRequest(url, dictionaryName);
    }

    public List<BdDictionaryItem> baseRequest(String url, String dictionaryName) {
        LOGGER.info("getDictionaryValues request:{}", dictionaryName);
        BdDictionaryItem[] items = this.restTemplate.getForObject(url, BdDictionaryItem[].class);
        LOGGER.info("getDictionaryValues response:{}", JsonMapper.toJson(items));
        if (Objects.isNull(items)) {
            return null;
        }
        return Arrays.asList(items);
    }
}
