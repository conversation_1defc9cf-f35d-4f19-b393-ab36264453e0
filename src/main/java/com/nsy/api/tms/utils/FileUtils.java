package com.nsy.api.tms.utils;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;

public class FileUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(FileUtils.class);

    /**
     * 只有一页pdf处理
     *
     * @param pdfPath    pdf地址路径
     * @param imagePath  图片地址
     * @param formatName 图片格式
     * @throws IOException
     */
    public static File pdf2SingleImage(String pdfPath, String imagePath, String formatName) throws IOException {
        PDDocument document = PDDocument.load(new File(pdfPath));
        PDFRenderer renderer = new PDFRenderer(document);
        File file = new File(imagePath);
        BufferedImage image = renderer.renderImageWithDPI(0, 130, ImageType.RGB);
        ImageIO.write(image, formatName, file);
        document.close();
        return file;
    }

    /**
     * 多页pdf处理
     *
     * @param pdfBytes
     * @param imagePathPrefix
     * @throws IOException
     */
    public static List<File> pdf2MultiImage(byte[] pdfBytes, String imagePathPrefix) throws IOException {
        List<File> imageList = new ArrayList<>();
        List<BufferedImage> picList = new ArrayList<>();
        PDDocument document = PDDocument.load(pdfBytes);
        PDFRenderer renderer = new PDFRenderer(document);
        int pageCount = document.getNumberOfPages();
        for (int i = 0; i < pageCount; i++) {
            BufferedImage image = renderer.renderImageWithDPI(i, 180, ImageType.RGB);
            picList.add(image);
            String imagePath = String.format("%s_%s.jpeg", imagePathPrefix, i);
            File itemImage = FileUtils.createFile(imagePath);
            ImageIO.write(image, "jpeg", itemImage);
            imageList.add(itemImage);
        }
        document.close();
        return imageList;
    }

    public static File toPdf(byte[] bytes, String fileName) throws IOException {
        ByteArrayInputStream byteInputStream = null;
        BufferedInputStream bis = null;
        FileOutputStream fos = null;
        BufferedOutputStream bos = null;
        File pdfFile = null;
        try {
            byteInputStream = new ByteArrayInputStream(bytes);
            pdfFile = FileUtils.createFile(fileName + ".pdf");
            bis = new BufferedInputStream(byteInputStream);
            fos = new FileOutputStream(pdfFile);
            bos = new BufferedOutputStream(fos);
            byte[] buffer = new byte[1024];
            int length = bis.read(buffer);
            while (length != -1) {
                bos.write(buffer, 0, length);
                length = bis.read(buffer);
            }
            bos.flush();
        } catch (IOException e) {

        } finally {
            bos.close();
            fos.close();
            bis.close();
        }
        return pdfFile;
    }

    public static File createFile(String descFileName) {
        File file = new File(descFileName);
        if (file.exists()) {
            return file;
        }
        // 如果文件所在的目录不存在，则创建目录
        if (!file.getParentFile().exists() && !file.getParentFile().mkdirs()) {
            LOGGER.debug("创建文件所在的目录失败!");
            throw new RuntimeException("创建文件所在的目录失败!");
        }
        // 创建文件
        try {
            file.createNewFile();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return file;
    }


    public static byte[] downloadPdf(String pdfUrl, RestTemplate restTemplate) {
        try {
            RequestEntity requestEntity = RequestEntity.get(new URI(pdfUrl)).build();
            ResponseEntity<byte[]> responseEntity = restTemplate.exchange(requestEntity, byte[].class);
            return responseEntity.getBody();
        } catch (URISyntaxException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static boolean isPdf(byte[] data) {
        if (data != null && data.length > 4 && data[0] == 0x25 && data[1] == 0x50 && data[2] == 0x44 && data[3] == 0x46 && data[4] == 0x2D) {
            if (data[5] == 0x31 && data[6] == 0x2E && data[7] == 0x33 && data[data.length - 7] == 0x25 && data[data.length - 6] == 0x25
                    && data[data.length - 5] == 0x45 && data[data.length - 4] == 0x4F && data[data.length - 3] == 0x46 && data[data.length - 2] == 0x20
                    && data[data.length - 1] == 0x0A) {
                return true;
            }
            if (data[5] == 0x31 && data[6] == 0x2E && data[7] == 0x34 && data[data.length - 6] == 0x25 && data[data.length - 5] == 0x25
                    && data[data.length - 4] == 0x45 && data[data.length - 3] == 0x4F && data[data.length - 2] == 0x46 && data[data.length - 1] == 0x0A) {
                return true;
            }
        }
        return false;
    }

}
