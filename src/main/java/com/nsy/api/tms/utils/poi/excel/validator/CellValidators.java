package com.nsy.api.tms.utils.poi.excel.validator;

import com.google.common.collect.Maps;
import com.nsy.api.core.apicore.util.Convert;
import com.nsy.api.core.apicore.util.StringUtils;

import java.util.Map;
import java.util.regex.Pattern;

/**
 * User: Emily
 * Date: 2018/7/23
 */
public class CellValidators {
    public static Map<String, Integer> doCheck(Integer rowNum, String value, CellValidator... validators) {
        StringBuilder errorMessage = new StringBuilder();
        Map<String, Integer> errorMsgMap = Maps.newHashMap();
        for (CellValidator validator : validators) {
            switch (validator.rule()) {
                case nullable:
                    if (StringUtils.equals("false", validator.value()) && !StringUtils.hasText(value)) {
                        errorMessage.append(String.format("%s%n", validator.message()));
                    }
                    break;
                case maxLength:
                    if (StringUtils.hasText(value) && value.length() > Convert.toInt(validator.value(), 0)) {
                        errorMessage.append(String.format("%s%n", validator.message()));
                    }
                    break;
                case minLength:
                    if (!StringUtils.hasText(value) || value.length() < Convert.toInt(validator.value(), 0)) {
                        errorMessage.append(String.format("%s%n", validator.message()));
                    }
                    break;
                case regular:
                    if (!Pattern.matches(validator.value(), value)) {
                        errorMessage.append(String.format("%s%n", validator.message()));
                    }
                    break;
                default:
                    break;
            }
        }
        if (errorMessage.length() > 0) {
            errorMsgMap.put(errorMessage.toString(), rowNum + 1);
        }
        return errorMsgMap;
    }
}
