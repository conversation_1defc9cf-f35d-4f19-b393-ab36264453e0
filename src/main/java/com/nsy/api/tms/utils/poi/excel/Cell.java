package com.nsy.api.tms.utils.poi.excel;

import com.nsy.api.tms.utils.poi.excel.converter.CellConverter;
import com.nsy.api.tms.utils.poi.excel.validator.CellValidator;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * Created by jun.
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Cell {
    int index();

    String title();

    Class<? extends CellConverter>[] converters() default {};

    CellValidator[] validators() default {};
}
