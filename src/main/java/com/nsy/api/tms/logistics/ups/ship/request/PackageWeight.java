package com.nsy.api.tms.logistics.ups.ship.request;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/19 19:18
 */
public class PackageWeight {
    @JsonProperty(value = "UnitOfMeasurement")
    private UnitOfMeasurement unitOfMeasurement;
    @JsonProperty(value = "Weight")
    private String weight;

    public UnitOfMeasurement getUnitOfMeasurement() {
        return unitOfMeasurement;
    }

    public void setUnitOfMeasurement(UnitOfMeasurement unitOfMeasurement) {
        this.unitOfMeasurement = unitOfMeasurement;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }
}
