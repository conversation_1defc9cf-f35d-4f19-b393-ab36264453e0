package com.nsy.api.tms.logistics.yuntu.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Date;
import java.util.List;

public class YuTuTrackRouteResponse extends YunTuBaseResponse {
    @JsonProperty(value = "Item")
    private Item item;

    public Item getItem() {
        return item;
    }

    public void setItem(Item item) {
        this.item = item;
    }

    public static class Item {
        @JsonProperty(value = "CountryCode")
        private String countryCode; // 目的地国家简码
        @JsonProperty(value = "WaybillNumber")
        private String waybillNumber; // 运单号
        @JsonProperty(value = "TrackingNumber")
        private String trackingNumber; // 跟踪号
        @JsonProperty(value = "CreatedBy")
        private String createdBy; // 创建人
        @JsonProperty(value = "PackageState")
        private int packageState; // 包裹状态0-未知，1-已提交 2-运输中 3-已签收，4-已收货，5-订单取消，6-投递失败，7-已退回
        @JsonProperty(value = "IntervalDays")
        private int intervalDays; //包裹签收天数
        @JsonProperty(value = "OrderTrackingDetails")
        private List<OrderTrackingDetail> orderTrackingDetails; // 订单跟踪详情

        public String getCountryCode() {
            return countryCode;
        }

        public void setCountryCode(String countryCode) {
            this.countryCode = countryCode;
        }

        public String getWaybillNumber() {
            return waybillNumber;
        }

        public void setWaybillNumber(String waybillNumber) {
            this.waybillNumber = waybillNumber;
        }

        public String getTrackingNumber() {
            return trackingNumber;
        }

        public void setTrackingNumber(String trackingNumber) {
            this.trackingNumber = trackingNumber;
        }

        public String getCreatedBy() {
            return createdBy;
        }

        public void setCreatedBy(String createdBy) {
            this.createdBy = createdBy;
        }

        public int getPackageState() {
            return packageState;
        }

        public void setPackageState(int packageState) {
            this.packageState = packageState;
        }

        public int getIntervalDays() {
            return intervalDays;
        }

        public void setIntervalDays(int intervalDays) {
            this.intervalDays = intervalDays;
        }

        public List<OrderTrackingDetail> getOrderTrackingDetails() {
            return orderTrackingDetails;
        }

        public void setOrderTrackingDetails(List<OrderTrackingDetail> orderTrackingDetails) {
            this.orderTrackingDetails = orderTrackingDetails;
        }


    }
    public static class OrderTrackingDetail {
        @JsonProperty(value = "ProcessDate")
        private Date processDate; //包裹请求日期
        @JsonProperty(value = "ProcessContent")
        private String processContent; //包裹请求内容
        @JsonProperty(value = "ProcessLocation")
        private String processLocation; //包裹请求地址

        public Date getProcessDate() {
            return processDate;
        }

        public void setProcessDate(Date processDate) {
            this.processDate = processDate;
        }

        public String getProcessContent() {
            return processContent;
        }

        public void setProcessContent(String processContent) {
            this.processContent = processContent;
        }

        public String getProcessLocation() {
            return processLocation;
        }

        public void setProcessLocation(String processLocation) {
            this.processLocation = processLocation;
        }
    }
}
