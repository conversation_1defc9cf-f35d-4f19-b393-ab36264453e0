package com.nsy.api.tms.logistics.ups.ship.response;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/19 23:24
 */
public class ShipFault {
    @JsonProperty(value = "faultcode")
    private String faultCode;
    @JsonProperty(value = "faultstring")
    private String faultString;
    @JsonProperty(value = "detail")
    private FaultDetail detail;

    public String getFaultCode() {
        return faultCode;
    }

    public void setFaultCode(String faultCode) {
        this.faultCode = faultCode;
    }

    public String getFaultString() {
        return faultString;
    }

    public void setFaultString(String faultString) {
        this.faultString = faultString;
    }

    public FaultDetail getDetail() {
        return detail;
    }

    public void setDetail(FaultDetail detail) {
        this.detail = detail;
    }
}
