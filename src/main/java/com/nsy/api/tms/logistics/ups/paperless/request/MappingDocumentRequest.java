package com.nsy.api.tms.logistics.ups.paperless.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.nsy.api.tms.logistics.ups.paperless.UPSSecurity;

/**
 * HXD
 * 2021/4/6
 **/
public class MappingDocumentRequest {

    @J<PERSON><PERSON>ield(name = "UPSSecurity")
    private UPSSecurity upsSecurity;

    @JSONField(name = "PushToImageRepositoryRequest")
    private PushToImageRepositoryRequest pushToImageRepositoryRequest;

    public UPSSecurity getUpsSecurity() {
        return upsSecurity;
    }

    public void setUpsSecurity(UPSSecurity upsSecurity) {
        this.upsSecurity = upsSecurity;
    }

    public PushToImageRepositoryRequest getPushToImageRepositoryRequest() {
        return pushToImageRepositoryRequest;
    }

    public void setPushToImageRepositoryRequest(PushToImageRepositoryRequest pushToImageRepositoryRequest) {
        this.pushToImageRepositoryRequest = pushToImageRepositoryRequest;
    }
}
