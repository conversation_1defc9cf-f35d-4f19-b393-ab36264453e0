package com.nsy.api.tms.logistics.ups.ship.response.onepiece;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/19 21:13
 */
public class UpsOnePieceShipmentResponse {
    @JsonProperty(value = "ShipmentResponse")
    private OnePieceShipmentResponse shipmentResponse;

    public OnePieceShipmentResponse getShipmentResponse() {
        return shipmentResponse;
    }

    public void setShipmentResponse(OnePieceShipmentResponse shipmentResponse) {
        this.shipmentResponse = shipmentResponse;
    }
}
