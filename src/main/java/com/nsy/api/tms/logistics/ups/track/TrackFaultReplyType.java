package com.nsy.api.tms.logistics.ups.track;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/18 19:45
 */
public class TrackFaultReplyType {
    @JsonProperty(value = "faultcode")
    private String faultCode;
    @JsonProperty(value = "faultstring")
    private String faultString;
    @JsonProperty(value ="detail")
    private FaultReplyDetail detail;

    public String getFaultCode() {
        return faultCode;
    }

    public void setFaultCode(String faultCode) {
        this.faultCode = faultCode;
    }

    public String getFaultString() {
        return faultString;
    }

    public void setFaultString(String faultString) {
        this.faultString = faultString;
    }

    public FaultReplyDetail getDetail() {
        return detail;
    }

    public void setDetail(FaultReplyDetail detail) {
        this.detail = detail;
    }
}
