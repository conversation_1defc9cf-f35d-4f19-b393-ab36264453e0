package com.nsy.api.tms.logistics.tnt.response.track;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/24 9:47
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "OriginCountry")
public class OriginCountry {
    @XmlElement(name = "CountryCode")
    private String countryCode;
    @XmlElement(name = "CountryName")
    private String countryName;

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }
}
