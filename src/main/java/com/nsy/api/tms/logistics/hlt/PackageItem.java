package com.nsy.api.tms.logistics.hlt;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>packageItem complex type�� Java �ࡣ
 *
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 *
 * <pre>
 * &lt;complexType name="packageItem">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="height" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
 *         &lt;element name="length" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
 *         &lt;element name="trackingNo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="weight" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
 *         &lt;element name="width" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "packageItem", propOrder = {
        "height",
        "length",
        "trackingNo",
        "weight",
        "width"
})
public class PackageItem {

    protected Double height;
    protected Double length;
    protected String trackingNo;
    protected Double weight;
    protected Double width;

    /**
     * ��ȡheight���Ե�ֵ��
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getHeight() {
        return height;
    }

    /**
     * ����height���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setHeight(Double value) {
        this.height = value;
    }

    /**
     * ��ȡlength���Ե�ֵ��
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getLength() {
        return length;
    }

    /**
     * ����length���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setLength(Double value) {
        this.length = value;
    }

    /**
     * ��ȡtrackingNo���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getTrackingNo() {
        return trackingNo;
    }

    /**
     * ����trackingNo���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setTrackingNo(String value) {
        this.trackingNo = value;
    }

    /**
     * ��ȡweight���Ե�ֵ��
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getWeight() {
        return weight;
    }

    /**
     * ����weight���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setWeight(Double value) {
        this.weight = value;
    }

    /**
     * ��ȡwidth���Ե�ֵ��
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getWidth() {
        return width;
    }

    /**
     * ����width���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setWidth(Double value) {
        this.width = value;
    }

}
