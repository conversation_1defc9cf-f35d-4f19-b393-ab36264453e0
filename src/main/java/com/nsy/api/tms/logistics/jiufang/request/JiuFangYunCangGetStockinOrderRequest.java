package com.nsy.api.tms.logistics.jiufang.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-03-27 9:38
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JiuFangYunCangGetStockinOrderRequest extends JiuFangYunPageBaseRequest {

    /**
     * 入库单号
     */
    @JsonProperty("receiving_code")
    private String receivingCode;

    /**
     * 参考号，即时颖订单号
     */
    @JsonProperty("reference_no")
    private String referenceNo;

    public String getReceivingCode() {
        return receivingCode;
    }

    public void setReceivingCode(String receivingCode) {
        this.receivingCode = receivingCode;
    }

    public String getReferenceNo() {
        return referenceNo;
    }

    public void setReferenceNo(String referenceNo) {
        this.referenceNo = referenceNo;
    }
}
