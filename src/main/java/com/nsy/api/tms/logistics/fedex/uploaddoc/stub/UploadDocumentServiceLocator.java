/**
 * UploadDocumentServiceLocator.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.nsy.api.tms.logistics.fedex.uploaddoc.stub;

public class UploadDocumentServiceLocator extends org.apache.axis.client.Service implements UploadDocumentService {

    public UploadDocumentServiceLocator() {
    }


    public UploadDocumentServiceLocator(org.apache.axis.EngineConfiguration config) {
        super(config);
    }

    public UploadDocumentServiceLocator(java.lang.String wsdlLoc, javax.xml.namespace.QName sName) throws javax.xml.rpc.ServiceException {
        super(wsdlLoc, sName);
    }

    // Use to get a proxy class for UploadDocumentServicePort
    private java.lang.String UploadDocumentServicePort_address = "https://ws.fedex.com:443/web-services/uploaddocument";

    public java.lang.String getUploadDocumentServicePortAddress() {
        return UploadDocumentServicePort_address;
    }

    // The WSDD service name defaults to the port name.
    private java.lang.String UploadDocumentServicePortWSDDServiceName = "UploadDocumentServicePort";

    public java.lang.String getUploadDocumentServicePortWSDDServiceName() {
        return UploadDocumentServicePortWSDDServiceName;
    }

    public void setUploadDocumentServicePortWSDDServiceName(java.lang.String name) {
        UploadDocumentServicePortWSDDServiceName = name;
    }

    public UploadDocumentPortType getUploadDocumentServicePort() throws javax.xml.rpc.ServiceException {
       java.net.URL endpoint;
        try {
            endpoint = new java.net.URL(UploadDocumentServicePort_address);
        }
        catch (java.net.MalformedURLException e) {
            throw new javax.xml.rpc.ServiceException(e);
        }
        return getUploadDocumentServicePort(endpoint);
    }

    public UploadDocumentPortType getUploadDocumentServicePort(java.net.URL portAddress) throws javax.xml.rpc.ServiceException {
        try {
            UploadDocumentServiceSoapBindingStub _stub = new UploadDocumentServiceSoapBindingStub(portAddress, this);
            _stub.setPortName(getUploadDocumentServicePortWSDDServiceName());
            return _stub;
        }
        catch (org.apache.axis.AxisFault e) {
            return null;
        }
    }

    public void setUploadDocumentServicePortEndpointAddress(java.lang.String address) {
        UploadDocumentServicePort_address = address;
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        try {
            if (UploadDocumentPortType.class.isAssignableFrom(serviceEndpointInterface)) {
                UploadDocumentServiceSoapBindingStub _stub = new UploadDocumentServiceSoapBindingStub(new java.net.URL(UploadDocumentServicePort_address), this);
                _stub.setPortName(getUploadDocumentServicePortWSDDServiceName());
                return _stub;
            }
        }
        catch (java.lang.Throwable t) {
            throw new javax.xml.rpc.ServiceException(t);
        }
        throw new javax.xml.rpc.ServiceException("There is no stub implementation for the interface:  " + (serviceEndpointInterface == null ? "null" : serviceEndpointInterface.getName()));
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(javax.xml.namespace.QName portName, Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        if (portName == null) {
            return getPort(serviceEndpointInterface);
        }
        java.lang.String inputPortName = portName.getLocalPart();
        if ("UploadDocumentServicePort".equals(inputPortName)) {
            return getUploadDocumentServicePort();
        }
        else  {
            java.rmi.Remote _stub = getPort(serviceEndpointInterface);
            ((org.apache.axis.client.Stub) _stub).setPortName(portName);
            return _stub;
        }
    }

    public javax.xml.namespace.QName getServiceName() {
        return new javax.xml.namespace.QName("http://fedex.com/ws/uploaddocument/v11", "UploadDocumentService");
    }

    private java.util.HashSet ports = null;

    public java.util.Iterator getPorts() {
        if (ports == null) {
            ports = new java.util.HashSet();
            ports.add(new javax.xml.namespace.QName("http://fedex.com/ws/uploaddocument/v11", "UploadDocumentServicePort"));
        }
        return ports.iterator();
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(java.lang.String portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        
if ("UploadDocumentServicePort".equals(portName)) {
            setUploadDocumentServicePortEndpointAddress(address);
        }
        else 
{ // Unknown Port Name
            throw new javax.xml.rpc.ServiceException(" Cannot set Endpoint Address for Unknown Port" + portName);
        }
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(javax.xml.namespace.QName portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        setEndpointAddress(portName.getLocalPart(), address);
    }

}
