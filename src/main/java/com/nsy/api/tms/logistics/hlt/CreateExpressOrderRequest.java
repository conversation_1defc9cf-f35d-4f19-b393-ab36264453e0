package com.nsy.api.tms.logistics.hlt;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>createExpressOrderRequest complex type�� Java �ࡣ
 *
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 *
 * <pre>
 * &lt;complexType name="createExpressOrderRequest">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="additionalJson" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="cargoCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codCurrency" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codSum" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
 *         &lt;element name="consigneeCompanyName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="consigneeMobile" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="consigneeName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="consigneePostcode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="consigneeStreetNo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="consigneeTelephone" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="declareItems" type="{http://service.hop.service.ws.hlt.com/}declareItem" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="destinationCountryCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="dutiesPayment" type="{http://service.hop.service.ws.hlt.com/}payment" minOccurs="0"/>
 *         &lt;element name="goodsCategory" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="goodsDescription" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="height" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
 *         &lt;element name="insured" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="length" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
 *         &lt;element name="memo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="orderNo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="originCountryCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="packageItems" type="{http://service.hop.service.ws.hlt.com/}packageItem" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="pieces" type="{http://www.w3.org/2001/XMLSchema}long" minOccurs="0"/>
 *         &lt;element name="platformNo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="province" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="shipperAddress" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="shipperCity" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="shipperCompanyName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="shipperMobile" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="shipperName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="shipperPostcode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="shipperProvince" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="shipperStreet" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="shipperStreetNo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="shipperTelephone" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="shippingPayment" type="{http://service.hop.service.ws.hlt.com/}payment" minOccurs="0"/>
 *         &lt;element name="street" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="trackingNo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="transportWayCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="weight" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
 *         &lt;element name="width" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "createExpressOrderRequest", propOrder = {
        "additionalJson",
        "cargoCode",
        "city",
        "codCurrency",
        "codSum",
        "consigneeCompanyName",
        "consigneeMobile",
        "consigneeName",
        "consigneePostcode",
        "consigneeStreetNo",
        "consigneeTelephone",
        "declareItems",
        "destinationCountryCode",
        "dutiesPayment",
        "goodsCategory",
        "goodsDescription",
        "height",
        "insured",
        "length",
        "memo",
        "orderNo",
        "originCountryCode",
        "packageItems",
        "pieces",
        "platformNo",
        "province",
        "shipperAddress",
        "shipperCity",
        "shipperCompanyName",
        "shipperMobile",
        "shipperName",
        "shipperPostcode",
        "shipperProvince",
        "shipperStreet",
        "shipperStreetNo",
        "shipperTelephone",
        "shippingPayment",
        "street",
        "trackingNo",
        "transportWayCode",
        "weight",
        "width"
})
public class CreateExpressOrderRequest {

    protected String additionalJson;
    protected String cargoCode;
    protected String city;
    protected String codCurrency;
    protected Double codSum;
    protected String consigneeCompanyName;
    protected String consigneeMobile;
    protected String consigneeName;
    protected String consigneePostcode;
    protected String consigneeStreetNo;
    protected String consigneeTelephone;
    @XmlElement(nillable = true)
    protected List<DeclareItem> declareItems;
    protected String destinationCountryCode;
    protected Payment dutiesPayment;
    protected String goodsCategory;
    protected String goodsDescription;
    protected Double height;
    protected String insured;
    protected Double length;
    protected String memo;
    protected String orderNo;
    protected String originCountryCode;
    @XmlElement(nillable = true)
    protected List<PackageItem> packageItems;
    protected Long pieces;
    protected String platformNo;
    protected String province;
    protected String shipperAddress;
    protected String shipperCity;
    protected String shipperCompanyName;
    protected String shipperMobile;
    protected String shipperName;
    protected String shipperPostcode;
    protected String shipperProvince;
    protected String shipperStreet;
    protected String shipperStreetNo;
    protected String shipperTelephone;
    protected Payment shippingPayment;
    protected String street;
    protected String trackingNo;
    protected String transportWayCode;
    protected Double weight;
    protected Double width;

    /**
     * ��ȡadditionalJson���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getAdditionalJson() {
        return additionalJson;
    }

    /**
     * ����additionalJson���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setAdditionalJson(String value) {
        this.additionalJson = value;
    }

    /**
     * ��ȡcargoCode���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getCargoCode() {
        return cargoCode;
    }

    /**
     * ����cargoCode���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCargoCode(String value) {
        this.cargoCode = value;
    }

    /**
     * ��ȡcity���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getCity() {
        return city;
    }

    /**
     * ����city���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCity(String value) {
        this.city = value;
    }

    /**
     * ��ȡcodCurrency���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getCodCurrency() {
        return codCurrency;
    }

    /**
     * ����codCurrency���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCodCurrency(String value) {
        this.codCurrency = value;
    }

    /**
     * ��ȡcodSum���Ե�ֵ��
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getCodSum() {
        return codSum;
    }

    /**
     * ����codSum���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setCodSum(Double value) {
        this.codSum = value;
    }

    /**
     * ��ȡconsigneeCompanyName���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getConsigneeCompanyName() {
        return consigneeCompanyName;
    }

    /**
     * ����consigneeCompanyName���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setConsigneeCompanyName(String value) {
        this.consigneeCompanyName = value;
    }

    /**
     * ��ȡconsigneeMobile���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getConsigneeMobile() {
        return consigneeMobile;
    }

    /**
     * ����consigneeMobile���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setConsigneeMobile(String value) {
        this.consigneeMobile = value;
    }

    /**
     * ��ȡconsigneeName���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getConsigneeName() {
        return consigneeName;
    }

    /**
     * ����consigneeName���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setConsigneeName(String value) {
        this.consigneeName = value;
    }

    /**
     * ��ȡconsigneePostcode���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getConsigneePostcode() {
        return consigneePostcode;
    }

    /**
     * ����consigneePostcode���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setConsigneePostcode(String value) {
        this.consigneePostcode = value;
    }

    /**
     * ��ȡconsigneeStreetNo���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getConsigneeStreetNo() {
        return consigneeStreetNo;
    }

    /**
     * ����consigneeStreetNo���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setConsigneeStreetNo(String value) {
        this.consigneeStreetNo = value;
    }

    /**
     * ��ȡconsigneeTelephone���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getConsigneeTelephone() {
        return consigneeTelephone;
    }

    /**
     * ����consigneeTelephone���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setConsigneeTelephone(String value) {
        this.consigneeTelephone = value;
    }

    /**
     * Gets the value of the declareItems property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the declareItems property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDeclareItems().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link DeclareItem }
     */
    public List<DeclareItem> getDeclareItems() {
        if (declareItems == null) {
            declareItems = new ArrayList<DeclareItem>();
        }
        return this.declareItems;
    }

    /**
     * ��ȡdestinationCountryCode���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getDestinationCountryCode() {
        return destinationCountryCode;
    }

    /**
     * ����destinationCountryCode���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDestinationCountryCode(String value) {
        this.destinationCountryCode = value;
    }

    /**
     * ��ȡdutiesPayment���Ե�ֵ��
     *
     * @return possible object is
     * {@link Payment }
     */
    public Payment getDutiesPayment() {
        return dutiesPayment;
    }

    /**
     * ����dutiesPayment���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link Payment }
     */
    public void setDutiesPayment(Payment value) {
        this.dutiesPayment = value;
    }

    /**
     * ��ȡgoodsCategory���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getGoodsCategory() {
        return goodsCategory;
    }

    /**
     * ����goodsCategory���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setGoodsCategory(String value) {
        this.goodsCategory = value;
    }

    /**
     * ��ȡgoodsDescription���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getGoodsDescription() {
        return goodsDescription;
    }

    /**
     * ����goodsDescription���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setGoodsDescription(String value) {
        this.goodsDescription = value;
    }

    /**
     * ��ȡheight���Ե�ֵ��
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getHeight() {
        return height;
    }

    /**
     * ����height���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setHeight(Double value) {
        this.height = value;
    }

    /**
     * ��ȡinsured���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getInsured() {
        return insured;
    }

    /**
     * ����insured���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setInsured(String value) {
        this.insured = value;
    }

    /**
     * ��ȡlength���Ե�ֵ��
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getLength() {
        return length;
    }

    /**
     * ����length���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setLength(Double value) {
        this.length = value;
    }

    /**
     * ��ȡmemo���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getMemo() {
        return memo;
    }

    /**
     * ����memo���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setMemo(String value) {
        this.memo = value;
    }

    /**
     * ��ȡorderNo���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * ����orderNo���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setOrderNo(String value) {
        this.orderNo = value;
    }

    /**
     * ��ȡoriginCountryCode���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getOriginCountryCode() {
        return originCountryCode;
    }

    /**
     * ����originCountryCode���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setOriginCountryCode(String value) {
        this.originCountryCode = value;
    }

    /**
     * Gets the value of the packageItems property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the packageItems property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPackageItems().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PackageItem }
     */
    public List<PackageItem> getPackageItems() {
        if (packageItems == null) {
            packageItems = new ArrayList<PackageItem>();
        }
        return this.packageItems;
    }

    /**
     * ��ȡpieces���Ե�ֵ��
     *
     * @return possible object is
     * {@link Long }
     */
    public Long getPieces() {
        return pieces;
    }

    /**
     * ����pieces���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link Long }
     */
    public void setPieces(Long value) {
        this.pieces = value;
    }

    /**
     * ��ȡplatformNo���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getPlatformNo() {
        return platformNo;
    }

    /**
     * ����platformNo���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPlatformNo(String value) {
        this.platformNo = value;
    }

    /**
     * ��ȡprovince���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getProvince() {
        return province;
    }

    /**
     * ����province���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setProvince(String value) {
        this.province = value;
    }

    /**
     * ��ȡshipperAddress���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getShipperAddress() {
        return shipperAddress;
    }

    /**
     * ����shipperAddress���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setShipperAddress(String value) {
        this.shipperAddress = value;
    }

    /**
     * ��ȡshipperCity���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getShipperCity() {
        return shipperCity;
    }

    /**
     * ����shipperCity���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setShipperCity(String value) {
        this.shipperCity = value;
    }

    /**
     * ��ȡshipperCompanyName���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getShipperCompanyName() {
        return shipperCompanyName;
    }

    /**
     * ����shipperCompanyName���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setShipperCompanyName(String value) {
        this.shipperCompanyName = value;
    }

    /**
     * ��ȡshipperMobile���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getShipperMobile() {
        return shipperMobile;
    }

    /**
     * ����shipperMobile���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setShipperMobile(String value) {
        this.shipperMobile = value;
    }

    /**
     * ��ȡshipperName���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getShipperName() {
        return shipperName;
    }

    /**
     * ����shipperName���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setShipperName(String value) {
        this.shipperName = value;
    }

    /**
     * ��ȡshipperPostcode���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getShipperPostcode() {
        return shipperPostcode;
    }

    /**
     * ����shipperPostcode���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setShipperPostcode(String value) {
        this.shipperPostcode = value;
    }

    /**
     * ��ȡshipperProvince���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getShipperProvince() {
        return shipperProvince;
    }

    /**
     * ����shipperProvince���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setShipperProvince(String value) {
        this.shipperProvince = value;
    }

    /**
     * ��ȡshipperStreet���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getShipperStreet() {
        return shipperStreet;
    }

    /**
     * ����shipperStreet���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setShipperStreet(String value) {
        this.shipperStreet = value;
    }

    /**
     * ��ȡshipperStreetNo���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getShipperStreetNo() {
        return shipperStreetNo;
    }

    /**
     * ����shipperStreetNo���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setShipperStreetNo(String value) {
        this.shipperStreetNo = value;
    }

    /**
     * ��ȡshipperTelephone���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getShipperTelephone() {
        return shipperTelephone;
    }

    /**
     * ����shipperTelephone���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setShipperTelephone(String value) {
        this.shipperTelephone = value;
    }

    /**
     * ��ȡshippingPayment���Ե�ֵ��
     *
     * @return possible object is
     * {@link Payment }
     */
    public Payment getShippingPayment() {
        return shippingPayment;
    }

    /**
     * ����shippingPayment���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link Payment }
     */
    public void setShippingPayment(Payment value) {
        this.shippingPayment = value;
    }

    /**
     * ��ȡstreet���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getStreet() {
        return street;
    }

    /**
     * ����street���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setStreet(String value) {
        this.street = value;
    }

    /**
     * ��ȡtrackingNo���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getTrackingNo() {
        return trackingNo;
    }

    /**
     * ����trackingNo���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setTrackingNo(String value) {
        this.trackingNo = value;
    }

    /**
     * ��ȡtransportWayCode���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getTransportWayCode() {
        return transportWayCode;
    }

    /**
     * ����transportWayCode���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setTransportWayCode(String value) {
        this.transportWayCode = value;
    }

    /**
     * ��ȡweight���Ե�ֵ��
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getWeight() {
        return weight;
    }

    /**
     * ����weight���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setWeight(Double value) {
        this.weight = value;
    }

    /**
     * ��ȡwidth���Ե�ֵ��
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getWidth() {
        return width;
    }

    /**
     * ����width���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setWidth(Double value) {
        this.width = value;
    }

}
