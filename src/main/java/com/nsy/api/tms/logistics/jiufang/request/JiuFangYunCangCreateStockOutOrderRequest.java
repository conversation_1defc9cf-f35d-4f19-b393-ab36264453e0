package com.nsy.api.tms.logistics.jiufang.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.nsy.api.tms.request.BaseLogisticsOrderRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-03-27 13:54
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JiuFangYunCangCreateStockOutOrderRequest extends BaseLogisticsOrderRequest {

    /**
     * 订单参考号（建议使用平台单号）
     */
    @JsonProperty("reference_no")
    private String referenceNo;

    /**
     * 平台，ALIEXPRESS, AMAZON, B2C, EBAY, OTHER 默认OTHER，temu半托管平台填SEMITEMU
     */
    @JsonProperty("platform")
    private String platform;

    /**
     * 配送方式，参考getShippingMethod，如果配置了(CPCAINIAO)，该值就只能传配置的(CPSHIPPINGMETHOD)
     */
    @JsonProperty("shipping_method")
    private String shippingMethod;

    /**
     * 配送仓库，参考getWarehouse，配置(checkCustomerWarehousesCloud)开启必传
     */
    @JsonProperty("warehouse_code")
    private String warehouseCode;

    /**
     * 收件人国家，参考getCountry
     */
    @JsonProperty("country_code")
    private String countryCode;

    /**
     * 省/州/府
     */
    @JsonProperty("province")
    private String province;

    /**
     * 城市/区
     */
    @JsonProperty("city")
    private String city;

    /**
     * 收件人区
     */
    @JsonProperty("district")
    private String district;

    /**
     * 地址1 (length:500)
     */
    @JsonProperty("address1")
    private String address1;

    /**
     * 地址2 (length:216)
     */
    @JsonProperty("address2")
    private String address2;

    /**
     * 地址3 (length:216)
     */
    @JsonProperty("address3")
    private String address3;

    /**
     * 邮编；收件人国家为菲律宾，邮编非必填
     */
    @JsonProperty("zipcode")
    private String zipcode;

    /**
     * 收件人证件号
     */
    @JsonProperty("license")
    private String license;

    /**
     * 门牌号
     */
    @JsonProperty("doorplate")
    private String doorplate;

    /**
     * 公司名
     */
    @JsonProperty("company")
    private String company;

    /**
     * 收件人姓名
     */
    @JsonProperty("name")
    private String name;

    /**
     * 收件人联系方式
     */
    @JsonProperty("phone")
    private String phone;

    /**
     * 收件人联系方式2
     */
    @JsonProperty("cell_phone")
    private String cellPhone;

    /**
     * 收件人电话分机
     */
    @JsonProperty("phone_extension")
    private String phoneExtension;

    /**
     * 收件人邮箱
     */
    @JsonProperty("email")
    private String email;


    /**
     * 币种
     */
    @JsonProperty("order_cod_currency")
    private String orderCodCurrency;

    /**
     * 订单说明
     */
    @JsonProperty("order_desc")
    private String orderDesc;

    /**
     * 操作员留言-同步WMS订单备注中
     */
    @JsonProperty("remark")
    private String remark;


    /**
     * 是否审核，0新建不审核(草稿状态)，1新建并审核，默认为0，审核通过之后，不可编辑
     */
    @JsonProperty("verify")
    private Integer verify;

    /**
     * 是否强制审核(如欠费，缺货时是否审核到仓配系统)，0不强制，1强制，默认为0当verify==1时生效
     */
    @JsonProperty("forceVerify")
    private Integer forceVerify;

    /**
     * 订单明细
     */
    @JsonProperty("items")
    private List<JiuFangYunCangCreateStockOutOrderItemRequest> items;

    public String getReferenceNo() {
        return referenceNo;
    }

    public void setReferenceNo(String referenceNo) {
        this.referenceNo = referenceNo;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getShippingMethod() {
        return shippingMethod;
    }

    public void setShippingMethod(String shippingMethod) {
        this.shippingMethod = shippingMethod;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getAddress1() {
        return address1;
    }

    public void setAddress1(String address1) {
        this.address1 = address1;
    }

    public String getAddress2() {
        return address2;
    }

    public void setAddress2(String address2) {
        this.address2 = address2;
    }

    public String getAddress3() {
        return address3;
    }

    public void setAddress3(String address3) {
        this.address3 = address3;
    }

    public String getZipcode() {
        return zipcode;
    }

    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }

    public String getLicense() {
        return license;
    }

    public void setLicense(String license) {
        this.license = license;
    }

    public String getDoorplate() {
        return doorplate;
    }

    public void setDoorplate(String doorplate) {
        this.doorplate = doorplate;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

    public String getPhoneExtension() {
        return phoneExtension;
    }

    public void setPhoneExtension(String phoneExtension) {
        this.phoneExtension = phoneExtension;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getOrderCodCurrency() {
        return orderCodCurrency;
    }

    public void setOrderCodCurrency(String orderCodCurrency) {
        this.orderCodCurrency = orderCodCurrency;
    }

    public String getOrderDesc() {
        return orderDesc;
    }

    public void setOrderDesc(String orderDesc) {
        this.orderDesc = orderDesc;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getVerify() {
        return verify;
    }

    public void setVerify(Integer verify) {
        this.verify = verify;
    }

    public Integer getForceVerify() {
        return forceVerify;
    }

    public void setForceVerify(Integer forceVerify) {
        this.forceVerify = forceVerify;
    }

    public List<JiuFangYunCangCreateStockOutOrderItemRequest> getItems() {
        return items;
    }

    public void setItems(List<JiuFangYunCangCreateStockOutOrderItemRequest> items) {
        this.items = items;
    }
}
