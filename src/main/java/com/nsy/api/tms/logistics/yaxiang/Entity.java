package com.nsy.api.tms.logistics.yaxiang;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public class Entity {
    @JsonProperty(value = "Code")
    private String code;

    @JsonProperty(value = "Msg")
    private String msg;

    @JsonProperty(value = "Obj_Json")
    private String objJson;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getObjJson() {
        return objJson;
    }

    public void setObjJson(String objJson) {
        this.objJson = objJson;
    }
}
