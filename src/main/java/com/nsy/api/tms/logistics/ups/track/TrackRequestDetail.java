package com.nsy.api.tms.logistics.ups.track;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/18 11:02
 */
public class TrackRequestDetail {
    @JsonProperty(value = "RequestOption")
    private String requestOption;
    @JsonProperty(value = "TransactionReference")
    private UpsTransactionReference transactionReference;

    public String getRequestOption() {
        return requestOption;
    }

    public void setRequestOption(String requestOption) {
        this.requestOption = requestOption;
    }

    public UpsTransactionReference getTransactionReference() {
        return transactionReference;
    }

    public void setTransactionReference(UpsTransactionReference transactionReference) {
        this.transactionReference = transactionReference;
    }
}
