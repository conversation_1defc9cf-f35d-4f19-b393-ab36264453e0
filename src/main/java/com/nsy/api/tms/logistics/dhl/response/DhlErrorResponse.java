package com.nsy.api.tms.logistics.dhl.response;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlRootElement( namespace = "http://www.dhl.com", name = "ErrorResponse")
@XmlAccessorType(XmlAccessType.FIELD)
public class DhlErrorResponse {
    @XmlAttribute(namespace = "http://www.w3.org/2001/XMLSchema-instance", name = "schemaLocation")
    private String schemaLocation = "http://www.dhl.com err-res.xsd";

    @XmlElement(name = "Response")
    private Response response;

    @XmlRootElement(name = "Response")
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class Response {
        @XmlElement(name = "Status")
        private Status status;
        @XmlElement(name = "LabelImage")
        private LabelImage labelImage;

        public Status getStatus() {
            return status;
        }

        public void setStatus(Status status) {
            this.status = status;
        }

        public LabelImage getLabelImage() {
            return labelImage;
        }

        public void setLabelImage(LabelImage labelImage) {
            this.labelImage = labelImage;
        }
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "LabelImage")
    public static class LabelImage {
        @XmlElement(name = "OutputFormat")
        private String outputFormat;
        @XmlElement(name = "OutputImage")
        private String outputImage;
        @XmlElement(name = "MultiLabels")
        List<MultiLabel> multiLabelList;

        public String getOutputFormat() {
            return outputFormat;
        }

        public void setOutputFormat(String outputFormat) {
            this.outputFormat = outputFormat;
        }

        public String getOutputImage() {
            return outputImage;
        }

        public void setOutputImage(String outputImage) {
            this.outputImage = outputImage;
        }

        public List<MultiLabel> getMultiLabelList() {
            return multiLabelList;
        }

        public void setMultiLabelList(List<MultiLabel> multiLabelList) {
            this.multiLabelList = multiLabelList;
        }
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "MultiLabel")
    public static class MultiLabel {
        @XmlElement(name = "DocName")
        private String docName;
        @XmlElement(name = "DocFormat")
        private String docFormat;
        @XmlElement(name = "DocImageVal")
        private String docImageVal;

        public String getDocName() {
            return docName;
        }

        public void setDocName(String docName) {
            this.docName = docName;
        }

        public String getDocFormat() {
            return docFormat;
        }

        public void setDocFormat(String docFormat) {
            this.docFormat = docFormat;
        }

        public String getDocImageVal() {
            return docImageVal;
        }

        public void setDocImageVal(String docImageVal) {
            this.docImageVal = docImageVal;
        }
    }


    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "Status")
    public static class Status {
        @XmlElement(name = "ActionStatus")
        private Integer actionStatus;
        @XmlElement(name = "Condition")
        private List<Condition> condition;

        public Integer getActionStatus() {
            return actionStatus;
        }

        public void setActionStatus(Integer actionStatus) {
            this.actionStatus = actionStatus;
        }

        public List<Condition> getCondition() {
            return condition;
        }

        public void setCondition(List<Condition> condition) {
            this.condition = condition;
        }
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "Condition")
    public static class Condition {
        @XmlElement(name = "ConditionCode")
        private Integer conditionCode;
        @XmlElement(name = "ConditionData")
        private String conditionData;

        public Integer getConditionCode() {
            return conditionCode;
        }

        public void setConditionCode(Integer conditionCode) {
            this.conditionCode = conditionCode;
        }

        public String getConditionData() {
            return conditionData;
        }

        public void setConditionData(String conditionData) {
            this.conditionData = conditionData;
        }
    }

    public Response getResponse() {
        return response;
    }

    public void setResponse(Response response) {
        this.response = response;
    }

    public String getSchemaLocation() {
        return schemaLocation;
    }

    public void setSchemaLocation(String schemaLocation) {
        this.schemaLocation = schemaLocation;
    }
}
