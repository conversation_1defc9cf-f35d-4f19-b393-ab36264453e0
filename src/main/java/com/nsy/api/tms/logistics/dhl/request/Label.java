package com.nsy.api.tms.logistics.dhl.request;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.NONE)
@XmlRootElement(name = "req:OrderRequest")
public class Label {
    @XmlElement(name = "HideAccount")
    private String hideAccount;
    @XmlElement(name = "LabelTemplate")
    private String labelTemplate;
    @XmlElement(name = "CustomsInvoiceTemplate")
    private String customsInvoiceTemplate;
    @XmlElement(name = "Resolution")
    private String resolution;

    public String getHideAccount() {
        return hideAccount;
    }

    public void setHideAccount(String hideAccount) {
        this.hideAccount = hideAccount;
    }

    public String getLabelTemplate() {
        return labelTemplate;
    }

    public void setLabelTemplate(String labelTemplate) {
        this.labelTemplate = labelTemplate;
    }

    public String getCustomsInvoiceTemplate() {
        return customsInvoiceTemplate;
    }

    public void setCustomsInvoiceTemplate(String customsInvoiceTemplate) {
        this.customsInvoiceTemplate = customsInvoiceTemplate;
    }

    public String getResolution() {
        return resolution;
    }

    public void setResolution(String resolution) {
        this.resolution = resolution;
    }
}
