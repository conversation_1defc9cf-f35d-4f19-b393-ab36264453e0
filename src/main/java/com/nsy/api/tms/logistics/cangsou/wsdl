<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://www.example.org/Ec/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="Ec" targetNamespace="http://www.example.org/Ec/">
  <wsdl:types>
    <xsd:schema targetNamespace="http://www.example.org/Ec/">
      <xsd:element name="callService">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="paramsJson" type="xsd:string" maxOccurs="1" minOccurs="0">
            	<xsd:annotation>
            		<xsd:documentation>json格式化后的数据</xsd:documentation>
            	</xsd:annotation></xsd:element>
            <xsd:element name="appToken" type="xsd:string" maxOccurs="1" minOccurs="1">
            	<xsd:annotation>
            		<xsd:documentation>用户Token</xsd:documentation>
            	</xsd:annotation></xsd:element>
            <xsd:element name="appKey" type="xsd:string" maxOccurs="1" minOccurs="1">
            	<xsd:annotation>
            		<xsd:documentation>用户Key</xsd:documentation>
            	</xsd:annotation></xsd:element>
            <xsd:element name="service" type="xsd:string" maxOccurs="1" minOccurs="1">
            	<xsd:annotation>
            		<xsd:documentation>请求的方法</xsd:documentation>
            	</xsd:annotation></xsd:element>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="callServiceResponse">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="response" type="xsd:string"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="callServiceRequest">
    <wsdl:part element="tns:callService" name="parameters"/>
  </wsdl:message>
  <wsdl:message name="callServiceResponse">
    <wsdl:part element="tns:callServiceResponse" name="parameters"/>
  </wsdl:message>
  <wsdl:portType name="Ec">
    <wsdl:operation name="callService">
      <wsdl:input message="tns:callServiceRequest"/>
      <wsdl:output message="tns:callServiceResponse"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="EcSOAP" type="tns:Ec">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="callService">
      <soap:operation soapAction="http://www.example.org/Ec/callService"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="Ec">
    <wsdl:port binding="tns:EcSOAP" name="EcSOAP">
      <soap:address location="http://************:91/default/svc/web-service"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
