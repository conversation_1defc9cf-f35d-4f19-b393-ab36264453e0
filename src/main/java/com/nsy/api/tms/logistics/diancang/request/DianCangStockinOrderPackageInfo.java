package com.nsy.api.tms.logistics.diancang.request;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2024/8/7 18:29
 */
@XmlRootElement(name = "Package")
@XmlAccessorType(XmlAccessType.FIELD)
public class DianCangStockinOrderPackageInfo {

    /**
     * 装箱清单编号
     */
    @XmlElement(name = "CustomerPackageNumber")
    private String customerPackageNumber;

    /**
     * 箱子重量
     */
    @XmlElement(name = "GrossWeight")
    private String grossWeight;

    /**
     * 箱子尺寸(长,宽,高): 10,20,30
     */
    @XmlElement(name = "Size")
    private String size;

    /**
     * 单位制类型：公制：Metric, 英制：Imperial
     */
    @XmlElement(name = "UnitType")
    private String unitType;

    /**
     * 备注信息
     */
    @XmlElement(name = "Remark")
    private String remark;

    public String getCustomerPackageNumber() {
        return customerPackageNumber;
    }

    public void setCustomerPackageNumber(String customerPackageNumber) {
        this.customerPackageNumber = customerPackageNumber;
    }

    public String getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(String grossWeight) {
        this.grossWeight = grossWeight;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getUnitType() {
        return unitType;
    }

    public void setUnitType(String unitType) {
        this.unitType = unitType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
