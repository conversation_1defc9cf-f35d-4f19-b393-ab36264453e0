package com.nsy.api.tms.logistics.zhiyun.resp;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * HXD
 * 2022/12/7
 **/
@XmlAccessorType(XmlAccessType.NONE)
@XmlRootElement(name = "labeUrlList")
public class LabelUrl {
    @XmlElement(name = "labelUrl")
    private String labelUrl;
    @XmlElement(name = "type")
    private String type;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getLabelUrl() {
        return labelUrl;
    }

    public void setLabelUrl(String labelUrl) {
        this.labelUrl = labelUrl;
    }
}
