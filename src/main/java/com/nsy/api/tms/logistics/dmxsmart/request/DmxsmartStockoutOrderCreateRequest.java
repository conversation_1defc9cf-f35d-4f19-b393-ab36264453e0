package com.nsy.api.tms.logistics.dmxsmart.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.nsy.api.tms.request.BaseLogisticsOrderRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/31 17:30
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DmxsmartStockoutOrderCreateRequest extends BaseLogisticsOrderRequest {

    /**
     * 客户订单号
     */
    private String referenceId;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 物流渠道编码
     */
    private String serviceCode;

    /**
     * 跟踪号码，物流单号，承运商单号
     * 如果已经存在物流单号，可以通过该字段传入
     */
    private String trackingNo;

    /**
     * 是否合箱
     */
    private Boolean combinePackage;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 收件人信息
     */
    private DmxsmartReviceInfo consignee;

    /**
     * 出库明细
     */
    private List<DmxsmartStockOutProductDetail> orderItems;

    /**
     * 标签信息
     */
    private List<DmxsmartStockOutOrderLabelsInfo> orderLabels;

    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getServiceCode() {
        return serviceCode;
    }

    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode;
    }

    public String getTrackingNo() {
        return trackingNo;
    }

    public void setTrackingNo(String trackingNo) {
        this.trackingNo = trackingNo;
    }

    public Boolean getCombinePackage() {
        return combinePackage;
    }

    public void setCombinePackage(Boolean combinePackage) {
        this.combinePackage = combinePackage;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public DmxsmartReviceInfo getConsignee() {
        return consignee;
    }

    public void setConsignee(DmxsmartReviceInfo consignee) {
        this.consignee = consignee;
    }

    public List<DmxsmartStockOutProductDetail> getOrderItems() {
        return orderItems;
    }

    public void setOrderItems(List<DmxsmartStockOutProductDetail> orderItems) {
        this.orderItems = orderItems;
    }

    public List<DmxsmartStockOutOrderLabelsInfo> getOrderLabels() {
        return orderLabels;
    }

    public void setOrderLabels(List<DmxsmartStockOutOrderLabelsInfo> orderLabels) {
        this.orderLabels = orderLabels;
    }
}
