package com.nsy.api.tms.logistics.diancang.request;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2024/5/16 15:42
 */
@XmlRootElement(name = "GetExpressOrder")
@XmlAccessorType(XmlAccessType.FIELD)
public class DianCangLogisticsQueryRequest {

    /**
     * 合作方订单编号
     */
    @XmlElement(name = "PartnerOrderNumber")
    private String partnerOrderNumber;

    /**
     * 系统订单号
     */
    @XmlElement(name = "OrderNumber")
    private String orderNumber;

    /**
     * 客户单号
     */
    @XmlElement(name = "CustomerOrderNumber")
    private String customerOrderNumber;

    public String getPartnerOrderNumber() {
        return partnerOrderNumber;
    }

    public void setPartnerOrderNumber(String partnerOrderNumber) {
        this.partnerOrderNumber = partnerOrderNumber;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getCustomerOrderNumber() {
        return customerOrderNumber;
    }

    public void setCustomerOrderNumber(String customerOrderNumber) {
        this.customerOrderNumber = customerOrderNumber;
    }
}
