package com.nsy.api.tms.logistics.aliexpress.response.ship;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @Author: <PERSON>
 * @Date: 2019/2/27 20:17
 */
public class AliRefundSellerAddressList {
    @JsonProperty(value = "AddressId")
    private Long addressId;
    @JsonProperty(value = "City")
    private String city;
    @JsonProperty(value = "Country")
    private String country;
    @JsonProperty(value = "County")
    private String county;
    @JsonProperty(value = "Email")
    private String email;
    @JsonProperty(value = "Fax")
    private String fax;
    @JsonProperty(value = "IsDefault")
    private Long isDefault;
    @JsonProperty(value = "Language")
    private String language;
    @JsonProperty(value = "MemberType")
    private String memberType;
    @JsonProperty(value = "Mobile")
    private String mobile;
    @JsonProperty(value = "Name")
    private String name;
    @JsonProperty(value = "NeedToUpdate")
    private Boolean needToUpdate;
    @JsonProperty(value = "Phone")
    private String phone;
    @JsonProperty(value = "Postcode")
    private String postcode;
    @JsonProperty(value = "Province")
    private String province;
    @JsonProperty(value = "Street")
    private String street;
    @JsonProperty(value = "StreetAddress")
    private String streetAddress;
    @JsonProperty(value = "TrademanageId")
    private String trademanageId;

    public Long getAddressId() {
        return addressId;
    }

    public void setAddressId(Long addressId) {
        this.addressId = addressId;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public Long getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Long isDefault) {
        this.isDefault = isDefault;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getMemberType() {
        return memberType;
    }

    public void setMemberType(String memberType) {
        this.memberType = memberType;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getNeedToUpdate() {
        return needToUpdate;
    }

    public void setNeedToUpdate(Boolean needToUpdate) {
        this.needToUpdate = needToUpdate;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getStreetAddress() {
        return streetAddress;
    }

    public void setStreetAddress(String streetAddress) {
        this.streetAddress = streetAddress;
    }

    public String getTrademanageId() {
        return trademanageId;
    }

    public void setTrademanageId(String trademanageId) {
        this.trademanageId = trademanageId;
    }
}
