package com.nsy.api.tms.logistics.zuohai;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * HXD
 * 2024/1/11
 **/
public class ReplenishmentOrder {
    @JsonProperty("waybill")
    private String waybill;

    @JsonProperty("jhfs")
    private String jhfs;

    @JsonProperty("tcck")
    private String tcck;

    @JsonProperty("mdck")
    private String mdck;

    @JsonProperty("chqd")
    private String chqd;

    @JsonProperty("beizhu")
    private String beizhu;

    @JsonProperty("country")
    private String country;

    @JsonProperty("order_types")
    private String orderTypes;

    @JsonProperty("order_products")
    private List<ReplenishmentProduct> orderProducts;

    public String getWaybill() {
        return waybill;
    }

    public void setWaybill(String waybill) {
        this.waybill = waybill;
    }

    public String getJhfs() {
        return jhfs;
    }

    public void setJhfs(String jhfs) {
        this.jhfs = jhfs;
    }

    public String getTcck() {
        return tcck;
    }

    public void setTcck(String tcck) {
        this.tcck = tcck;
    }

    public String getMdck() {
        return mdck;
    }

    public void setMdck(String mdck) {
        this.mdck = mdck;
    }

    public String getChqd() {
        return chqd;
    }

    public void setChqd(String chqd) {
        this.chqd = chqd;
    }

    public String getBeizhu() {
        return beizhu;
    }

    public void setBeizhu(String beizhu) {
        this.beizhu = beizhu;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getOrderTypes() {
        return orderTypes;
    }

    public void setOrderTypes(String orderTypes) {
        this.orderTypes = orderTypes;
    }

    public List<ReplenishmentProduct> getOrderProducts() {
        return orderProducts;
    }

    public void setOrderProducts(List<ReplenishmentProduct> orderProducts) {
        this.orderProducts = orderProducts;
    }
}
