package com.nsy.api.tms.logistics.aliexpress.response.ship;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @Author: <PERSON>
 * @Date: 2019/2/28 14:56
 */
public class AliExpressCreateOrderResponse {
    @JsonProperty(value = "Result")
    private CreateWarehouseOrderResult result;
    @JsonProperty(value = "ResultSuccess")
    private Boolean resultSuccess;

    public CreateWarehouseOrderResult getResult() {
        return result;
    }

    public void setResult(CreateWarehouseOrderResult result) {
        this.result = result;
    }

    public Boolean getResultSuccess() {
        return resultSuccess;
    }

    public void setResultSuccess(Boolean resultSuccess) {
        this.resultSuccess = resultSuccess;
    }
}
