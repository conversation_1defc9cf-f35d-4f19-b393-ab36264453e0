
package com.nsy.api.tms.logistics.jiacheng;

import java.util.List;


public class Data {
    private String apiplatform;
    private String jcexkey;
    private String customerid;
    private String customer;
    private String linkcustomer;
    private List<Packages> packages;

    public void setApiplatform(String apiplatform) {
        this.apiplatform = apiplatform;
    }

    public String getApiplatform() {
        return apiplatform;
    }

    public void setJcexkey(String jcexkey) {
        this.jcexkey = jcexkey;
    }

    public String getJcexkey() {
        return jcexkey;
    }

    public void setCustomerid(String customerid) {
        this.customerid = customerid;
    }

    public String getCustomerid() {
        return customerid;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public String getCustomer() {
        return customer;
    }

    public void setLinkcustomer(String linkcustomer) {
        this.linkcustomer = linkcustomer;
    }

    public String getLinkcustomer() {
        return linkcustomer;
    }

    public void setPackages(List<Packages> packages) {
        this.packages = packages;
    }

    public List<Packages> getPackages() {
        return packages;
    }

}