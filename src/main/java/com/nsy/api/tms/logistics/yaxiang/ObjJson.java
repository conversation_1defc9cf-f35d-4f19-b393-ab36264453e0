package com.nsy.api.tms.logistics.yaxiang;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public class ObjJson {

    @JsonProperty(value = "errorMsg")
    private String errorMsg;

    @JsonProperty(value = "labelUrl")
    private String labelUrl;

    @JsonProperty(value = "orderNo")
    private String orderNo;


    @JsonProperty(value = "serviceNo")
    private String serviceNo;


    @JsonProperty(value = "tracking_code")
    private String trackingCode;

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getLabelUrl() {
        return labelUrl;
    }

    public void setLabelUrl(String labelUrl) {
        this.labelUrl = labelUrl;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getServiceNo() {
        return serviceNo;
    }

    public void setServiceNo(String serviceNo) {
        this.serviceNo = serviceNo;
    }

    public String getTrackingCode() {
        return trackingCode;
    }

    public void setTrackingCode(String trackingCode) {
        this.trackingCode = trackingCode;
    }
}
