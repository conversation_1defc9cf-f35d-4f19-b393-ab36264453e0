package com.nsy.api.tms.logistics.ups.ship.request;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/19 19:15
 */
public class Packaging {
    @JsonProperty(value = "Code")
    private String code;
    @JsonProperty(value = "Description")
    private String description;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
