package com.nsy.api.tms.logistics.zhiyun.resp;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * HXD
 * 2022/12/7
 **/
@XmlAccessorType(XmlAccessType.NONE)
@XmlRootElement(name = "soap:Envelope")
public class SoapEnvelope {

    @XmlAttribute(name = "soap:Body")
    private ZySoapBody soapBody;


    public ZySoapBody getSoapBody() {
        return soapBody;
    }

    public void setSoapBody(ZySoapBody soapBody) {
        this.soapBody = soapBody;
    }
}
