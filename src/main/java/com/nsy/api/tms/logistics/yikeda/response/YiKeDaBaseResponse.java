package com.nsy.api.tms.logistics.yikeda.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public class YiKeDaBaseResponse {

    @JsonProperty(value = "Version")
    private String version;

    @JsonProperty(value = "RequestId")
    private String requestId; // 返回的请求序号

    @JsonProperty(value = "ResponseId")
    private String responseId;

    @JsonProperty(value = "ResponseTime")
    private String responseTime; // 当前的UTC时间

    @JsonProperty(value = "ResponseResult")
    private Integer responseResult; // 当成功的时候该值为1,失败为0

    @JsonProperty(value = "ResponseError")
    private ResponseError responseError; // 请求成功的时候，该值为NULL；请求失败的结构体解析如下

    public static class ResponseError {
        @JsonProperty(value = "Code")
        private String code; // 错误代码

        @JsonProperty(value = "ShortMessage")
        private String shortMessage; // 错误提示内容

        @JsonProperty(value = "LongMessage")
        private String longMessage; // 错误原因解释

        @JsonProperty(value = "ErrorValue")
        private String errorValue; // 错误值

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getShortMessage() {
            return shortMessage;
        }

        public void setShortMessage(String shortMessage) {
            this.shortMessage = shortMessage;
        }

        public String getLongMessage() {
            return longMessage;
        }

        public void setLongMessage(String longMessage) {
            this.longMessage = longMessage;
        }

        public String getErrorValue() {
            return errorValue;
        }

        public void setErrorValue(String errorValue) {
            this.errorValue = errorValue;
        }
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }

    public String getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(String responseTime) {
        this.responseTime = responseTime;
    }

    public Integer getResponseResult() {
        return responseResult;
    }

    public void setResponseResult(Integer responseResult) {
        this.responseResult = responseResult;
    }

    public ResponseError getResponseError() {
        return responseError;
    }

    public void setResponseError(ResponseError responseError) {
        this.responseError = responseError;
    }
}
