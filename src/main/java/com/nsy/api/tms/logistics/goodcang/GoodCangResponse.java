package com.nsy.api.tms.logistics.goodcang;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * HXD
 * 2022/7/11
 **/

public class GoodCangResponse {
    private String ask;
    private String message;
    private Data data;
    @JsonProperty(value = "Error")
    private ErrorMsg error;

    public ErrorMsg getError() {
        return error;
    }

    public void setError(ErrorMsg error) {
        this.error = error;
    }

    public String getAsk() {
        return ask;
    }

    public void setAsk(String ask) {
        this.ask = ask;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    public static class Data {

        @JsonProperty(value = "order_code")
        private String orderCode;

        @JsonProperty(value = "tracking_no")
        private String trackingNo;

        @JsonProperty(value = "reference_no")
        private String referenceNo;

        @JsonProperty(value = "order_status")
        private String orderStatus;

        @JsonProperty(value = "order_weight")
        private String orderWeight;

        public String getOrderWeight() {
            return orderWeight;
        }

        public void setOrderWeight(String orderWeight) {
            this.orderWeight = orderWeight;
        }

        public String getOrderStatus() {
            return orderStatus;
        }

        public void setOrderStatus(String orderStatus) {
            this.orderStatus = orderStatus;
        }

        public String getTrackingNo() {
            return trackingNo;
        }

        public void setTrackingNo(String trackingNo) {
            this.trackingNo = trackingNo;
        }

        public String getReferenceNo() {
            return referenceNo;
        }

        public void setReferenceNo(String referenceNo) {
            this.referenceNo = referenceNo;
        }

        public String getOrderCode() {
            return orderCode;
        }

        public void setOrderCode(String orderCode) {
            this.orderCode = orderCode;
        }
    }

    public static class ErrorMsg {
        private String errCode;
        private String errMessage;

        public String getErrCode() {
            return errCode;
        }

        public void setErrCode(String errCode) {
            this.errCode = errCode;
        }

        public String getErrMessage() {
            return errMessage;
        }

        public void setErrMessage(String errMessage) {
            this.errMessage = errMessage;
        }
    }
}
