package com.nsy.api.tms.logistics.hlt;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "auditOrderResponse", propOrder = {
        "error",
        "success",
        "trackingNo"
})
public class AuditOrderResponse {

    protected HopHopError error;
    protected Boolean success;
    protected String trackingNo;

    /**
     * ��ȡerror���Ե�ֵ��
     *
     * @return possible object is
     * {@link HopHopError }
     */
    public HopHopError getError() {
        return error;
    }

    /**
     * ����error���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link HopHopError }
     */
    public void setError(HopHopError value) {
        this.error = value;
    }

    /**
     * ��ȡsuccess���Ե�ֵ��
     *
     * @return possible object is
     * {@link Boolean }
     */
    public Boolean isSuccess() {
        return success;
    }

    /**
     * ����success���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link Boolean }
     */
    public void setSuccess(Boolean value) {
        this.success = value;
    }

    /**
     * ��ȡtrackingNo���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getTrackingNo() {
        return trackingNo;
    }

    /**
     * ����trackingNo���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setTrackingNo(String value) {
        this.trackingNo = value;
    }

}
