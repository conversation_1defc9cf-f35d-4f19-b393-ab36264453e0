package com.nsy.api.tms.logistics.dhl.request;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.NONE)
@XmlRootElement(name = "SpecialService")
public class SpecialService {
    @XmlElement(name = "SpecialServiceType")
    private String specialServiceType;

    public String getSpecialServiceType() {
        return specialServiceType;
    }

    public void setSpecialServiceType(String specialServiceType) {
        this.specialServiceType = specialServiceType;
    }
}
