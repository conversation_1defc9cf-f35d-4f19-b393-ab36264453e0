package com.nsy.api.tms.logistics.tnt.request.track;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @Author: Woods Lee
 * @Date: 2019/1/22 19:06
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "TrackRequest")
public class TNTTrackRequest {
    @XmlElement(name = "SearchCriteria")
    private SearchCriteria searchCriteria;
    @XmlElement(name = "LevelOfDetail")
    private LevelOfDetail levelOfDetail;
    @XmlAttribute(name = "locale")
    private String locale;
    @XmlAttribute(name = "version")
    private String version;

    public SearchCriteria getSearchCriteria() {
        return searchCriteria;
    }

    public void setSearchCriteria(SearchCriteria searchCriteria) {
        this.searchCriteria = searchCriteria;
    }

    public LevelOfDetail getLevelOfDetail() {
        return levelOfDetail;
    }

    public void setLevelOfDetail(LevelOfDetail levelOfDetail) {
        this.levelOfDetail = levelOfDetail;
    }

    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
