package com.nsy.api.tms.logistics.dmxsmart.request;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * <AUTHOR>
 * @date 2024/7/31 17:38
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DmxsmartStockOutProductDetail {

    /**
     * sku
     */
    private String sku;

    /**
     * 产品数量
     */
    private Integer qty;

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }
}
