package com.nsy.api.tms.logistics.zuohai;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * HXD
 * 2024/1/11
 **/

public class ReplenishmentRequest {
    @JsonProperty("token")
    private String token;

    @JsonProperty("order_list")
    private List<ReplenishmentOrder> orderList;

    // Getters and Setters

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public List<ReplenishmentOrder> getOrderList() {
        return orderList;
    }

    public void setOrderList(List<ReplenishmentOrder> orderList) {
        this.orderList = orderList;
    }
}
