package com.nsy.api.tms.logistics.ups.ship.response;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/19 21:26
 */
public class ServiceOptionsCharges {
    @JsonProperty(value = "CurrencyCode")
    private String currencyCode;
    @JsonProperty(value = "MonetaryValue")
    private String monetaryValue;

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getMonetaryValue() {
        return monetaryValue;
    }

    public void setMonetaryValue(String monetaryValue) {
        this.monetaryValue = monetaryValue;
    }
}
