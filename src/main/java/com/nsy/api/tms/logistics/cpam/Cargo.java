package com.nsy.api.tms.logistics.cpam;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * <AUTHOR>
 * @date 2020-02-02 11:13
 */
public class Cargo {

    @JSONField(name = "cargo_no")
    private String cargoNo;
    @JSONField(name = "cargo_name")
    private String cargoName;
    @JSONField(name = "cargo_name_en")
    private String cargoNameEn;
    @JSONField(name = "cargo_type_name")
    private String cargoTypeName;
    @JSONField(name = "cargo_quantity")
    private Integer cargoQuantity;
    @JSONField(name = "cargo_value")
    private Double cargoValue;
    @JSONField(name = "cost")
    private Double cost;
    @JSONField(name = "cargo_currency")
    private String cargoCurrency;
    @JSONField(name = "cargo_serial")
    private String cargoSerial;
    @JSONField(name = "carogo_weight")
    private Double carogoWeight;
    @JSONField(name = "cargo_description")
    private String cargoDescription;
    @JSONField(name = "unit")
    private String unit;

    public String getCargoSerial() {
        return cargoSerial;
    }

    public void setCargoSerial(String cargoSerial) {
        this.cargoSerial = cargoSerial;
    }

    public String getCargoNo() {
        return cargoNo;
    }

    public void setCargoNo(String cargoNo) {
        this.cargoNo = cargoNo;
    }

    public String getCargoName() {
        return cargoName;
    }

    public void setCargoName(String cargoName) {
        this.cargoName = cargoName;
    }

    public String getCargoNameEn() {
        return cargoNameEn;
    }

    public void setCargoNameEn(String cargoNameEn) {
        this.cargoNameEn = cargoNameEn;
    }

    public String getCargoTypeName() {
        return cargoTypeName;
    }

    public void setCargoTypeName(String cargoTypeName) {
        this.cargoTypeName = cargoTypeName;
    }

    public Integer getCargoQuantity() {
        return cargoQuantity;
    }

    public void setCargoQuantity(Integer cargoQuantity) {
        this.cargoQuantity = cargoQuantity;
    }

    public Double getCargoValue() {
        return cargoValue;
    }

    public void setCargoValue(Double cargoValue) {
        this.cargoValue = cargoValue;
    }

    public Double getCost() {
        return cost;
    }

    public void setCost(Double cost) {
        this.cost = cost;
    }

    public String getCargoCurrency() {
        return cargoCurrency;
    }

    public void setCargoCurrency(String cargoCurrency) {
        this.cargoCurrency = cargoCurrency;
    }

    public Double getCarogoWeight() {
        return carogoWeight;
    }

    public void setCarogoWeight(Double carogoWeight) {
        this.carogoWeight = carogoWeight;
    }

    public String getCargoDescription() {
        return cargoDescription;
    }

    public void setCargoDescription(String cargoDescription) {
        this.cargoDescription = cargoDescription;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }
}
