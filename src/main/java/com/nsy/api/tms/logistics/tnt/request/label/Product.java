package com.nsy.api.tms.logistics.tnt.request.label;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/22 17:00
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "product")
public class Product {
    @XmlElement(name = "lineOfBusiness")
    private Integer lineOfBusiness;
    @XmlElement(name = "groupId")
    private Integer groupId;
    @XmlElement(name = "subGroupId")
    private Integer subGroupId;
    @XmlElement(name = "id")
    private String id;
    //Type of service for product chosen. Values (as defined for ProductTypeEnum) are "D" for a document or "N" for non-documents
    @XmlElement(name = "type")
    private String type;
    @XmlElement(name = "option")
    private String option;

    public Integer getLineOfBusiness() {
        return lineOfBusiness;
    }

    public void setLineOfBusiness(Integer lineOfBusiness) {
        this.lineOfBusiness = lineOfBusiness;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public Integer getSubGroupId() {
        return subGroupId;
    }

    public void setSubGroupId(Integer subGroupId) {
        this.subGroupId = subGroupId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getOption() {
        return option;
    }

    public void setOption(String option) {
        this.option = option;
    }
}
