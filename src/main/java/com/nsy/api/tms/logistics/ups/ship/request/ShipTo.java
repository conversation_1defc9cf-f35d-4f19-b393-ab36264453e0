package com.nsy.api.tms.logistics.ups.ship.request;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/19 19:04
 */
public class ShipTo {
    @JsonProperty(value = "Name")
    private String name;
    @JsonProperty(value = "AttentionName")
    private String attentionName;
    @JsonProperty(value = "Phone")
    private Phone phone;
    @JsonProperty(value = "Address")
    private Address address;
    @JsonProperty(value = "EMailAddress")
    private String emailAddress;
    @JsonProperty(value = "TaxIdentificationNumber")
    private String taxIdentificationNumber;

    public String getTaxIdentificationNumber() {
        return taxIdentificationNumber;
    }

    public void setTaxIdentificationNumber(String taxIdentificationNumber) {
        this.taxIdentificationNumber = taxIdentificationNumber;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAttentionName() {
        return attentionName;
    }

    public void setAttentionName(String attentionName) {
        this.attentionName = attentionName;
    }

    public Phone getPhone() {
        return phone;
    }

    public void setPhone(Phone phone) {
        this.phone = phone;
    }

    public Address getAddress() {
        return address;
    }

    public void setAddress(Address address) {
        this.address = address;
    }
}
