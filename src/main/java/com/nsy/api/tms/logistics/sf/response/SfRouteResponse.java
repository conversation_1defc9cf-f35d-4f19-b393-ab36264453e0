package com.nsy.api.tms.logistics.sf.response;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlAccessorType(XmlAccessType.NONE)
@XmlRootElement(name = "RouteResponse")
public class SfRouteResponse {

    @XmlAttribute(name = "mailno")
    private String mailNo;

    @XmlAttribute(name = "orderid")
    private String orderId;

    @XmlElement(name = "Route")
    private List<SfRoute> route;

    public String getMailNo() {
        return mailNo;
    }

    public void setMailNo(String mailNo) {
        this.mailNo = mailNo;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public List<SfRoute> getRoute() {
        return route;
    }

    public void setRoute(List<SfRoute> route) {
        this.route = route;
    }
}
