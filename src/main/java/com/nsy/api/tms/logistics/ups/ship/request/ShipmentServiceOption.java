package com.nsy.api.tms.logistics.ups.ship.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class ShipmentServiceOption {
    @JsonProperty(value = "InternationalForms")
    private InternationalForm internationalForms;
    @JsonProperty(value = "Notification")
    private List<Notification> notification;

    public List<Notification> getNotification() {
        return notification;
    }

    public void setNotification(List<Notification> notification) {
        this.notification = notification;
    }

    public InternationalForm getInternationalForms() {
        return internationalForms;
    }

    public void setInternationalForms(InternationalForm internationalForms) {
        this.internationalForms = internationalForms;
    }
}
