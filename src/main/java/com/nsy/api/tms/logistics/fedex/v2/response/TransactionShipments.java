package com.nsy.api.tms.logistics.fedex.v2.response;

import java.util.Date;
import java.util.List;


public class TransactionShipments {

    private String masterTrackingNumber;
    private String serviceType;
    private Date shipDatestamp;
    private String serviceName;
    private List<ShipmentDocuments> shipmentDocuments;
    private List<PieceResponses> pieceResponses;

    public String getMasterTrackingNumber() {
        return masterTrackingNumber;
    }


    public List<ShipmentDocuments> getShipmentDocuments() {
        return shipmentDocuments;
    }

    public void setShipmentDocuments(List<ShipmentDocuments> shipmentDocuments) {
        this.shipmentDocuments = shipmentDocuments;
    }

    public void setMasterTrackingNumber(String masterTrackingNumber) {
        this.masterTrackingNumber = masterTrackingNumber;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public Date getShipDatestamp() {
        return shipDatestamp;
    }

    public void setShipDatestamp(Date shipDatestamp) {
        this.shipDatestamp = shipDatestamp;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public List<PieceResponses> getPieceResponses() {
        return pieceResponses;
    }

    public void setPieceResponses(List<PieceResponses> pieceResponses) {
        this.pieceResponses = pieceResponses;
    }

}
