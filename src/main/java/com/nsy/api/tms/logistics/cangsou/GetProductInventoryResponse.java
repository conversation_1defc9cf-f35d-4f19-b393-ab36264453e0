package com.nsy.api.tms.logistics.cangsou;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public class GetProductInventoryResponse extends CangSouBaseResponse {

    @JsonProperty(value = "data")
    List<Data> dataList;

    public List<Data> getDataList() {
        return dataList;
    }

    public void setDataList(List<Data> dataList) {
        this.dataList = dataList;
    }

    public static class Data {
        @JsonProperty(value = "product_sku")
        private String barcode;

        @JsonProperty(value = "product_title")
        private String productTitle;

        @JsonProperty(value = "warehouse_code")
        private String warehouseCode;


        @JsonProperty(value = "onway")
        private Integer onway;

        @JsonProperty(value = "pending")
        private Integer pending;

        @JsonProperty(value = "sellable")
        private Integer sellable;

        @JsonProperty(value = "unsellable")
        private Integer unsellable;

        @JsonProperty(value = "reserved")
        private Integer reserved;

        @JsonProperty(value = "shipped")
        private Integer shipped;


        public String getBarcode() {
            return barcode;
        }

        public void setBarcode(String barcode) {
            this.barcode = barcode;
        }

        public String getProductTitle() {
            return productTitle;
        }

        public void setProductTitle(String productTitle) {
            this.productTitle = productTitle;
        }

        public String getWarehouseCode() {
            return warehouseCode;
        }

        public void setWarehouseCode(String warehouseCode) {
            this.warehouseCode = warehouseCode;
        }

        public Integer getOnway() {
            return onway;
        }

        public void setOnway(Integer onway) {
            this.onway = onway;
        }

        public Integer getPending() {
            return pending;
        }

        public void setPending(Integer pending) {
            this.pending = pending;
        }

        public Integer getSellable() {
            return sellable;
        }

        public void setSellable(Integer sellable) {
            this.sellable = sellable;
        }

        public Integer getUnsellable() {
            return unsellable;
        }

        public void setUnsellable(Integer unsellable) {
            this.unsellable = unsellable;
        }

        public Integer getReserved() {
            return reserved;
        }

        public void setReserved(Integer reserved) {
            this.reserved = reserved;
        }

        public Integer getShipped() {
            return shipped;
        }

        public void setShipped(Integer shipped) {
            this.shipped = shipped;
        }

    }
}
