package com.nsy.api.tms.logistics.diancang.request;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2024/3/13 10:04
 */
@XmlRootElement(name = "GetInstockManifestDetails")
@XmlAccessorType(XmlAccessType.FIELD)
public class DianCangStockinOrderQueryRequest {

    /**
     * 查询单号
     */
    @XmlElement(name = "InstockManifestNumber")
    private String instockManifestNumber;

    /**
     * 查询单号
     */
    @XmlElement(name = "PartnerInstockManifestID")
    private String partnerInstockManifestID;

    /**
     * 物流公司海外仓编号
     */
    @XmlElement(name = "BranchCode")
    private String branchCode;

    public String getPartnerInstockManifestID() {
        return partnerInstockManifestID;
    }

    public void setPartnerInstockManifestID(String partnerInstockManifestID) {
        this.partnerInstockManifestID = partnerInstockManifestID;
    }

    public String getInstockManifestNumber() {
        return instockManifestNumber;
    }

    public void setInstockManifestNumber(String instockManifestNumber) {
        this.instockManifestNumber = instockManifestNumber;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }
}
