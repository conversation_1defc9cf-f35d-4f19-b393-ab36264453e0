package com.nsy.api.tms.logistics.sf.request;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlAccessorType(XmlAccessType.NONE)
@XmlRootElement(name = "Order")
public class SfOrder {
    @XmlAttribute(name = "orderid")
    private String orderId;

    @XmlAttribute(name = "express_type")
    private String expressType;

    @XmlAttribute(name = "operate_flag")
    private String operateFlag;

    @XmlAttribute(name = "parcel_quantity")
    private Integer parcelQuantity;

    @XmlAttribute(name = "cargo_total_weight")
    private Double cargoTotalWeight;

    @XmlAttribute(name = "returnsign")
    private String returnSign;

    @XmlAttribute(name = "pit_code")
    private String pitCode;

    @XmlAttribute(name = "length")
    private Double length;

    @XmlAttribute(name = "width")
    private Double width;

    @XmlAttribute(name = "height")
    private Double height;

    @XmlAttribute(name = "vat_code")
    private String vatCode;

    @XmlAttribute(name = "is_battery")
    private String isBattery;

    @XmlAttribute(name = "is_insurance")
    private String isInsurance;

    @XmlAttribute(name = "j_company")
    private String senderCompany;

    @XmlAttribute(name = "j_contact")
    private String senderContact;

    @XmlAttribute(name = "j_tel")
    private String senderTel;

    @XmlAttribute(name = "j_mobile")
    private String senderMobile;

    @XmlAttribute(name = "j_address")
    private String senderAddress;

    @XmlAttribute(name = "j_province")
    private String senderProvince;

    @XmlAttribute(name = "j_city")
    private String senderCity;

    @XmlAttribute(name = "j_country")
    private String senderCountry;

    @XmlAttribute(name = "j_post_code")
    private String senderPostCode;

    @XmlAttribute(name = "d_company")
    private String receiverCompany;

    @XmlAttribute(name = "d_contact")
    private String receiverContact;

    @XmlAttribute(name = "d_tel")
    private String receiverTel;

    @XmlAttribute(name = "d_mobile")
    private String receiverMobile;

    @XmlAttribute(name = "d_email")
    private String receiverEmail;

    @XmlAttribute(name = "d_address")
    private String receiverAddress;

    @XmlAttribute(name = "d_province")
    private String receiverProvince;

    @XmlAttribute(name = "d_city")
    private String receiverCity;

    @XmlAttribute(name = "d_country")
    private String receiverCountry;

    @XmlAttribute(name = "d_post_code")
    private String receiverPostCode;

    @XmlAttribute(name = "d_identify_code")
    private String receiverIdentifyCode;

    @XmlElement(name = "Cargo")
    private List<SfCargo> cargo;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getExpressType() {
        return expressType;
    }

    public void setExpressType(String expressType) {
        this.expressType = expressType;
    }

    public String getOperateFlag() {
        return operateFlag;
    }

    public void setOperateFlag(String operateFlag) {
        this.operateFlag = operateFlag;
    }

    public Integer getParcelQuantity() {
        return parcelQuantity;
    }

    public void setParcelQuantity(Integer parcelQuantity) {
        this.parcelQuantity = parcelQuantity;
    }

    public Double getCargoTotalWeight() {
        return cargoTotalWeight;
    }

    public void setCargoTotalWeight(Double cargoTotalWeight) {
        this.cargoTotalWeight = cargoTotalWeight;
    }

    public String getReturnSign() {
        return returnSign;
    }

    public void setReturnSign(String returnSign) {
        this.returnSign = returnSign;
    }

    public String getPitCode() {
        return pitCode;
    }

    public void setPitCode(String pitCode) {
        this.pitCode = pitCode;
    }

    public Double getLength() {
        return length;
    }

    public void setLength(Double length) {
        this.length = length;
    }

    public Double getWidth() {
        return width;
    }

    public void setWidth(Double width) {
        this.width = width;
    }

    public Double getHeight() {
        return height;
    }

    public void setHeight(Double height) {
        this.height = height;
    }

    public String getVatCode() {
        return vatCode;
    }

    public void setVatCode(String vatCode) {
        this.vatCode = vatCode;
    }

    public String getIsBattery() {
        return isBattery;
    }

    public void setIsBattery(String isBattery) {
        this.isBattery = isBattery;
    }

    public String getIsInsurance() {
        return isInsurance;
    }

    public void setIsInsurance(String isInsurance) {
        this.isInsurance = isInsurance;
    }

    public String getSenderCompany() {
        return senderCompany;
    }

    public void setSenderCompany(String senderCompany) {
        this.senderCompany = senderCompany;
    }

    public String getSenderContact() {
        return senderContact;
    }

    public void setSenderContact(String senderContact) {
        this.senderContact = senderContact;
    }

    public String getSenderTel() {
        return senderTel;
    }

    public void setSenderTel(String senderTel) {
        this.senderTel = senderTel;
    }

    public String getSenderMobile() {
        return senderMobile;
    }

    public void setSenderMobile(String senderMobile) {
        this.senderMobile = senderMobile;
    }

    public String getSenderAddress() {
        return senderAddress;
    }

    public void setSenderAddress(String senderAddress) {
        this.senderAddress = senderAddress;
    }

    public String getSenderProvince() {
        return senderProvince;
    }

    public void setSenderProvince(String senderProvince) {
        this.senderProvince = senderProvince;
    }

    public String getSenderCity() {
        return senderCity;
    }

    public void setSenderCity(String senderCity) {
        this.senderCity = senderCity;
    }

    public String getSenderCountry() {
        return senderCountry;
    }

    public void setSenderCountry(String senderCountry) {
        this.senderCountry = senderCountry;
    }

    public String getSenderPostCode() {
        return senderPostCode;
    }

    public void setSenderPostCode(String senderPostCode) {
        this.senderPostCode = senderPostCode;
    }

    public String getReceiverCompany() {
        return receiverCompany;
    }

    public void setReceiverCompany(String receiverCompany) {
        this.receiverCompany = receiverCompany;
    }

    public String getReceiverContact() {
        return receiverContact;
    }

    public void setReceiverContact(String receiverContact) {
        this.receiverContact = receiverContact;
    }

    public String getReceiverTel() {
        return receiverTel;
    }

    public void setReceiverTel(String receiverTel) {
        this.receiverTel = receiverTel;
    }

    public String getReceiverMobile() {
        return receiverMobile;
    }

    public void setReceiverMobile(String receiverMobile) {
        this.receiverMobile = receiverMobile;
    }

    public String getReceiverEmail() {
        return receiverEmail;
    }

    public void setReceiverEmail(String receiverEmail) {
        this.receiverEmail = receiverEmail;
    }

    public String getReceiverAddress() {
        return receiverAddress;
    }

    public void setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress;
    }

    public String getReceiverProvince() {
        return receiverProvince;
    }

    public void setReceiverProvince(String receiverProvince) {
        this.receiverProvince = receiverProvince;
    }

    public String getReceiverCity() {
        return receiverCity;
    }

    public void setReceiverCity(String receiverCity) {
        this.receiverCity = receiverCity;
    }

    public String getReceiverCountry() {
        return receiverCountry;
    }

    public void setReceiverCountry(String receiverCountry) {
        this.receiverCountry = receiverCountry;
    }

    public String getReceiverPostCode() {
        return receiverPostCode;
    }

    public void setReceiverPostCode(String receiverPostCode) {
        this.receiverPostCode = receiverPostCode;
    }

    public String getReceiverIdentifyCode() {
        return receiverIdentifyCode;
    }

    public void setReceiverIdentifyCode(String receiverIdentifyCode) {
        this.receiverIdentifyCode = receiverIdentifyCode;
    }

    public List<SfCargo> getCargo() {
        return cargo;
    }

    public void setCargo(List<SfCargo> cargo) {
        this.cargo = cargo;
    }
}
