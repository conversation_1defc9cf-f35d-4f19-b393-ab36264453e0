
package com.nsy.api.tms.logistics.yanwen.request;

import java.math.BigDecimal;
import java.util.List;


public class ParcelInfo {

    private Integer hasBattery;
    private String currency;
    private BigDecimal totalPrice;
    private Integer totalQuantity;
    private Integer totalWeight;
    private Integer height;
    private Integer width;
    private Integer length;
    private String ioss;
    private List<ProductList> productList;

    public Integer getHasBattery() {
        return hasBattery;
    }

    public void setHasBattery(Integer hasBattery) {
        this.hasBattery = hasBattery;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    public Integer getTotalWeight() {
        return totalWeight;
    }

    public void setTotalWeight(Integer totalWeight) {
        this.totalWeight = totalWeight;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getLength() {
        return length;
    }

    public void setLength(Integer length) {
        this.length = length;
    }

    public String getIoss() {
        return ioss;
    }

    public void setIoss(String ioss) {
        this.ioss = ioss;
    }

    public List<ProductList> getProductList() {
        return productList;
    }

    public void setProductList(List<ProductList> productList) {
        this.productList = productList;
    }
}
