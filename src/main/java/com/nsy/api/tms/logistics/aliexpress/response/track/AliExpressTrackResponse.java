package com.nsy.api.tms.logistics.aliexpress.response.track;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * @Author: <PERSON> Lee
 * @Date: 2019/3/18 17:49
 */
public class AliExpressTrackResponse {
    @JsonProperty(value = "Details")
    private List<TrackDetail> trackDetails;
    @JsonProperty(value = "ErrorDesc")
    private String errorDesc;
    @JsonProperty(value = "OfficialWebsite")
    private String officialWebsite;
    @JsonProperty(value = "ResultSuccess")
    private String resultSuccess;
    @JsonProperty(value = "ErrCode")
    private String errCode;
    @JsonProperty(value = "ErrMsg")
    private String errMsg;

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    @JsonProperty(value = "SubErrCode")
    private String subErrCode;
    @JsonProperty(value = "SubErrMsg")
    private String subErrMsg;

    public List<TrackDetail> getTrackDetails() {
        return trackDetails;
    }

    public void setTrackDetails(List<TrackDetail> trackDetails) {
        this.trackDetails = trackDetails;
    }

    public String getErrorDesc() {
        return errorDesc;
    }

    public void setErrorDesc(String errorDesc) {
        this.errorDesc = errorDesc;
    }

    public String getSubErrCode() {
        return subErrCode;
    }

    public void setSubErrCode(String subErrCode) {
        this.subErrCode = subErrCode;
    }

    public String getSubErrMsg() {
        return subErrMsg;
    }

    public void setSubErrMsg(String subErrMsg) {
        this.subErrMsg = subErrMsg;
    }

    public String getOfficialWebsite() {
        return officialWebsite;
    }

    public void setOfficialWebsite(String officialWebsite) {
        this.officialWebsite = officialWebsite;
    }

    public String getResultSuccess() {
        return resultSuccess;
    }

    public void setResultSuccess(String resultSuccess) {
        this.resultSuccess = resultSuccess;
    }

    public String getErrCode() {
        return errCode;
    }

    public void setErrCode(String errCode) {
        this.errCode = errCode;
    }
}
