package com.nsy.api.tms.logistics.jiufang.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Date;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-03-27 15:19
 */
public class JiuFangYunCangGetStockOutOrderResponse {

    /**
     * 订单号
     */
    @JsonProperty("order_code")
    private String orderCode;

    /**
     * 客户参考号
     */
    @JsonProperty("reference_no")
    private String referenceNo;

    /**
     * 平台
     */
    @JsonProperty("platform")
    private String platform;

    /**
     * 订单状态：C:待发货审核 W:待发货 D:已发货 H:暂存 N:异常订单 P:问题件 X:废弃
     */
    @JsonProperty("order_status")
    private String orderStatus;

    /**
     * 运输方式
     */
    @JsonProperty("shipping_method")
    private String shippingMethod;

    /**
     * 跟踪号
     */
    @JsonProperty("tracking_no")
    private String trackingNo;

    /**
     * 仓库代码
     */
    @JsonProperty("warehouse_code")
    private String warehouseCode;

    /**
     * 订单重量
     */
    @JsonProperty("order_weight")
    private String orderWeight;

    /**
     * 创建时间
     */
    @JsonProperty("date_create")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dateCreate;

    /**
     * 审核时间
     */
    @JsonProperty("date_release")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dateRelease;

    /**
     * 出库时间
     */
    @JsonProperty("date_shipping")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dateShipping;

    /**
     * 修改时间
     */
    @JsonProperty("date_modify")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dateModify;


    /**
     * 异常原因
     */
    @JsonProperty("abnormal_reason")
    private String abnormalReason;

    /**
     * 订单费用 根据实际需求调整类型
     */
    @JsonProperty("fee_details")
    private Object feeDetails;

    /**
     * 转单号
     */
    @JsonProperty("transfer_order_no")
    private String transferOrderNo;


    public String getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    public String getReferenceNo() {
        return referenceNo;
    }

    public void setReferenceNo(String referenceNo) {
        this.referenceNo = referenceNo;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getShippingMethod() {
        return shippingMethod;
    }

    public void setShippingMethod(String shippingMethod) {
        this.shippingMethod = shippingMethod;
    }

    public String getTrackingNo() {
        return trackingNo;
    }

    public void setTrackingNo(String trackingNo) {
        this.trackingNo = trackingNo;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getOrderWeight() {
        return orderWeight;
    }

    public void setOrderWeight(String orderWeight) {
        this.orderWeight = orderWeight;
    }

    public Date getDateCreate() {
        return dateCreate;
    }

    public void setDateCreate(Date dateCreate) {
        this.dateCreate = dateCreate;
    }

    public Date getDateRelease() {
        return dateRelease;
    }

    public void setDateRelease(Date dateRelease) {
        this.dateRelease = dateRelease;
    }

    public Date getDateShipping() {
        return dateShipping;
    }

    public void setDateShipping(Date dateShipping) {
        this.dateShipping = dateShipping;
    }

    public Date getDateModify() {
        return dateModify;
    }

    public void setDateModify(Date dateModify) {
        this.dateModify = dateModify;
    }

    public String getAbnormalReason() {
        return abnormalReason;
    }

    public void setAbnormalReason(String abnormalReason) {
        this.abnormalReason = abnormalReason;
    }

    public Object getFeeDetails() {
        return feeDetails;
    }

    public void setFeeDetails(Object feeDetails) {
        this.feeDetails = feeDetails;
    }

    public String getTransferOrderNo() {
        return transferOrderNo;
    }

    public void setTransferOrderNo(String transferOrderNo) {
        this.transferOrderNo = transferOrderNo;
    }
}
