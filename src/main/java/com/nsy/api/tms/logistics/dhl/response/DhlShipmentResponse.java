package com.nsy.api.tms.logistics.dhl.response;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(namespace = "http://www.dhl.com", name = "ShipmentResponse")
public class DhlShipmentResponse {
    @XmlAttribute(namespace = "http://www.w3.org/2001/XMLSchema-instance", name = "schemaLocation")
    private String schemaLocation = "http://www.dhl.com ship-val-res.xsd";

    @XmlElement(name = "Note")
    private Note note;
    @XmlElement(name = "AirwayBillNumber")
    private String airwayBillNumber;
    @XmlElement(name = "Barcodes")
    private BarCodes barcodes;
    @XmlElement(name = "LabelImage")
    private LabelImage labelImage;

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "Note")
    public static class Note {
        @XmlElement(name = "ActionNote")
        private String actionNote;

        public String getActionNote() {
            return actionNote;
        }

        public void setActionNote(String actionNote) {
            this.actionNote = actionNote;
        }
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "Barcodes")
    public static class BarCodes {
        @XmlElement(name = "ActionNote")
        private String awbBarCode;
        @XmlElement(name = "OriginDestnBarcode")
        private String originDestnBarcode;

        public String getAwbBarCode() {
            return awbBarCode;
        }

        public void setAwbBarCode(String awbBarCode) {
            this.awbBarCode = awbBarCode;
        }

        public String getOriginDestnBarcode() {
            return originDestnBarcode;
        }

        public void setOriginDestnBarcode(String originDestnBarcode) {
            this.originDestnBarcode = originDestnBarcode;
        }
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "LabelImage")
    public static class LabelImage {
        @XmlElement(name = "OutputFormat")
        private String outputFormat;
        @XmlElement(name = "OutputImage")
        private String outputImage;
        @XmlElementWrapper(name = "MultiLabels")
        @XmlElement(name = "MultiLabel")
        private List<MultiLabel> multiLabels;

        public String getOutputFormat() {
            return outputFormat;
        }

        public void setOutputFormat(String outputFormat) {
            this.outputFormat = outputFormat;
        }

        public String getOutputImage() {
            return outputImage;
        }

        public void setOutputImage(String outputImage) {
            this.outputImage = outputImage;
        }

        public List<MultiLabel> getMultiLabels() {
            return multiLabels;
        }

        public void setMultiLabels(List<MultiLabel> multiLabels) {
            this.multiLabels = multiLabels;
        }
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "MultiLabel")
    public static class MultiLabel {
        @XmlElement(name = "DocName")
        private String docName;
        @XmlElement(name = "DocFormat")
        private String docFormat;
        @XmlElement(name = "DocImageVal")
        private String docImageVal;

        public String getDocName() {
            return docName;
        }

        public void setDocName(String docName) {
            this.docName = docName;
        }

        public String getDocFormat() {
            return docFormat;
        }

        public void setDocFormat(String docFormat) {
            this.docFormat = docFormat;
        }

        public String getDocImageVal() {
            return docImageVal;
        }

        public void setDocImageVal(String docImageVal) {
            this.docImageVal = docImageVal;
        }
    }

    public Note getNote() {
        return note;
    }

    public void setNote(Note note) {
        this.note = note;
    }

    public String getAirwayBillNumber() {
        return airwayBillNumber;
    }

    public void setAirwayBillNumber(String airwayBillNumber) {
        this.airwayBillNumber = airwayBillNumber;
    }

    public BarCodes getBarcodes() {
        return barcodes;
    }

    public void setBarcodes(BarCodes barcodes) {
        this.barcodes = barcodes;
    }

    public LabelImage getLabelImage() {
        return labelImage;
    }

    public void setLabelImage(LabelImage labelImage) {
        this.labelImage = labelImage;
    }

    public String getSchemaLocation() {
        return schemaLocation;
    }

    public void setSchemaLocation(String schemaLocation) {
        this.schemaLocation = schemaLocation;
    }
}
