package com.nsy.api.tms.logistics.tnt.response.ship;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/21 16:20
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "CREATE")
public class Create {
    @XmlElement(name = "CONREF")
    private String conRef;
    @XmlElement(name = "CONNUMBER")
    private String conNumber;
    @XmlElement(name = "SUCCESS")
    private String success;

    public String getConNumber() {
        return conNumber;
    }

    public void setConNumber(String conNumber) {
        this.conNumber = conNumber;
    }

    public String getSuccess() {
        return success;
    }

    public void setSuccess(String success) {
        this.success = success;
    }

    public String getConRef() {
        return conRef;
    }

    public void setConRef(String conRef) {
        this.conRef = conRef;
    }
}
