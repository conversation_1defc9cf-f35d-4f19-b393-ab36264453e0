package com.nsy.api.tms.logistics.xinshunyi.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class XinShunYiOrderRequest extends XinShunYiBaseRequest {

    /**
     * 1：快件订单
     * 2：快递制单-非实时返回单号
     * 3：仓储订单
     * 4：快递制单-实时返回单号(等待时间较长)。此方法选择 4，后续如需调用其他方法，例如调用删除接口，其他方法OrderType 请选择 2。
     */
    @JsonProperty(value = "OrderType")
    private String orderType; // 订单类型

    @JsonProperty(value = "OrderDatas")
    private List<OrderData> orderDatas; // 订单数据

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public List<OrderData> getOrderDatas() {
        return orderDatas;
    }

    public void setOrderDatas(List<OrderData> orderDatas) {
        this.orderDatas = orderDatas;
    }

    public static class OrderItem {
        @JsonProperty(value = "Sku")
        private String sku; // 产品 Sku (OrderType 为仓储订单必传)

        @JsonProperty(value = "Cnname")
        private String cnname; // 产品中文名

        @JsonProperty(value = "Enname")
        private String enname; // 产品英文名

        @JsonProperty(value = "Price")
        private Double price; // 单价

        @JsonProperty(value = "Weight")
        private Double weight; // 重量

        @JsonProperty(value = "Nuintm")
        private int num; // 数量

        public String getSku() {
            return sku;
        }

        public void setSku(String sku) {
            this.sku = sku;
        }

        public String getCnname() {
            return cnname;
        }

        public void setCnname(String cnname) {
            this.cnname = cnname;
        }

        public String getEnname() {
            return enname;
        }

        public void setEnname(String enname) {
            this.enname = enname;
        }

        public Double getPrice() {
            return price;
        }

        public void setPrice(Double price) {
            this.price = price;
        }

        public Double getWeight() {
            return weight;
        }

        public void setWeight(Double weight) {
            this.weight = weight;
        }

        public int getNum() {
            return num;
        }

        public void setNum(int num) {
            this.num = num;
        }
    }

    public static class Recipient {
        @JsonProperty(value = "Name")
        public String name; // 名称

        @JsonProperty(value = "Company")
        public String company; //  公司

        @JsonProperty(value = "Addres1")
        public String addres1; // 地址1

        @JsonProperty(value = "Tel")
        public String tel; // 电话

        @JsonProperty(value = "Mobile")
        public String mobile; // 手机

        @JsonProperty(value = "Province")
        public String province; // 省州

        @JsonProperty(value = "City")
        public String city; // 城市

        @JsonProperty(value = "Post")
        public String post; // 邮编

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCompany() {
            return company;
        }

        public void setCompany(String company) {
            this.company = company;
        }

        public String getAddres1() {
            return addres1;
        }

        public void setAddres1(String addres1) {
            this.addres1 = addres1;
        }

        public String getTel() {
            return tel;
        }

        public void setTel(String tel) {
            this.tel = tel;
        }

        public String getMobile() {
            return mobile;
        }

        public void setMobile(String mobile) {
            this.mobile = mobile;
        }

        public String getProvince() {
            return province;
        }

        public void setProvince(String province) {
            this.province = province;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getPost() {
            return post;
        }

        public void setPost(String post) {
            this.post = post;
        }
    }

    public static class Sender {
        @JsonProperty(value = "Name")
        public String name; // 名称

        @JsonProperty(value = "Company")
        public String company; //  公司

        @JsonProperty(value = "Country")
        public String country; // 国家

        @JsonProperty(value = "Addres")
        public String addres; // 地址

        @JsonProperty(value = "Tel")
        public String tel; // 电话

        @JsonProperty(value = "Mobile")
        public String mobile; // 手机

        @JsonProperty(value = "Province")
        public String province; // 省州

        @JsonProperty(value = "City")
        public String city; // 城市

        @JsonProperty(value = "Post")
        public String post; // 邮编

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCompany() {
            return company;
        }

        public void setCompany(String company) {
            this.company = company;
        }

        public String getCountry() {
            return country;
        }

        public void setCountry(String country) {
            this.country = country;
        }

        public String getAddres() {
            return addres;
        }

        public void setAddres(String addres) {
            this.addres = addres;
        }

        public String getTel() {
            return tel;
        }

        public void setTel(String tel) {
            this.tel = tel;
        }

        public String getMobile() {
            return mobile;
        }

        public void setMobile(String mobile) {
            this.mobile = mobile;
        }

        public String getProvince() {
            return province;
        }

        public void setProvince(String province) {
            this.province = province;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getPost() {
            return post;
        }

        public void setPost(String post) {
            this.post = post;
        }
    }

    public static class OrderData {

        @JsonProperty(value = "CustomerNumber")
        private String customerNumber; // 客户订单号(可传入贵公司内部单号)

        @JsonProperty(value = "ChannelCode")
        private String channelCode; // 渠道代码 可调用[searchStartChannel]方法获取

        @JsonProperty(value = "CountryCode")
        private String countryCode; // 国家二字代码

        @JsonProperty(value = "TotalWeight")
        private Double totalWeight; // 订单总重量

        @JsonProperty(value = "TotalValue")
        private Double totalValue; // 订单总申报价值

        @JsonProperty(value = "Number")
        private int number; // 件数

        @JsonProperty(value = "Recipient")
        private Recipient recipient; // 收件人信息

        @JsonProperty(value = "Sender")
        private Sender sender; // 寄件人信息

        @JsonProperty(value = "OrderItems")
        private List<OrderItem> orderItems; // 订单明细产品信息

        public String getCustomerNumber() {
            return customerNumber;
        }

        public void setCustomerNumber(String customerNumber) {
            this.customerNumber = customerNumber;
        }

        public String getChannelCode() {
            return channelCode;
        }

        public void setChannelCode(String channelCode) {
            this.channelCode = channelCode;
        }

        public String getCountryCode() {
            return countryCode;
        }

        public void setCountryCode(String countryCode) {
            this.countryCode = countryCode;
        }

        public Double getTotalWeight() {
            return totalWeight;
        }

        public Recipient getRecipient() {
            return recipient;
        }

        public void setRecipient(Recipient recipient) {
            this.recipient = recipient;
        }

        public Sender getSender() {
            return sender;
        }

        public void setSender(Sender sender) {
            this.sender = sender;
        }

        public List<OrderItem> getOrderItems() {
            return orderItems;
        }

        public void setOrderItems(List<OrderItem> orderItems) {
            this.orderItems = orderItems;
        }

        public void setTotalWeight(Double totalWeight) {
            this.totalWeight = totalWeight;
        }

        public Double getTotalValue() {
            return totalValue;
        }

        public void setTotalValue(Double totalValue) {
            this.totalValue = totalValue;
        }

        public int getNumber() {
            return number;
        }

        public void setNumber(int number) {
            this.number = number;
        }
    }

}
