package com.nsy.api.tms.logistics.hlt;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>declareItem complex type�� Java �ࡣ
 *
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 *
 * <pre>
 * &lt;complexType name="declareItem">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="cnName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="customsNo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="made" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="netWeight" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
 *         &lt;element name="pieces" type="{http://www.w3.org/2001/XMLSchema}long" minOccurs="0"/>
 *         &lt;element name="productMemo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="saleUrl" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="unitPrice" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
 *         &lt;element name="used" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "declareItem", propOrder = {
        "cnName",
        "customsNo",
        "made",
        "name",
        "netWeight",
        "pieces",
        "productMemo",
        "saleUrl",
        "unitPrice",
        "used"
})
public class DeclareItem {

    protected String cnName;
    protected String customsNo;
    protected String made;
    protected String name;
    protected Double netWeight;
    protected Long pieces;
    protected String productMemo;
    protected String saleUrl;
    protected Double unitPrice;
    protected String used;

    /**
     * ��ȡcnName���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getCnName() {
        return cnName;
    }

    /**
     * ����cnName���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCnName(String value) {
        this.cnName = value;
    }

    /**
     * ��ȡcustomsNo���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getCustomsNo() {
        return customsNo;
    }

    /**
     * ����customsNo���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCustomsNo(String value) {
        this.customsNo = value;
    }

    /**
     * ��ȡmade���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getMade() {
        return made;
    }

    /**
     * ����made���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setMade(String value) {
        this.made = value;
    }

    /**
     * ��ȡname���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getName() {
        return name;
    }

    /**
     * ����name���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * ��ȡnetWeight���Ե�ֵ��
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getNetWeight() {
        return netWeight;
    }

    /**
     * ����netWeight���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setNetWeight(Double value) {
        this.netWeight = value;
    }

    /**
     * ��ȡpieces���Ե�ֵ��
     *
     * @return possible object is
     * {@link Long }
     */
    public Long getPieces() {
        return pieces;
    }

    /**
     * ����pieces���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link Long }
     */
    public void setPieces(Long value) {
        this.pieces = value;
    }

    /**
     * ��ȡproductMemo���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getProductMemo() {
        return productMemo;
    }

    /**
     * ����productMemo���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setProductMemo(String value) {
        this.productMemo = value;
    }

    /**
     * ��ȡsaleUrl���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getSaleUrl() {
        return saleUrl;
    }

    /**
     * ����saleUrl���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setSaleUrl(String value) {
        this.saleUrl = value;
    }

    /**
     * ��ȡunitPrice���Ե�ֵ��
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getUnitPrice() {
        return unitPrice;
    }

    /**
     * ����unitPrice���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setUnitPrice(Double value) {
        this.unitPrice = value;
    }

    /**
     * ��ȡused���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getUsed() {
        return used;
    }

    /**
     * ����used���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUsed(String value) {
        this.used = value;
    }

}
