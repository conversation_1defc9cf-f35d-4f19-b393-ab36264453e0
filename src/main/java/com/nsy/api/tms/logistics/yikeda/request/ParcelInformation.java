package com.nsy.api.tms.logistics.yikeda.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class ParcelInformation {
    @JsonProperty(value = "Weight")
    private Double weight; // 重量

    @JsonProperty(value = "WeightUnit")
    private Integer weightUnit; // 重量单位；/n盎司 OZ = 1,//磅 LB = 2，//克 G = 3（中邮选择），//千 克 KG =4

    @JsonProperty(value = "Length")
    private Double length; // 长，中邮默认 15

    @JsonProperty(value = "Width")
    private Double width; // 宽，中邮默认 10

    @JsonProperty(value = "Height")
    private Double height; // 高，中邮默认 3

    @JsonProperty(value = "SizeUnit")
    private Integer sizeUnit; // 尺寸单位，/n英寸 IN =1,//厘米 CM=2（中邮选 CM）//米 ,M=3；

    @JsonProperty(value = "ExistDangerousGoods")
    private Boolean existDangerousGoods; // 是否含有危险物品 非必填

    @JsonProperty(value = "ProductInformations")
    private List<ProductInformations> productInformations; // 商品详细信息，国际件该信息必填

    public static class ProductInformations {
        @JsonProperty(value = "Description")
        private String description; // 商品描述

        @JsonProperty(value = "Quantity")
        private Integer quantity; // 商品数量

        @JsonProperty(value = "Weight")
        private Double weight; // 重量

        @JsonProperty(value = "WeightUnit")
        private Integer weightUnit; // 重量单位；/n盎司 OZ = 1,//磅 LB = 2，//克 G = 3（中邮选择），//千 克 KG =4

        @JsonProperty(value = "Currency")
        private String currency; // 币种

        @JsonProperty(value = "Value")
        private Double value; // 单价

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public Integer getQuantity() {
            return quantity;
        }

        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }

        public Double getWeight() {
            return weight;
        }

        public void setWeight(Double weight) {
            this.weight = weight;
        }

        public Integer getWeightUnit() {
            return weightUnit;
        }

        public void setWeightUnit(Integer weightUnit) {
            this.weightUnit = weightUnit;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public Double getValue() {
            return value;
        }

        public void setValue(Double value) {
            this.value = value;
        }
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }


    public Double getLength() {
        return length;
    }

    public void setLength(Double length) {
        this.length = length;
    }

    public Double getWidth() {
        return width;
    }

    public void setWidth(Double width) {
        this.width = width;
    }

    public Double getHeight() {
        return height;
    }

    public void setHeight(Double height) {
        this.height = height;
    }

    public Integer getSizeUnit() {
        return sizeUnit;
    }

    public void setSizeUnit(Integer sizeUnit) {
        this.sizeUnit = sizeUnit;
    }

    public Boolean getExistDangerousGoods() {
        return existDangerousGoods;
    }

    public void setExistDangerousGoods(Boolean existDangerousGoods) {
        this.existDangerousGoods = existDangerousGoods;
    }

    public Integer getWeightUnit() {
        return weightUnit;
    }

    public void setWeightUnit(Integer weightUnit) {
        this.weightUnit = weightUnit;
    }

    public List<ProductInformations> getProductInformations() {
        return productInformations;
    }

    public void setProductInformations(List<ProductInformations> productInformations) {
        this.productInformations = productInformations;
    }
}
