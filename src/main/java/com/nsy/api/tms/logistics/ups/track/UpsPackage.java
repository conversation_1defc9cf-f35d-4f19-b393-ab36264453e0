package com.nsy.api.tms.logistics.ups.track;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/18 12:03
 */
public class UpsPackage {
    @JsonProperty(value = "TrackingNumber")
    private String trackingNumber;
    @JsonProperty(value = "Activity")
    private List<Activity> activity;
    @JsonProperty(value = "PackageWeight")
    private PackageWeight packageWeight;
    @JsonProperty(value = "ReferenceNumber")
    private List<ReferenceNumber> referenceNumberList;
    @JsonProperty(value = "Message")
    private TrackMessage message;

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }

    public PackageWeight getPackageWeight() {
        return packageWeight;
    }

    public void setPackageWeight(PackageWeight packageWeight) {
        this.packageWeight = packageWeight;
    }

    public List<Activity> getActivity() {
        return activity;
    }

    public void setActivity(List<Activity> activity) {
        this.activity = activity;
    }

    public List<ReferenceNumber> getReferenceNumberList() {
        return referenceNumberList;
    }

    public void setReferenceNumberList(List<ReferenceNumber> referenceNumberList) {
        this.referenceNumberList = referenceNumberList;
    }

    public TrackMessage getMessage() {
        return message;
    }

    public void setMessage(TrackMessage message) {
        this.message = message;
    }
}
