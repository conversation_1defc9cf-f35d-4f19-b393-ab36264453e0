package com.nsy.api.tms.logistics.jiufang.request;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-03-26 18:34
 */
public class WmsStockinOrderItemRequest {

    /**
     * SKU
     */
    private String productSku;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 箱号
     */
    private Integer boxNo;

    /**
     * 产品单价（此币种将转换为客户币种后的产 品单价）
     */
    private BigDecimal productPrice;

    /**
     * 产品币种（此币种将转换为客户币种）
     */
    private String currencyCode;

    /**
     * 关联条码,非必传,一个入库单相同SKU只能传同一个关联条码,不同的SKU必须传不同的关联条码
     */
    private String associatedBarcode;

    /**
     * 产品生产日期
     */
    private Date productDate;

    /**
     * 库存类型：0 = 按仓库确认，1 = 标准，2 = 暂存（传值 stock_type=5时等同于按仓库确认，注意相同箱号必须保持库存类型一致，箱维度未传默认按仓库确认；stock_type其他值时，箱维度需传stock_type值一致）
     */
    private Integer inventoryType;

    // Getters and Setters

    public String getProductSku() {
        return productSku;
    }

    public void setProductSku(String productSku) {
        this.productSku = productSku;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Integer getBoxNo() {
        return boxNo;
    }

    public void setBoxNo(Integer boxNo) {
        this.boxNo = boxNo;
    }

    public BigDecimal getProductPrice() {
        return productPrice;
    }

    public void setProductPrice(BigDecimal productPrice) {
        this.productPrice = productPrice;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getAssociatedBarcode() {
        return associatedBarcode;
    }

    public void setAssociatedBarcode(String associatedBarcode) {
        this.associatedBarcode = associatedBarcode;
    }

    public Date getProductDate() {
        return productDate;
    }

    public void setProductDate(Date productDate) {
        this.productDate = productDate;
    }

    public Integer getInventoryType() {
        return inventoryType;
    }

    public void setInventoryType(Integer inventoryType) {
        this.inventoryType = inventoryType;
    }
}
