package com.nsy.api.tms.logistics.hlt;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>lookupOrderInfoLoadResponse complex type�� Java �ࡣ
 *
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 *
 * <pre>
 * &lt;complexType name="lookupOrderInfoLoadResponse">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="error" type="{http://service.hop.service.ws.hlt.com/}HopHopError" minOccurs="0"/>
 *         &lt;element name="order" type="{http://service.hop.service.ws.hlt.com/}orderWithInfoLoad" minOccurs="0"/>
 *         &lt;element name="success" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "lookupOrderInfoLoadResponse", propOrder = {
        "error",
        "order",
        "success"
})
public class LookupOrderInfoLoadResponse {

    protected HopHopError error;
    protected OrderWithInfoLoad order;
    protected Boolean success;

    /**
     * ��ȡerror���Ե�ֵ��
     *
     * @return possible object is
     * {@link HopHopError }
     */
    public HopHopError getError() {
        return error;
    }

    /**
     * ����error���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link HopHopError }
     */
    public void setError(HopHopError value) {
        this.error = value;
    }

    /**
     * ��ȡorder���Ե�ֵ��
     *
     * @return possible object is
     * {@link OrderWithInfoLoad }
     */
    public OrderWithInfoLoad getOrder() {
        return order;
    }

    /**
     * ����order���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link OrderWithInfoLoad }
     */
    public void setOrder(OrderWithInfoLoad value) {
        this.order = value;
    }

    /**
     * ��ȡsuccess���Ե�ֵ��
     *
     * @return possible object is
     * {@link Boolean }
     */
    public Boolean isSuccess() {
        return success;
    }

    /**
     * ����success���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link Boolean }
     */
    public void setSuccess(Boolean value) {
        this.success = value;
    }

}
