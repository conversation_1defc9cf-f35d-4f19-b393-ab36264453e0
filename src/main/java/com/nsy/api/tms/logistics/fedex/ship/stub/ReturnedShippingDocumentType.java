/**
 * ReturnedShippingDocumentType.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.nsy.api.tms.logistics.fedex.ship.stub;

public class ReturnedShippingDocumentType implements java.io.Serializable {
    private String _value_;
    private static java.util.HashMap _table_ = new java.util.HashMap();

    // Constructor
    protected ReturnedShippingDocumentType(String value) {
        _value_ = value;
        _table_.put(_value_,this);
    }

    public static final String _AUXILIARY_LABEL = "AUXILIARY_LABEL";
    public static final String _CERTIFICATE_OF_ORIGIN = "CERTIFICATE_OF_ORIGIN";
    public static final String _COD_AND_DELIVERY_ON_INVOICE_ACCEPTANCE_RETURN_2_D_BARCODE = "COD_AND_DELIVERY_ON_INVOICE_ACCEPTANCE_RETURN_2_D_BARCODE";
    public static final String _COD_AND_DELIVERY_ON_INVOICE_ACCEPTANCE_RETURN_LABEL = "COD_AND_DELIVERY_ON_INVOICE_ACCEPTANCE_RETURN_LABEL";
    public static final String _COD_RETURN_2_D_BARCODE = "COD_RETURN_2_D_BARCODE";
    public static final String _COD_RETURN_LABEL = "COD_RETURN_LABEL";
    public static final String _COMMERCIAL_INVOICE = "COMMERCIAL_INVOICE";
    public static final String _CUSTOM_PACKAGE_DOCUMENT = "CUSTOM_PACKAGE_DOCUMENT";
    public static final String _CUSTOM_SHIPMENT_DOCUMENT = "CUSTOM_SHIPMENT_DOCUMENT";
    public static final String _DANGEROUS_GOODS_SHIPPERS_DECLARATION = "DANGEROUS_GOODS_SHIPPERS_DECLARATION";
    public static final String _DELIVERY_ON_INVOICE_ACCEPTANCE_RETURN_2_D_BARCODE = "DELIVERY_ON_INVOICE_ACCEPTANCE_RETURN_2_D_BARCODE";
    public static final String _DELIVERY_ON_INVOICE_ACCEPTANCE_RETURN_LABEL = "DELIVERY_ON_INVOICE_ACCEPTANCE_RETURN_LABEL";
    public static final String _ETD_LABEL = "ETD_LABEL";
    public static final String _EXPORT_DECLARATION = "EXPORT_DECLARATION";
    public static final String _FEDEX_FREIGHT_STRAIGHT_BILL_OF_LADING = "FEDEX_FREIGHT_STRAIGHT_BILL_OF_LADING";
    public static final String _FREIGHT_ADDRESS_LABEL = "FREIGHT_ADDRESS_LABEL";
    public static final String _GENERAL_AGENCY_AGREEMENT = "GENERAL_AGENCY_AGREEMENT";
    public static final String _GROUND_BARCODE = "GROUND_BARCODE";
    public static final String _NAFTA_CERTIFICATE_OF_ORIGIN = "NAFTA_CERTIFICATE_OF_ORIGIN";
    public static final String _OP_900 = "OP_900";
    public static final String _OUTBOUND_2_D_BARCODE = "OUTBOUND_2_D_BARCODE";
    public static final String _OUTBOUND_LABEL = "OUTBOUND_LABEL";
    public static final String _PRO_FORMA_INVOICE = "PRO_FORMA_INVOICE";
    public static final String _RECIPIENT_ADDRESS_BARCODE = "RECIPIENT_ADDRESS_BARCODE";
    public static final String _RECIPIENT_POSTAL_BARCODE = "RECIPIENT_POSTAL_BARCODE";
    public static final String _RETURN_INSTRUCTIONS = "RETURN_INSTRUCTIONS";
    public static final String _TERMS_AND_CONDITIONS = "TERMS_AND_CONDITIONS";
    public static final String _USPS_BARCODE = "USPS_BARCODE";
    public static final String _VICS_BILL_OF_LADING = "VICS_BILL_OF_LADING";
    public static final ReturnedShippingDocumentType AUXILIARY_LABEL = new ReturnedShippingDocumentType(_AUXILIARY_LABEL);
    public static final ReturnedShippingDocumentType CERTIFICATE_OF_ORIGIN = new ReturnedShippingDocumentType(_CERTIFICATE_OF_ORIGIN);
    public static final ReturnedShippingDocumentType COD_AND_DELIVERY_ON_INVOICE_ACCEPTANCE_RETURN_2_D_BARCODE = new ReturnedShippingDocumentType(_COD_AND_DELIVERY_ON_INVOICE_ACCEPTANCE_RETURN_2_D_BARCODE);
    public static final ReturnedShippingDocumentType COD_AND_DELIVERY_ON_INVOICE_ACCEPTANCE_RETURN_LABEL = new ReturnedShippingDocumentType(_COD_AND_DELIVERY_ON_INVOICE_ACCEPTANCE_RETURN_LABEL);
    public static final ReturnedShippingDocumentType COD_RETURN_2_D_BARCODE = new ReturnedShippingDocumentType(_COD_RETURN_2_D_BARCODE);
    public static final ReturnedShippingDocumentType COD_RETURN_LABEL = new ReturnedShippingDocumentType(_COD_RETURN_LABEL);
    public static final ReturnedShippingDocumentType COMMERCIAL_INVOICE = new ReturnedShippingDocumentType(_COMMERCIAL_INVOICE);
    public static final ReturnedShippingDocumentType CUSTOM_PACKAGE_DOCUMENT = new ReturnedShippingDocumentType(_CUSTOM_PACKAGE_DOCUMENT);
    public static final ReturnedShippingDocumentType CUSTOM_SHIPMENT_DOCUMENT = new ReturnedShippingDocumentType(_CUSTOM_SHIPMENT_DOCUMENT);
    public static final ReturnedShippingDocumentType DANGEROUS_GOODS_SHIPPERS_DECLARATION = new ReturnedShippingDocumentType(_DANGEROUS_GOODS_SHIPPERS_DECLARATION);
    public static final ReturnedShippingDocumentType DELIVERY_ON_INVOICE_ACCEPTANCE_RETURN_2_D_BARCODE = new ReturnedShippingDocumentType(_DELIVERY_ON_INVOICE_ACCEPTANCE_RETURN_2_D_BARCODE);
    public static final ReturnedShippingDocumentType DELIVERY_ON_INVOICE_ACCEPTANCE_RETURN_LABEL = new ReturnedShippingDocumentType(_DELIVERY_ON_INVOICE_ACCEPTANCE_RETURN_LABEL);
    public static final ReturnedShippingDocumentType ETD_LABEL = new ReturnedShippingDocumentType(_ETD_LABEL);
    public static final ReturnedShippingDocumentType EXPORT_DECLARATION = new ReturnedShippingDocumentType(_EXPORT_DECLARATION);
    public static final ReturnedShippingDocumentType FEDEX_FREIGHT_STRAIGHT_BILL_OF_LADING = new ReturnedShippingDocumentType(_FEDEX_FREIGHT_STRAIGHT_BILL_OF_LADING);
    public static final ReturnedShippingDocumentType FREIGHT_ADDRESS_LABEL = new ReturnedShippingDocumentType(_FREIGHT_ADDRESS_LABEL);
    public static final ReturnedShippingDocumentType GENERAL_AGENCY_AGREEMENT = new ReturnedShippingDocumentType(_GENERAL_AGENCY_AGREEMENT);
    public static final ReturnedShippingDocumentType GROUND_BARCODE = new ReturnedShippingDocumentType(_GROUND_BARCODE);
    public static final ReturnedShippingDocumentType NAFTA_CERTIFICATE_OF_ORIGIN = new ReturnedShippingDocumentType(_NAFTA_CERTIFICATE_OF_ORIGIN);
    public static final ReturnedShippingDocumentType OP_900 = new ReturnedShippingDocumentType(_OP_900);
    public static final ReturnedShippingDocumentType OUTBOUND_2_D_BARCODE = new ReturnedShippingDocumentType(_OUTBOUND_2_D_BARCODE);
    public static final ReturnedShippingDocumentType OUTBOUND_LABEL = new ReturnedShippingDocumentType(_OUTBOUND_LABEL);
    public static final ReturnedShippingDocumentType PRO_FORMA_INVOICE = new ReturnedShippingDocumentType(_PRO_FORMA_INVOICE);
    public static final ReturnedShippingDocumentType RECIPIENT_ADDRESS_BARCODE = new ReturnedShippingDocumentType(_RECIPIENT_ADDRESS_BARCODE);
    public static final ReturnedShippingDocumentType RECIPIENT_POSTAL_BARCODE = new ReturnedShippingDocumentType(_RECIPIENT_POSTAL_BARCODE);
    public static final ReturnedShippingDocumentType RETURN_INSTRUCTIONS = new ReturnedShippingDocumentType(_RETURN_INSTRUCTIONS);
    public static final ReturnedShippingDocumentType TERMS_AND_CONDITIONS = new ReturnedShippingDocumentType(_TERMS_AND_CONDITIONS);
    public static final ReturnedShippingDocumentType USPS_BARCODE = new ReturnedShippingDocumentType(_USPS_BARCODE);
    public static final ReturnedShippingDocumentType VICS_BILL_OF_LADING = new ReturnedShippingDocumentType(_VICS_BILL_OF_LADING);
    public String getValue() { return _value_;}
    public static ReturnedShippingDocumentType fromValue(String value)
          throws IllegalArgumentException {
        ReturnedShippingDocumentType enumeration = (ReturnedShippingDocumentType)
            _table_.get(value);
        if (enumeration==null) throw new IllegalArgumentException();
        return enumeration;
    }
    public static ReturnedShippingDocumentType fromString(String value)
          throws IllegalArgumentException {
        return fromValue(value);
    }
    public boolean equals(Object obj) {return (obj == this);}
    public int hashCode() { return toString().hashCode();}
    public String toString() { return _value_;}
    public Object readResolve() throws java.io.ObjectStreamException { return fromValue(_value_);}
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumSerializer(
            _javaType, _xmlType);
    }
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumDeserializer(
            _javaType, _xmlType);
    }
    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(ReturnedShippingDocumentType.class);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://fedex.com/ws/ship/v26", "ReturnedShippingDocumentType"));
    }
    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

}
