package com.nsy.api.tms.logistics.ydh.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nsy.api.tms.logistics.toms.request.Consignee;
import com.nsy.api.tms.logistics.toms.request.Invoice;
import com.nsy.api.tms.logistics.toms.request.Shipper;
import java.util.List;

public class YdhOrderRequest extends YdhBaseRequest {

    @JsonProperty(value = "reference_no")
    private String referenceNo; // 客户参考号

    @JsonProperty(value = "shipping_method")
    private String shippingMethod; // 运输方式代码

    @JsonProperty(value = "shipping_method_no")
    private String shippingMethodNo; // 服务商单号

    @JsonProperty(value = "order_weight")
    private String orderWeight; // 订单重量，单位KG，默认为0.2

    @JsonProperty(value = "order_pieces")
    private String orderPieces; // 外包装件数,默认1

    @JsonProperty(value = "cargotype")
    private String cargoType; // 货物类型 W：包裹 D：文件 B：袋子

    @JsonProperty(value = "mail_cargo_type")
    private String mailCargoType; // 包裹申报种类 1：Gif礼品 2：CommercialSample 商品货样3：Document 文件4：Other 其他默认4

    @JsonProperty(value = "return_sign")
    private String returnSign; // 是否需要标识退件退回 (Y,N)

    @JsonProperty(value = "platform_id")
    private String platformId; // 平台ID

    @JsonProperty(value = "custom_hawbcode")
    private String customHawbCode; // 自定义单号

    @JsonProperty(value = "shipper")
    private Shipper shipper; // 寄件人信息

    @JsonProperty(value = "consignee")
    private Consignee consignee; // 收件人信息

    @JsonProperty(value = "invoice")
    private List<Invoice> invoiceList; // 海关申报信息


    public String getReferenceNo() {
        return referenceNo;
    }

    public void setReferenceNo(String referenceNo) {
        this.referenceNo = referenceNo;
    }

    public String getShippingMethod() {
        return shippingMethod;
    }

    public void setShippingMethod(String shippingMethod) {
        this.shippingMethod = shippingMethod;
    }

    public String getShippingMethodNo() {
        return shippingMethodNo;
    }

    public void setShippingMethodNo(String shippingMethodNo) {
        this.shippingMethodNo = shippingMethodNo;
    }

    public String getOrderWeight() {
        return orderWeight;
    }

    public void setOrderWeight(String orderWeight) {
        this.orderWeight = orderWeight;
    }

    public String getOrderPieces() {
        return orderPieces;
    }

    public void setOrderPieces(String orderPieces) {
        this.orderPieces = orderPieces;
    }

    public String getCargoType() {
        return cargoType;
    }

    public void setCargoType(String cargoType) {
        this.cargoType = cargoType;
    }

    public String getMailCargoType() {
        return mailCargoType;
    }

    public void setMailCargoType(String mailCargoType) {
        this.mailCargoType = mailCargoType;
    }

    public String getReturnSign() {
        return returnSign;
    }

    public void setReturnSign(String returnSign) {
        this.returnSign = returnSign;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public String getCustomHawbCode() {
        return customHawbCode;
    }

    public void setCustomHawbCode(String customHawbCode) {
        this.customHawbCode = customHawbCode;
    }

    public Shipper getShipper() {
        return shipper;
    }

    public void setShipper(Shipper shipper) {
        this.shipper = shipper;
    }

    public Consignee getConsignee() {
        return consignee;
    }

    public void setConsignee(Consignee consignee) {
        this.consignee = consignee;
    }

    public List<Invoice> getInvoiceList() {
        return invoiceList;
    }

    public void setInvoiceList(List<Invoice> invoiceList) {
        this.invoiceList = invoiceList;
    }
}
