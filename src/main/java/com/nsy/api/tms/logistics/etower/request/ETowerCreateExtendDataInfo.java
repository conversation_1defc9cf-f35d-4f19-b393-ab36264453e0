package com.nsy.api.tms.logistics.etower.request;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/4/15 16:59
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ETowerCreateExtendDataInfo {

    /**
     * 发件人税号
     */
    private String vendorid;

    /**
     * GST免税码，如果vendorid正确，该字段值自动设置为“PAID”
     */
    private String gstexemptionCode;

    /**
     * 发货人税号
     */
    private String senderTaxId;

    /**
     * 订单配送港口，针对加拿大服务
     */
    private String injectPort;

    /**
     * 订单起始港口
     */
    private String originPort;

    /**
     * 运费
     */
    private BigDecimal postage;

    /**
     * 巴西icms 费用
     */
    private BigDecimal icms;

    /**
     * 税号
     */
    private BigDecimal vat;

    public String getVendorid() {
        return vendorid;
    }

    public void setVendorid(String vendorid) {
        this.vendorid = vendorid;
    }

    public String getGstexemptionCode() {
        return gstexemptionCode;
    }

    public void setGstexemptionCode(String gstexemptionCode) {
        this.gstexemptionCode = gstexemptionCode;
    }

    public String getSenderTaxId() {
        return senderTaxId;
    }

    public void setSenderTaxId(String senderTaxId) {
        this.senderTaxId = senderTaxId;
    }

    public String getInjectPort() {
        return injectPort;
    }

    public void setInjectPort(String injectPort) {
        this.injectPort = injectPort;
    }

    public String getOriginPort() {
        return originPort;
    }

    public void setOriginPort(String originPort) {
        this.originPort = originPort;
    }

    public BigDecimal getPostage() {
        return postage;
    }

    public void setPostage(BigDecimal postage) {
        this.postage = postage;
    }

    public BigDecimal getIcms() {
        return icms;
    }

    public void setIcms(BigDecimal icms) {
        this.icms = icms;
    }

    public BigDecimal getVat() {
        return vat;
    }

    public void setVat(BigDecimal vat) {
        this.vat = vat;
    }
}
