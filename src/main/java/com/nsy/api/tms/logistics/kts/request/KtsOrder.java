package com.nsy.api.tms.logistics.kts.request;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlAccessorType(XmlAccessType.NONE)
@XmlRootElement(name = "Order")
public class KtsOrder {
    @XmlAttribute(name = "orderid")
    private String orderId;

    //平台订单号
    @XmlAttribute(name = "platform_order_id")
    private String platformOrderId;

    //平台简称
    @XmlAttribute(name = "platform_code")
    private String platformCode;

    @XmlAttribute(name = "erp_code")
    private String erpCode;

    //电商平台ID，非必传
    @XmlAttribute(name = "platform_merchant_id")
    private String platformMerchantId;

    @XmlAttribute(name = "express_type")
    private String expressType;

    @XmlAttribute(name = "j_company")
    private String senderCompany;

    @XmlAttribute(name = "j_contact")
    private String senderContact;

    @XmlAttribute(name = "j_tel")
    private String senderTel;

    @XmlAttribute(name = "j_mobile")
    private String senderMobile;

    @XmlAttribute(name = "j_province")
    private String senderProvince;

    @XmlAttribute(name = "j_city")
    private String senderCity;

    @XmlAttribute(name = "j_address")
    private String senderAddress;

    @XmlAttribute(name = "d_company")
    private String receiverCompany;

    @XmlAttribute(name = "d_contact")
    private String receiverContact;

    @XmlAttribute(name = "d_tel")
    private String receiverTel;

    @XmlAttribute(name = "d_mobile")
    private String receiverMobile;

    @XmlAttribute(name = "d_province")
    private String receiverProvince;

    @XmlAttribute(name = "d_city")
    private String receiverCity;

    @XmlAttribute(name = "d_address")
    private String receiverAddress;

    @XmlAttribute(name = "parcel_quantity")
    private Integer parcelQuantity = 1;

    @XmlAttribute(name = "pay_method")
    private Integer payMethod = 1;

    //订单托寄物声明价值（大于零）
    @XmlAttribute(name = "declared_value")
    private Double declaredValue;

    @XmlAttribute(name = "declared_value_currency")
    private String declaredValueCurrency = "USD";

    //客户月结卡号, 非必传
    @XmlAttribute(name = "custid")
    private String custid;

    @XmlAttribute(name = "j_country")
    private String senderCountry = "CN";

    @XmlAttribute(name = "j_county")
    private String senderCounty;

    @XmlAttribute(name = "j_post_code")
    private String senderPostCode;

    @XmlAttribute(name = "d_country")
    private String receiverCountry;

    @XmlAttribute(name = "d_county")
    private String receiverCounty;

    @XmlAttribute(name = "d_post_code")
    private String receiverPostCode;

    @XmlAttribute(name = "cargo_total_weight")
    private Double cargoTotalWeight;

    @XmlAttribute(name = "sendstarttime")
    private String sendStartTime;

    @XmlAttribute(name = "operate_flag")
    private String operateFlag = "1";

    @XmlAttribute(name = "isBat")
    private String isBattery = "0";

    @XmlAttribute(name = "cargo_length")
    private Double cargoLength;

    @XmlAttribute(name = "cargo_width")
    private Double cargoWidth;

    @XmlAttribute(name = "cargo_height")
    private Double cargoHeight;

    @XmlAttribute(name = "category")
    private String category;

    @XmlAttribute(name = "remark")
    private String remark;

    //目的国为澳洲AU ，产品类型为国际小包挂号10，
    // 税号和ABN不能同时为空，其它国家和产品类型选填
    @XmlAttribute(name = "tax_number")
    private String taxNumber;

    @XmlAttribute(name = "abn")
    private String abn;

    @XmlAttribute(name = "gst_exemption_code")
    private String gstExemptionCode;

    @XmlElement(name = "Cargo")
    private List<KtsCargo> cargo;

    //express_type为29时，才有此节点
    @XmlElement(name = "Extra")
    private KtsExtra extra;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getPlatformOrderId() {
        return platformOrderId;
    }

    public void setPlatformOrderId(String platformOrderId) {
        this.platformOrderId = platformOrderId;
    }

    public String getPlatformCode() {
        return platformCode;
    }

    public void setPlatformCode(String platformCode) {
        this.platformCode = platformCode;
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode;
    }

    public String getPlatformMerchantId() {
        return platformMerchantId;
    }

    public void setPlatformMerchantId(String platformMerchantId) {
        this.platformMerchantId = platformMerchantId;
    }

    public String getExpressType() {
        return expressType;
    }

    public void setExpressType(String expressType) {
        this.expressType = expressType;
    }

    public String getSenderCompany() {
        return senderCompany;
    }

    public void setSenderCompany(String senderCompany) {
        this.senderCompany = senderCompany;
    }

    public String getSenderContact() {
        return senderContact;
    }

    public void setSenderContact(String senderContact) {
        this.senderContact = senderContact;
    }

    public String getSenderTel() {
        return senderTel;
    }

    public void setSenderTel(String senderTel) {
        this.senderTel = senderTel;
    }

    public String getSenderMobile() {
        return senderMobile;
    }

    public void setSenderMobile(String senderMobile) {
        this.senderMobile = senderMobile;
    }

    public String getSenderProvince() {
        return senderProvince;
    }

    public void setSenderProvince(String senderProvince) {
        this.senderProvince = senderProvince;
    }

    public String getSenderCity() {
        return senderCity;
    }

    public void setSenderCity(String senderCity) {
        this.senderCity = senderCity;
    }

    public String getSenderAddress() {
        return senderAddress;
    }

    public void setSenderAddress(String senderAddress) {
        this.senderAddress = senderAddress;
    }

    public String getReceiverCompany() {
        return receiverCompany;
    }

    public void setReceiverCompany(String receiverCompany) {
        this.receiverCompany = receiverCompany;
    }

    public String getReceiverContact() {
        return receiverContact;
    }

    public void setReceiverContact(String receiverContact) {
        this.receiverContact = receiverContact;
    }

    public String getReceiverTel() {
        return receiverTel;
    }

    public void setReceiverTel(String receiverTel) {
        this.receiverTel = receiverTel;
    }

    public String getReceiverMobile() {
        return receiverMobile;
    }

    public void setReceiverMobile(String receiverMobile) {
        this.receiverMobile = receiverMobile;
    }

    public String getReceiverProvince() {
        return receiverProvince;
    }

    public void setReceiverProvince(String receiverProvince) {
        this.receiverProvince = receiverProvince;
    }

    public String getReceiverCity() {
        return receiverCity;
    }

    public void setReceiverCity(String receiverCity) {
        this.receiverCity = receiverCity;
    }

    public String getReceiverAddress() {
        return receiverAddress;
    }

    public void setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress;
    }

    public Integer getParcelQuantity() {
        return parcelQuantity;
    }

    public void setParcelQuantity(Integer parcelQuantity) {
        this.parcelQuantity = parcelQuantity;
    }

    public Integer getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(Integer payMethod) {
        this.payMethod = payMethod;
    }

    public Double getDeclaredValue() {
        return declaredValue;
    }

    public void setDeclaredValue(Double declaredValue) {
        this.declaredValue = declaredValue;
    }

    public String getDeclaredValueCurrency() {
        return declaredValueCurrency;
    }

    public void setDeclaredValueCurrency(String declaredValueCurrency) {
        this.declaredValueCurrency = declaredValueCurrency;
    }

    public String getCustid() {
        return custid;
    }

    public void setCustid(String custid) {
        this.custid = custid;
    }

    public String getSenderCountry() {
        return senderCountry;
    }

    public void setSenderCountry(String senderCountry) {
        this.senderCountry = senderCountry;
    }

    public String getSenderCounty() {
        return senderCounty;
    }

    public void setSenderCounty(String senderCounty) {
        this.senderCounty = senderCounty;
    }

    public String getSenderPostCode() {
        return senderPostCode;
    }

    public void setSenderPostCode(String senderPostCode) {
        this.senderPostCode = senderPostCode;
    }

    public String getReceiverCountry() {
        return receiverCountry;
    }

    public void setReceiverCountry(String receiverCountry) {
        this.receiverCountry = receiverCountry;
    }

    public String getReceiverCounty() {
        return receiverCounty;
    }

    public void setReceiverCounty(String receiverCounty) {
        this.receiverCounty = receiverCounty;
    }

    public String getReceiverPostCode() {
        return receiverPostCode;
    }

    public void setReceiverPostCode(String receiverPostCode) {
        this.receiverPostCode = receiverPostCode;
    }

    public Double getCargoTotalWeight() {
        return cargoTotalWeight;
    }

    public void setCargoTotalWeight(Double cargoTotalWeight) {
        this.cargoTotalWeight = cargoTotalWeight;
    }

    public String getSendStartTime() {
        return sendStartTime;
    }

    public void setSendStartTime(String sendStartTime) {
        this.sendStartTime = sendStartTime;
    }

    public String getOperateFlag() {
        return operateFlag;
    }

    public void setOperateFlag(String operateFlag) {
        this.operateFlag = operateFlag;
    }

    public String getIsBattery() {
        return isBattery;
    }

    public void setIsBattery(String isBattery) {
        this.isBattery = isBattery;
    }

    public Double getCargoLength() {
        return cargoLength;
    }

    public void setCargoLength(Double cargoLength) {
        this.cargoLength = cargoLength;
    }

    public Double getCargoWidth() {
        return cargoWidth;
    }

    public void setCargoWidth(Double cargoWidth) {
        this.cargoWidth = cargoWidth;
    }

    public Double getCargoHeight() {
        return cargoHeight;
    }

    public void setCargoHeight(Double cargoHeight) {
        this.cargoHeight = cargoHeight;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTaxNumber() {
        return taxNumber;
    }

    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }

    public String getAbn() {
        return abn;
    }

    public void setAbn(String abn) {
        this.abn = abn;
    }

    public String getGstExemptionCode() {
        return gstExemptionCode;
    }

    public void setGstExemptionCode(String gstExemptionCode) {
        this.gstExemptionCode = gstExemptionCode;
    }

    public List<KtsCargo> getCargo() {
        return cargo;
    }

    public void setCargo(List<KtsCargo> cargo) {
        this.cargo = cargo;
    }

    public KtsExtra getExtra() {
        return extra;
    }

    public void setExtra(KtsExtra extra) {
        this.extra = extra;
    }
}
