package com.nsy.api.tms.logistics.zuohai;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * HXD
 * 2024/1/22
 **/

public class StockinOrderResponse {
    @JsonProperty("id")
    private String id;

    @JsonProperty("waybill")
    private String waybill;

    @JsonProperty("order_no")
    private String orderNo;

    @JsonProperty("order_pb")
    private String orderPb;

    @JsonProperty("addtime")
    private String addtime;

    @JsonProperty("pdf_url")
    private String pdfUrl;

    @JsonProperty("order_products")
    private List<StockinOrderProduct> orderProducts;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getWaybill() {
        return waybill;
    }

    public void setWaybill(String waybill) {
        this.waybill = waybill;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderPb() {
        return orderPb;
    }

    public void setOrderPb(String orderPb) {
        this.orderPb = orderPb;
    }

    public String getAddtime() {
        return addtime;
    }

    public void setAddtime(String addtime) {
        this.addtime = addtime;
    }

    public String getPdfUrl() {
        return pdfUrl;
    }

    public void setPdfUrl(String pdfUrl) {
        this.pdfUrl = pdfUrl;
    }

    public List<StockinOrderProduct> getOrderProducts() {
        return orderProducts;
    }

    public void setOrderProducts(List<StockinOrderProduct> orderProducts) {
        this.orderProducts = orderProducts;
    }
}
