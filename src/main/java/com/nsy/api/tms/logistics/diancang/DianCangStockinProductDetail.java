package com.nsy.api.tms.logistics.diancang;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/4/7 16:01
 */
@XmlRootElement(name = "SKU")
@XmlAccessorType(XmlAccessType.FIELD)
public class DianCangStockinProductDetail {

    /**
     * 装箱清单号
     */
    @XmlElement(name = "CustomerPackageNumber")
    private String customerPackageNumber;

    /**
     * 客户SKU编号,相同的仓库，如果多个入库单中出现相同的PartnerSKUID，则认为是同一个SKU
     */
    @XmlElement(name = "PartnerSKUID")
    private String partnerSKUID;

    /**
     * 应入库数量
     */
    @XmlElement(name = "ExpectedQuantity")
    private Integer expectedQuantity;

    /**
     * 实际数量
     */
    @XmlElement(name = "ActualQuantity")
    private Integer actualQuantity;

    /**
     * 当前数量
     */
    @XmlElement(name = "CurrentQuantity")
    private Integer currentQuantity;

    @XmlElement(name = "LockedQuantity")
    private Integer lockedQuantity = 0;

    /**
     * 货物名称
     */
    @XmlElement(name = "Name")
    private String name;


    /**
     * 英文名
     */
    @XmlElement(name = "EnglishName")
    private String englishName;

    /**
     * 条形码
     */
    @XmlElement(name = "BarCode")
    private String barCode;

    /**
     * 品牌
     */
    @XmlElement(name = "Brand")
    private String brand;

    /**
     * 规格
     */
    @XmlElement(name = "Capacity")
    private String capacity;

    /**
     * 产地
     */
    @XmlElement(name = "ProductionPlace")
    private String productionPlace;

    /**
     * 内物单位
     */
    @XmlElement(name = "DeclareUnitType")
    private String declareUnitType;

    /**
     * 单价
     */
    @XmlElement(name = "UnitPrice")
    private String unitPrice;

    /**
     * 最后售价
     */
    @XmlElement(name = "ActualPrice")
    private BigDecimal actualPrice;

    /**
     * 货币代码
     */
    @XmlElement(name = "CurrencyCode")
    private String currencyCode;

    /**
     * 重量
     */
    @XmlElement(name = "GrossWeight")
    private String grossWeight;

    /**
     * 净重
     */
    @XmlElement(name = "NetWeight")
    private BigDecimal netWeight;

    /**
     * 尺寸
     */
    @XmlElement(name = "Size")
    private String size;

    /**
     * 单位制类型
     */
    @XmlElement(name = "UnitType")
    private String unitType;

    /**
     * 分类
     */
    @XmlElement(name = "Category")
    private String category;

    /**
     * 海关税号
     */
    @XmlElement(name = "DutyCode")
    private String dutyCode;

    /**
     * 用途
     */
    @XmlElement(name = "Usage")
    private String usage;

    /**
     * 主要成分
     */
    @XmlElement(name = "Ingredient")
    private String ingredient;

    /**
     * 备注信息
     */
    @XmlElement(name = "Remark")
    private String remark;

    /**
     * sku备货编号
     */
    @XmlElement(name = "SerialNumber")
    private String serialNumber;

    /**
     * hs code
     */
    @XmlElement(name = "HSCode")
    private String hsCode;

    public Integer getLockedQuantity() {
        return lockedQuantity;
    }

    public void setLockedQuantity(Integer lockedQuantity) {
        this.lockedQuantity = lockedQuantity;
    }

    public String getPartnerSKUID() {
        return partnerSKUID;
    }

    public void setPartnerSKUID(String partnerSKUID) {
        this.partnerSKUID = partnerSKUID;
    }

    public Integer getExpectedQuantity() {
        return expectedQuantity;
    }

    public void setExpectedQuantity(Integer expectedQuantity) {
        this.expectedQuantity = expectedQuantity;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getCapacity() {
        return capacity;
    }

    public void setCapacity(String capacity) {
        this.capacity = capacity;
    }

    public String getProductionPlace() {
        return productionPlace;
    }

    public void setProductionPlace(String productionPlace) {
        this.productionPlace = productionPlace;
    }

    public String getDeclareUnitType() {
        return declareUnitType;
    }

    public void setDeclareUnitType(String declareUnitType) {
        this.declareUnitType = declareUnitType;
    }

    public String getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(String unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(String grossWeight) {
        this.grossWeight = grossWeight;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getUnitType() {
        return unitType;
    }

    public void setUnitType(String unitType) {
        this.unitType = unitType;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getDutyCode() {
        return dutyCode;
    }

    public void setDutyCode(String dutyCode) {
        this.dutyCode = dutyCode;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    public String getIngredient() {
        return ingredient;
    }

    public void setIngredient(String ingredient) {
        this.ingredient = ingredient;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getActualQuantity() {
        return actualQuantity;
    }

    public void setActualQuantity(Integer actualQuantity) {
        this.actualQuantity = actualQuantity;
    }

    public Integer getCurrentQuantity() {
        return currentQuantity;
    }

    public void setCurrentQuantity(Integer currentQuantity) {
        this.currentQuantity = currentQuantity;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    public BigDecimal getActualPrice() {
        return actualPrice;
    }

    public void setActualPrice(BigDecimal actualPrice) {
        this.actualPrice = actualPrice;
    }

    public BigDecimal getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    public String getHsCode() {
        return hsCode;
    }

    public void setHsCode(String hsCode) {
        this.hsCode = hsCode;
    }

    public String getCustomerPackageNumber() {
        return customerPackageNumber;
    }

    public void setCustomerPackageNumber(String customerPackageNumber) {
        this.customerPackageNumber = customerPackageNumber;
    }
}
