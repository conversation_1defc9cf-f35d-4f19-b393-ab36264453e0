package com.nsy.api.tms.logistics.zuohai;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * HXD
 * 2024/1/22
 **/

public class StockinOrderProduct {
    @JsonProperty("zxh")
    private String zxh;

    @JsonProperty("pwaybill")
    private String pwaybill;

    @JsonProperty("weight")
    private String weight;

    @JsonProperty("cc")
    private String cc;

    @JsonProperty("kk")
    private String kk;

    @JsonProperty("gg")
    private String gg;

    @JsonProperty("nums")
    private String nums;

    @JsonProperty("md_time")
    private String mdTime;

    @JsonProperty("mu_nums")
    private String muNums;

    @JsonProperty("ck_sku")
    private String ckSku;

    @JsonProperty("pname")
    private String pname;

    @JsonProperty("order_id")
    private String orderId;

    @JsonProperty("pb")
    private String pb;

    @JsonProperty("status")
    private String status;

    public String getZxh() {
        return zxh;
    }

    public void setZxh(String zxh) {
        this.zxh = zxh;
    }

    public String getPwaybill() {
        return pwaybill;
    }

    public void setPwaybill(String pwaybill) {
        this.pwaybill = pwaybill;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getCc() {
        return cc;
    }

    public void setCc(String cc) {
        this.cc = cc;
    }

    public String getKk() {
        return kk;
    }

    public void setKk(String kk) {
        this.kk = kk;
    }

    public String getGg() {
        return gg;
    }

    public void setGg(String gg) {
        this.gg = gg;
    }

    public String getNums() {
        return nums;
    }

    public void setNums(String nums) {
        this.nums = nums;
    }

    public String getMdTime() {
        return mdTime;
    }

    public void setMdTime(String mdTime) {
        this.mdTime = mdTime;
    }

    public String getMuNums() {
        return muNums;
    }

    public void setMuNums(String muNums) {
        this.muNums = muNums;
    }

    public String getCkSku() {
        return ckSku;
    }

    public void setCkSku(String ckSku) {
        this.ckSku = ckSku;
    }

    public String getPname() {
        return pname;
    }

    public void setPname(String pname) {
        this.pname = pname;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getPb() {
        return pb;
    }

    public void setPb(String pb) {
        this.pb = pb;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
