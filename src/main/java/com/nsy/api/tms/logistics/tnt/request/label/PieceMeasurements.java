package com.nsy.api.tms.logistics.tnt.request.label;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/22 17:17
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "pieceMeasurements")
public class PieceMeasurements {
    @XmlElement(name = "length")
    private Double length;
    @XmlElement(name = "width")
    private Double width;
    @XmlElement(name = "height")
    private Double height;
    @XmlElement(name = "weight")
    private Double weight;

    public Double getLength() {
        return length;
    }

    public void setLength(Double length) {
        this.length = length;
    }

    public Double getWidth() {
        return width;
    }

    public void setWidth(Double width) {
        this.width = width;
    }

    public Double getHeight() {
        return height;
    }

    public void setHeight(Double height) {
        this.height = height;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }
}
