package com.nsy.api.tms.logistics.tnt.request.label;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/22 16:49
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "consignmentIdentity")
public class ConsignmentIdentity {
    @XmlElement(name = "consignmentNumber")
    private String consignmentNumber;
    @XmlElement(name = "customerReference")
    private String customerReference;

    public String getConsignmentNumber() {
        return consignmentNumber;
    }

    public void setConsignmentNumber(String consignmentNumber) {
        this.consignmentNumber = consignmentNumber;
    }

    public String getCustomerReference() {
        return customerReference;
    }

    public void setCustomerReference(String customerReference) {
        this.customerReference = customerReference;
    }
}
