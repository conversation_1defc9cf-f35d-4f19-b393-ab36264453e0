package com.nsy.api.tms.logistics.seko;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nsy.api.tms.request.BaseLogisticsOrderRequest;

import java.util.List;

public class SekoRequest extends BaseLogisticsOrderRequest {
    @JsonProperty(value = "DeliveryReference")
    private String deliveryReference;
    @JsonProperty(value = "Reference2")
    private String reference2;
    @JsonProperty(value = "Reference3")
    private String reference3;
    @JsonProperty(value = "Origin")
    private Contact origin;
    @JsonProperty(value = "Destination")
    private Contact destination;
    @JsonProperty(value = "Commodities")
    private List<Commodities> commodities;
    @JsonProperty(value = "Packages")
    private List<Packages> packages;
    @JsonProperty(value = "issignaturerequired")
    private Boolean isSignatureRequired;
    @JsonProperty(value = "DutiesAndTaxesByReceiver")
    private Boolean dutiesAndTaxesByReceiver;
    @JsonProperty(value = "PrintToPrinter")
    private Boolean printToPrinter;
    @JsonProperty(value = "IncludeLineDetails")
    private Boolean includeLineDetails;
    @JsonProperty(value = "CostCentreName")
    private String costCentreName;
    @JsonProperty(value = "TaxIds")
    private List<TaxIds> taxIds;
    @JsonProperty(value = "Outputs")
    private List<String> outputs;

    public String getDeliveryReference() {
        return deliveryReference;
    }

    public void setDeliveryReference(String deliveryReference) {
        this.deliveryReference = deliveryReference;
    }

    public String getReference2() {
        return reference2;
    }

    public void setReference2(String reference2) {
        this.reference2 = reference2;
    }

    public String getReference3() {
        return reference3;
    }

    public void setReference3(String reference3) {
        this.reference3 = reference3;
    }

    public Contact getOrigin() {
        return origin;
    }

    public void setOrigin(Contact origin) {
        this.origin = origin;
    }

    public Contact getDestination() {
        return destination;
    }

    public void setDestination(Contact destination) {
        this.destination = destination;
    }

    public List<Commodities> getCommodities() {
        return commodities;
    }

    public void setCommodities(List<Commodities> commodities) {
        this.commodities = commodities;
    }

    public List<Packages> getPackages() {
        return packages;
    }

    public void setPackages(List<Packages> packages) {
        this.packages = packages;
    }

    public Boolean getSignatureRequired() {
        return isSignatureRequired;
    }

    public void setSignatureRequired(Boolean signatureRequired) {
        isSignatureRequired = signatureRequired;
    }

    public Boolean getDutiesAndTaxesByReceiver() {
        return dutiesAndTaxesByReceiver;
    }

    public void setDutiesAndTaxesByReceiver(Boolean dutiesAndTaxesByReceiver) {
        this.dutiesAndTaxesByReceiver = dutiesAndTaxesByReceiver;
    }

    public Boolean getPrintToPrinter() {
        return printToPrinter;
    }

    public void setPrintToPrinter(Boolean printToPrinter) {
        this.printToPrinter = printToPrinter;
    }

    public Boolean getIncludeLineDetails() {
        return includeLineDetails;
    }

    public void setIncludeLineDetails(Boolean includeLineDetails) {
        this.includeLineDetails = includeLineDetails;
    }

    public String getCostCentreName() {
        return costCentreName;
    }

    public void setCostCentreName(String costCentreName) {
        this.costCentreName = costCentreName;
    }

    public List<TaxIds> getTaxIds() {
        return taxIds;
    }

    public void setTaxIds(List<TaxIds> taxIds) {
        this.taxIds = taxIds;
    }

    public List<String> getOutputs() {
        return outputs;
    }

    public void setOutputs(List<String> outputs) {
        this.outputs = outputs;
    }
}
