
package com.nsy.api.tms.logistics.zhiyun;


import com.thoughtworks.xstream.annotations.XStreamImplicit;

import java.util.List;

public class Invoice {

    private String currencyCode;
    private String shipmentTerms;
    private String exportReason;
    private String insuranceCharges;
    private String freightCharges;
    @XStreamImplicit
    private List<InvoiceDetailList> invoiceDetailList;

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setShipmentTerms(String shipmentTerms) {
        this.shipmentTerms = shipmentTerms;
    }

    public String getShipmentTerms() {
        return shipmentTerms;
    }

    public void setExportReason(String exportReason) {
        this.exportReason = exportReason;
    }

    public String getExportReason() {
        return exportReason;
    }

    public void setInsuranceCharges(String insuranceCharges) {
        this.insuranceCharges = insuranceCharges;
    }

    public String getInsuranceCharges() {
        return insuranceCharges;
    }

    public void setFreightCharges(String freightCharges) {
        this.freightCharges = freightCharges;
    }

    public String getFreightCharges() {
        return freightCharges;
    }

    public List<InvoiceDetailList> getInvoiceDetailList() {
        return invoiceDetailList;
    }

    public void setInvoiceDetailList(List<InvoiceDetailList> invoiceDetailList) {
        this.invoiceDetailList = invoiceDetailList;
    }
}
