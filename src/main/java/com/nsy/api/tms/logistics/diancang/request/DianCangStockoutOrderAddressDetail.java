package com.nsy.api.tms.logistics.diancang.request;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 * @date 2024/4/7 10:31
 */
@XmlAccessorType(XmlAccessType.FIELD)
public class DianCangStockoutOrderAddressDetail {

    /**
     * 发件人姓名
     */
    @XmlElement(name = "ContactName")
    private String contactName;

    /**
     * 发件人公司
     */
    @XmlElement(name = "Company")
    private String company;

    /**
     * 发件人电话
     */
    @XmlElement(name = "Telephone")
    private String telephone;

    /**
     * 发件人手机
     */
    @XmlElement(name = "MobilePhone")
    private String mobilePhone;

    /**
     * 发件人邮箱
     */
    @XmlElement(name = "Email")
    private String email;

    /**
     * 州/省
     */
    @XmlElement(name = "Province")
    private String province;

    /**
     * 市
     */
    @XmlElement(name = "City")
    private String city;

    /**
     * 区
     */
    @XmlElement(name = "District")
    private String district;

    /**
     * 发件人街道地址
     */
    @XmlElement(name = "StreetAddress")
    private String streetAddress;

    /**
     * 发件人街道地址2
     */
    @XmlElement(name = "StreetAddress2")
    private String streetAddress2;

    /**
     * 发件人邮编
     */
    @XmlElement(name = "PostalCode")
    private String postalCode;

    /**
     * 发件人国家代码
     */
    @XmlElement(name = "CountryCode")
    private String countryCode;

    /**
     * 收件人身份证号码
     */
    @XmlElement(name = "IDNumber")
    private String idNumber;

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getStreetAddress() {
        return streetAddress;
    }

    public void setStreetAddress(String streetAddress) {
        this.streetAddress = streetAddress;
    }

    public String getStreetAddress2() {
        return streetAddress2;
    }

    public void setStreetAddress2(String streetAddress2) {
        this.streetAddress2 = streetAddress2;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getIdNumber() {
        return idNumber;
    }

    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber;
    }
}
