package com.nsy.api.tms.logistics.seko.response;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;


public class SekoResponse {
    @JsonProperty(value = "CarrierId")
    private Integer carrierId;
    @JsonProperty(value = "CarrierName")
    private String carrierName;
    @JsonProperty(value = "IsFreightForward")
    private Boolean isFreightForward;
    @JsonProperty(value = "IsOvernight")
    private Boolean isOvernight;
    @JsonProperty(value = "IsSaturdayDelivery")
    private Boolean isSaturdayDelivery;
    @JsonProperty(value = "IsRural")
    private Boolean isRural;
    @JsonProperty(value = "HasTrackPaks")
    private Boolean hasTrackPaks;
    @JsonProperty(value = "Message")
    private String message;
    @JsonProperty(value = "Errors")
    private List<ErrorMsg> errors;
    @JsonProperty(value = "SiteId")
    private Long siteId;
    @JsonProperty(value = "Consignments")
    private List<Consignments> consignments;
    @JsonProperty(value = "DestinationPort")
    private String destinationPort;
    @JsonProperty(value = "Downloads")
    private List<String> downloads;
    @JsonProperty(value = "CarrierType")
    private Integer carrierType;
    @JsonProperty(value = "AlertPath")
    private String alertPath;
    @JsonProperty(value = "Notifications")
    private List<String> notifications;
    @JsonProperty(value = "InvoiceResponse")
    private String invoiceResponse;
    @JsonProperty(value = "LogoPath")
    private String logoPath;

    public Integer getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Integer carrierId) {
        this.carrierId = carrierId;
    }

    public String getCarrierName() {
        return carrierName;
    }

    public void setCarrierName(String carrierName) {
        this.carrierName = carrierName;
    }

    public Boolean getFreightForward() {
        return isFreightForward;
    }

    public void setFreightForward(Boolean freightForward) {
        isFreightForward = freightForward;
    }

    public Boolean getOvernight() {
        return isOvernight;
    }

    public void setOvernight(Boolean overnight) {
        isOvernight = overnight;
    }

    public Boolean getSaturdayDelivery() {
        return isSaturdayDelivery;
    }

    public void setSaturdayDelivery(Boolean saturdayDelivery) {
        isSaturdayDelivery = saturdayDelivery;
    }

    public Boolean getRural() {
        return isRural;
    }

    public void setRural(Boolean rural) {
        isRural = rural;
    }

    public Boolean getHasTrackPaks() {
        return hasTrackPaks;
    }

    public void setHasTrackPaks(Boolean hasTrackPaks) {
        this.hasTrackPaks = hasTrackPaks;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<ErrorMsg> getErrors() {
        return errors;
    }

    public void setErrors(List<ErrorMsg> errors) {
        this.errors = errors;
    }

    public Long getSiteId() {
        return siteId;
    }

    public void setSiteId(Long siteId) {
        this.siteId = siteId;
    }

    public List<Consignments> getConsignments() {
        return consignments;
    }

    public void setConsignments(List<Consignments> consignments) {
        this.consignments = consignments;
    }

    public String getDestinationPort() {
        return destinationPort;
    }

    public void setDestinationPort(String destinationPort) {
        this.destinationPort = destinationPort;
    }

    public List<String> getDownloads() {
        return downloads;
    }

    public void setDownloads(List<String> downloads) {
        this.downloads = downloads;
    }

    public Integer getCarrierType() {
        return carrierType;
    }

    public void setCarrierType(Integer carrierType) {
        this.carrierType = carrierType;
    }

    public String getAlertPath() {
        return alertPath;
    }

    public void setAlertPath(String alertPath) {
        this.alertPath = alertPath;
    }

    public List<String> getNotifications() {
        return notifications;
    }

    public void setNotifications(List<String> notifications) {
        this.notifications = notifications;
    }

    public String getInvoiceResponse() {
        return invoiceResponse;
    }

    public void setInvoiceResponse(String invoiceResponse) {
        this.invoiceResponse = invoiceResponse;
    }

    public String getLogoPath() {
        return logoPath;
    }

    public void setLogoPath(String logoPath) {
        this.logoPath = logoPath;
    }
}
