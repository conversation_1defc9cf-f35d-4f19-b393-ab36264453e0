
package com.nsy.api.tms.logistics.hzups;

import javax.xml.ws.WebFault;


/**
 * This class was generated by Apache CXF 3.2.14
 * 2021-04-13T17:33:47.178+08:00
 * Generated source version: 3.2.14
 */

@WebFault(name = "Exception", targetNamespace = "http://service.eship.logisticstb/")
public class Exception_Exception extends java.lang.Exception {

    private com.nsy.api.tms.logistics.hzups.Exception exception;

    public Exception_Exception() {
        super();
    }

    public Exception_Exception(String message) {
        super(message);
    }

    public Exception_Exception(String message, java.lang.Throwable cause) {
        super(message, cause);
    }

    public Exception_Exception(String message, com.nsy.api.tms.logistics.hzups.Exception exception) {
        super(message);
        this.exception = exception;
    }

    public Exception_Exception(String message, com.nsy.api.tms.logistics.hzups.Exception exception, java.lang.Throwable cause) {
        super(message, cause);
        this.exception = exception;
    }

    public com.nsy.api.tms.logistics.hzups.Exception getFaultInfo() {
        return this.exception;
    }
}
