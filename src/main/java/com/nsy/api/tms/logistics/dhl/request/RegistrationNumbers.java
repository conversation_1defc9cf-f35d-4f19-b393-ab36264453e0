package com.nsy.api.tms.logistics.dhl.request;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * HXD
 * 2021/12/6
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "RegistrationNumbers")
public class RegistrationNumbers {

    @XmlElement(name = "RegistrationNumber")
    private List<RegistrationNumber> registrationNumber;

    public List<RegistrationNumber> getRegistrationNumber() {
        return registrationNumber;
    }

    public void setRegistrationNumber(List<RegistrationNumber> registrationNumber) {
        this.registrationNumber = registrationNumber;
    }
}
