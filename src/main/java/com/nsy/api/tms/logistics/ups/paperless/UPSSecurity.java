
package com.nsy.api.tms.logistics.ups.paperless;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.xml.bind.annotation.*;


/**
 *
 *
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="UsernameToken">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="Username" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="Password" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="ServiceAccessToken">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="AccessLicenseNumber" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
public class UPSSecurity {


    @JSONField(name = "UsernameToken")
    private UsernameToken usernameToken;

    @JSONField(name = "ServiceAccessToken")
    private ServiceAccessToken serviceAccessToken;

    /**
     *
     * @return possible object is
     * {@link UsernameToken }
     */
    public UsernameToken getUsernameToken() {
        return usernameToken;
    }

    /**
     *
     * @param value allowed object is
     *              {@link UsernameToken }
     */
    public void setUsernameToken(UsernameToken value) {
        this.usernameToken = value;
    }

    /**
     *
     * @return possible object is
     * {@link ServiceAccessToken }
     */
    public ServiceAccessToken getServiceAccessToken() {
        return serviceAccessToken;
    }

    /**
     *
     * @param value allowed object is
     *              {@link ServiceAccessToken }
     */
    public void setServiceAccessToken(ServiceAccessToken value) {
        this.serviceAccessToken = value;
    }


    /**
     *
     *
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="AccessLicenseNumber" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
            "accessLicenseNumber"
    })
    public static class ServiceAccessToken {

        @XmlElement(name = "AccessLicenseNumber", required = true)
        @JSONField(name = "AccessLicenseNumber")
        protected String accessLicenseNumber;

        /**
         *
         * @return possible object is
         * {@link String }
         */
        public String getAccessLicenseNumber() {
            return accessLicenseNumber;
        }

        /**
         *
         * @param value allowed object is
         *              {@link String }
         */
        public void setAccessLicenseNumber(String value) {
            this.accessLicenseNumber = value;
        }

    }


    /**
     *
     *
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="Username" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="Password" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     */
    public static class UsernameToken {

        @JSONField(name = "Username")
        protected String username;

        @JSONField(name = "Password")
        protected String password;

        /**
         *
         * @return possible object is
         * {@link String }
         */
        public String getUsername() {
            return username;
        }

        /**
         *
         * @param value allowed object is
         *              {@link String }
         */
        public void setUsername(String value) {
            this.username = value;
        }

        /**
         *
         * @return possible object is
         * {@link String }
         */
        public String getPassword() {
            return password;
        }

        /**
         *
         * @param value allowed object is
         *              {@link String }
         */
        public void setPassword(String value) {
            this.password = value;
        }

    }

}
