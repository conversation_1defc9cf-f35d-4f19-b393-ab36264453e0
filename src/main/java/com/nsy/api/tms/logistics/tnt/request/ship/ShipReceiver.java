package com.nsy.api.tms.logistics.tnt.request.ship;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/21 15:08
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "RECEIVER")
public class ShipReceiver {
    @XmlElement(name = "COMPANYNAME")
    private String companyName;
    @XmlElement(name = "STREETADDRESS1")
    private String streetAddress1;
    @XmlElement(name = "STREETADDRESS2")
    private String streetAddress2;
    @XmlElement(name = "STREETADDRESS3")
    private String streetAddress3;
    @XmlElement(name = "CITY")
    private String city;
    @XmlElement(name = "PROVINCE", required = true)
    private String province;
    @XmlElement(name = "POSTCODE", required = true)
    private String postCode;
    @XmlElement(name = "COUNTRY")
    private String country;
    @XmlElement(name = "CONTACTNAME")
    private String contactName;
    @XmlElement(name = "CONTACTDIALCODE")
    private String contactDialCode;
    @XmlElement(name = "CONTACTTELEPHONE")
    private String contactTelephone;


    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getStreetAddress1() {
        return streetAddress1;
    }

    public void setStreetAddress1(String streetAddress1) {
        this.streetAddress1 = streetAddress1;
    }

    public String getStreetAddress2() {
        return streetAddress2;
    }

    public void setStreetAddress2(String streetAddress2) {
        this.streetAddress2 = streetAddress2;
    }

    public String getStreetAddress3() {
        return streetAddress3;
    }

    public void setStreetAddress3(String streetAddress3) {
        this.streetAddress3 = streetAddress3;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactDialCode() {
        return contactDialCode;
    }

    public void setContactDialCode(String contactDialCode) {
        this.contactDialCode = contactDialCode;
    }

    public String getContactTelephone() {
        return contactTelephone;
    }

    public void setContactTelephone(String contactTelephone) {
        this.contactTelephone = contactTelephone;
    }

}
