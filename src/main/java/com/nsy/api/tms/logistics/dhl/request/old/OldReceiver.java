package com.nsy.api.tms.logistics.dhl.request.old;

import com.nsy.api.tms.logistics.dhl.request.Contact;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlAccessorType(XmlAccessType.NONE)
@XmlRootElement(name = "Consignee")
public class OldReceiver {
    @XmlElement(name = "CompanyName")
    private String companyName;
    @XmlElement(name = "AddressLine")
    private List<String> addressLineList;
    @XmlElement(name = "City")
    private String city;
    @XmlElement(name = "DivisionCode")
    private String divisionCode;
    @XmlElement(name = "PostalCode")
    private String postalCode;
    @XmlElement(name = "CountryCode")
    private String countryCode;
    @XmlElement(name = "CountryName")
    private String countryName;
    @XmlElement(name = "FederalTaxId")
    private String federalTaxId;
    @XmlElement(name = "Contact")
    private Contact contact;

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public List<String> getAddressLineList() {
        return addressLineList;
    }

    public void setAddressLineList(List<String> addressLineList) {
        this.addressLineList = addressLineList;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public String getFederalTaxId() {
        return federalTaxId;
    }

    public void setFederalTaxId(String federalTaxId) {
        this.federalTaxId = federalTaxId;
    }

    public Contact getContact() {
        return contact;
    }

    public void setContact(Contact contact) {
        this.contact = contact;
    }

    public String getDivisionCode() {
        return divisionCode;
    }

    public void setDivisionCode(String divisionCode) {
        this.divisionCode = divisionCode;
    }
}
