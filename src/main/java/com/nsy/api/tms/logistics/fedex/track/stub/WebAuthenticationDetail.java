/**
 * WebAuthenticationDetail.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.nsy.api.tms.logistics.fedex.track.stub;


/**
 * Used in authentication of the sender's identity.
 */
public class WebAuthenticationDetail  implements java.io.Serializable {
    /* Credential used to authenticate a specific software application.
     * This value is provided by FedEx after registration. */
    private WebAuthenticationCredential userCredential;

    public WebAuthenticationDetail() {
    }

    public WebAuthenticationDetail(
           WebAuthenticationCredential userCredential) {
           this.userCredential = userCredential;
    }


    /**
     * Gets the userCredential value for this WebAuthenticationDetail.
     * 
     * @return userCredential   * Credential used to authenticate a specific software application.
     * This value is provided by FedEx after registration.
     */
    public WebAuthenticationCredential getUserCredential() {
        return userCredential;
    }


    /**
     * Sets the userCredential value for this WebAuthenticationDetail.
     * 
     * @param userCredential   * Credential used to authenticate a specific software application.
     * This value is provided by FedEx after registration.
     */
    public void setUserCredential(WebAuthenticationCredential userCredential) {
        this.userCredential = userCredential;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof WebAuthenticationDetail)) return false;
        WebAuthenticationDetail other = (WebAuthenticationDetail) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.userCredential==null && other.getUserCredential()==null) || 
             (this.userCredential!=null &&
              this.userCredential.equals(other.getUserCredential())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getUserCredential() != null) {
            _hashCode += getUserCredential().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(WebAuthenticationDetail.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://fedex.com/ws/track/v9", "WebAuthenticationDetail"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("userCredential");
        elemField.setXmlName(new javax.xml.namespace.QName("http://fedex.com/ws/track/v9", "UserCredential"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://fedex.com/ws/track/v9", "WebAuthenticationCredential"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
