package com.nsy.api.tms.logistics.ups.track;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/18 12:56
 */
public class Status {
    @JsonProperty(value = "Type")
    private String type;
    @JsonProperty(value = "Code")
    private String code;
    @JsonProperty(value = "Description")
    private String description;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
