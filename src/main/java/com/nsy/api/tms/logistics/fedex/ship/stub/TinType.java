/**
 * TinType.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.nsy.api.tms.logistics.fedex.ship.stub;

public class TinType implements java.io.Serializable {
    private String _value_;
    private static java.util.HashMap _table_ = new java.util.HashMap();

    // Constructor
    protected TinType(String value) {
        _value_ = value;
        _table_.put(_value_,this);
    }

    public static final String _BUSINESS_NATIONAL = "BUSINESS_NATIONAL";
    public static final String _BUSINESS_STATE = "BUSINESS_STATE";
    public static final String _BUSINESS_UNION = "BUSINESS_UNION";
    public static final String _PERSONAL_NATIONAL = "PERSONAL_NATIONAL";
    public static final String _PERSONAL_STATE = "PERSONAL_STATE";
    public static final TinType BUSINESS_NATIONAL = new TinType(_BUSINESS_NATIONAL);
    public static final TinType BUSINESS_STATE = new TinType(_BUSINESS_STATE);
    public static final TinType BUSINESS_UNION = new TinType(_BUSINESS_UNION);
    public static final TinType PERSONAL_NATIONAL = new TinType(_PERSONAL_NATIONAL);
    public static final TinType PERSONAL_STATE = new TinType(_PERSONAL_STATE);
    public String getValue() { return _value_;}
    public static TinType fromValue(String value)
          throws IllegalArgumentException {
        TinType enumeration = (TinType)
            _table_.get(value);
        if (enumeration==null) throw new IllegalArgumentException();
        return enumeration;
    }
    public static TinType fromString(String value)
          throws IllegalArgumentException {
        return fromValue(value);
    }
    public boolean equals(Object obj) {return (obj == this);}
    public int hashCode() { return toString().hashCode();}
    public String toString() { return _value_;}
    public Object readResolve() throws java.io.ObjectStreamException { return fromValue(_value_);}
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumSerializer(
            _javaType, _xmlType);
    }
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumDeserializer(
            _javaType, _xmlType);
    }
    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(TinType.class);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://fedex.com/ws/ship/v26", "TinType"));
    }
    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

}
