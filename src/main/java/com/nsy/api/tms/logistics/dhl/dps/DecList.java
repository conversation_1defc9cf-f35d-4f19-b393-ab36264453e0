package com.nsy.api.tms.logistics.dhl.dps;

import java.math.BigDecimal;

public class DecList {

    private String ciqName;
    private String codeTs;
    private String contrItem;
    private String declGoodsEname;
    private Double declPrice;
    private Double declTotal;
    private String districtCode;
    private String dutyMode;
    private String exgNo;
    private String exgVersion;
    private BigDecimal firstQty;
    private String firstUnit;
    private String gModel;
    private String gName;
    private Integer gNo;
    private BigDecimal gQty;
    private String gUnit;
    private String goodsBrand;
    private String originCountry;
    private String secondQty;
    private String secondUnit;
    private String tradeCurr;
    private String workUsd;

    public void setCiqName(String ciqName) {
        this.ciqName = ciqName;
    }

    public String getCiqName() {
        return ciqName;
    }

    public void setCodeTs(String codeTs) {
        this.codeTs = codeTs;
    }

    public String getCodeTs() {
        return codeTs;
    }

    public void setContrItem(String contrItem) {
        this.contrItem = contrItem;
    }

    public String getContrItem() {
        return contrItem;
    }

    public void setDeclGoodsEname(String declGoodsEname) {
        this.declGoodsEname = declGoodsEname;
    }

    public String getDeclGoodsEname() {
        return declGoodsEname;
    }

    public void setDeclPrice(Double declPrice) {
        this.declPrice = declPrice;
    }

    public Double getDeclPrice() {
        return declPrice;
    }

    public void setDeclTotal(Double declTotal) {
        this.declTotal = declTotal;
    }

    public Double getDeclTotal() {
        return declTotal;
    }

    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
    }

    public String getDistrictCode() {
        return districtCode;
    }

    public void setDutyMode(String dutyMode) {
        this.dutyMode = dutyMode;
    }

    public String getDutyMode() {
        return dutyMode;
    }

    public void setExgNo(String exgNo) {
        this.exgNo = exgNo;
    }

    public String getExgNo() {
        return exgNo;
    }

    public void setExgVersion(String exgVersion) {
        this.exgVersion = exgVersion;
    }

    public String getExgVersion() {
        return exgVersion;
    }

    public BigDecimal getFirstQty() {
        return firstQty;
    }

    public void setFirstQty(BigDecimal firstQty) {
        this.firstQty = firstQty;
    }

    public void setFirstUnit(String firstUnit) {
        this.firstUnit = firstUnit;
    }

    public String getFirstUnit() {
        return firstUnit;
    }

    public void setGModel(String gModel) {
        this.gModel = gModel;
    }

    public String getGModel() {
        return gModel;
    }

    public void setGName(String gName) {
        this.gName = gName;
    }

    public String getGName() {
        return gName;
    }

    public Integer getgNo() {
        return gNo;
    }

    public void setgNo(Integer gNo) {
        this.gNo = gNo;
    }

    public BigDecimal getgQty() {
        return gQty;
    }

    public void setgQty(BigDecimal gQty) {
        this.gQty = gQty;
    }

    public void setGUnit(String gUnit) {
        this.gUnit = gUnit;
    }

    public String getGUnit() {
        return gUnit;
    }

    public void setGoodsBrand(String goodsBrand) {
        this.goodsBrand = goodsBrand;
    }

    public String getGoodsBrand() {
        return goodsBrand;
    }

    public void setOriginCountry(String originCountry) {
        this.originCountry = originCountry;
    }

    public String getOriginCountry() {
        return originCountry;
    }

    public void setSecondQty(String secondQty) {
        this.secondQty = secondQty;
    }

    public String getSecondQty() {
        return secondQty;
    }

    public void setSecondUnit(String secondUnit) {
        this.secondUnit = secondUnit;
    }

    public String getSecondUnit() {
        return secondUnit;
    }

    public void setTradeCurr(String tradeCurr) {
        this.tradeCurr = tradeCurr;
    }

    public String getTradeCurr() {
        return tradeCurr;
    }

    public void setWorkUsd(String workUsd) {
        this.workUsd = workUsd;
    }

    public String getWorkUsd() {
        return workUsd;
    }

}
