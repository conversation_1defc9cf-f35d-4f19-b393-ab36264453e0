package com.nsy.api.tms.controller.channel;

import com.nsy.api.tms.controller.BaseController;
import com.nsy.api.tms.controller.channel.request.ChannelTimelineConfigInsertRequest;
import com.nsy.api.tms.controller.channel.request.ChannelTimelineConfigPageRequest;
import com.nsy.api.tms.controller.channel.response.ChannelTimelineConfigResponse;
import com.nsy.api.tms.response.PageResponse;
import com.nsy.api.tms.service.ChannelTimelineConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 渠道时效配置表(ChannelTimelineConfig)接口
 *
 * <AUTHOR>
 * @since 2024-08-22 17:05:21
 */
@Api(tags = "渠道时效配置表(ChannelTimelineConfig)相关接口")
@RestController
public class ChannelTimelineConfigController extends BaseController {

    @Resource
    private ChannelTimelineConfigService channelTimelineConfigService;

    /**
     * 分页查询
     */
    @ApiOperation("首页page列表")
    @PostMapping("/channel-timeline-config/page")
    public PageResponse<ChannelTimelineConfigResponse> queryByPage(@RequestBody ChannelTimelineConfigPageRequest pageRequest) {
        return this.channelTimelineConfigService.queryByPage(pageRequest);
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("/channel-timeline-config/get/{id}")
    public ChannelTimelineConfigResponse queryById(@PathVariable("id") Integer id) {
        return this.channelTimelineConfigService.getOneById(id);
    }

    /**
     * 新增数据
     */
    @ApiOperation("新增数据")
    @PostMapping("/channel-timeline-config/insert")
    public void add(@RequestBody @Valid ChannelTimelineConfigInsertRequest channelTimelineConfig) {
        this.channelTimelineConfigService.insert(channelTimelineConfig);
    }

    /**
     * 编辑数据
     */
    @ApiOperation("编辑数据")
    @PostMapping("/channel-timeline-config/update")
    public void edit(@RequestBody @Valid ChannelTimelineConfigInsertRequest channelTimelineConfig) {
        this.channelTimelineConfigService.update(channelTimelineConfig);
    }

    /**
     * 删除数据
     */
    @DeleteMapping("/channel-timeline-config/delete/{id}")
    public void deleteById(@PathVariable("id") Integer id) {
        this.channelTimelineConfigService.removeById(id);
    }

}

