package com.nsy.api.tms.controller;


import com.nsy.api.tms.request.ForwarderPricePageRequest;
import com.nsy.api.tms.request.ForwarderPriceRequest;
import com.nsy.api.tms.request.GetForwarderPriceRequest;
import com.nsy.api.tms.response.ForwarderPriceResponse;
import com.nsy.api.tms.response.PageResponse;
import com.nsy.api.tms.service.ForwarderPriceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 头程运价业务报价 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@Api(tags = "头程运价业务报价-相关接口")
@RestController
@RequestMapping("/forwarder-price")
public class ForwarderPriceController {
    @Resource
    private ForwarderPriceService forwarderPriceService;

    @ApiOperation("列表")
    @PostMapping("/page")
    public PageResponse<ForwarderPriceResponse> queryByPage(@Valid @RequestBody ForwarderPricePageRequest request) {
        return forwarderPriceService.page(request);
    }

    @ApiOperation("发货运费信息")
    @PostMapping("/get-forwarder-price-list")
    public List<ForwarderPriceResponse> getForwarderPriceList(@Valid @RequestBody ForwarderPriceRequest request) {
        return forwarderPriceService.getForwarderPriceList(request);
    }

    @ApiOperation("发货运费信息")
    @PostMapping("/get-forwarder-price")
    public ForwarderPriceResponse getForwarderPrice(@Valid @RequestBody GetForwarderPriceRequest request) {
        return forwarderPriceService.getForwarderPrice(request);
    }

    @ApiOperation("通过地区获取头程运价物流方式")
    @GetMapping("/get-freight-modes-by-location/{location}")
    public List<String> getFreightModesByLocation(@PathVariable String location) {
        return forwarderPriceService.getFreightModesByLocation(location);
    }

}
