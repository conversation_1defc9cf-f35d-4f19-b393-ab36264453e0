package com.nsy.api.tms.controller.freight;

import com.nsy.api.tms.controller.BaseController;
import com.nsy.api.tms.request.freight.VpsChannelFreightBillInsertRequest;
import com.nsy.api.tms.request.freight.VpsChannelFreightBillPageRequest;
import com.nsy.api.tms.response.PageResponse;
import com.nsy.api.tms.response.freight.VpsChannelFreightBillResponse;
import com.nsy.api.tms.service.freight.VpsChannelFreightBillService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 渠道价格账单(VpsChannelFreightBill)接口
 *
 * <AUTHOR>
 * @since 2023-09-04 17:05:25
 */
@Api(tags = "渠道价格账单(VpsChannelFreightBill)相关接口")
@RestController
public class VpsChannelFreightBillController extends BaseController {

    @Resource
    private VpsChannelFreightBillService vpsChannelFreightBillService;

    /**
     * 分页查询
     */
    @ApiOperation("首页page列表")
    @PostMapping("/vps-channel-freight-bill/page")
    public PageResponse<VpsChannelFreightBillResponse> queryByPage(@RequestBody VpsChannelFreightBillPageRequest pageRequest) {
        return this.vpsChannelFreightBillService.queryByPage(pageRequest);
    }

    @ApiOperation("账单录入")
    @PostMapping("/vps-channel-freight-bill/insert")
    public void insert(@RequestBody VpsChannelFreightBillInsertRequest request) {
        this.vpsChannelFreightBillService.insert(request);
    }


}

