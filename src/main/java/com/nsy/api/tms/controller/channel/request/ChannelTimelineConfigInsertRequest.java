package com.nsy.api.tms.controller.channel.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024-08-22 17:05:22
 */
@ApiModel(value = "ChannelTimelineConfigInsertRequest", description = "新增request")
public class ChannelTimelineConfigInsertRequest {

    @ApiModelProperty("")
    private Integer id;

    @NotBlank
    @ApiModelProperty("location")
    private String location;

    // USW("美西"), USE("美东"), USM("美中"),
    private String destination;

    /**
     * 物流渠道id
     */
    @ApiModelProperty("物流渠道id")
    private Integer logisticsChannelId;

    /**
     * 市场
     */
    @ApiModelProperty("市场")
    @NotBlank
    private String marketplace;

    @ApiModelProperty("市场（中文）")
    @NotBlank
    private String marketplaceChinese;

    /**
     * 时效(天)
     */
    @ApiModelProperty("时效(天)")
    @NotNull
    private Integer timeline;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 更新者
     */
    @ApiModelProperty("更新者")
    private String updateBy;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private Long version;

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getLogisticsChannelId() {
        return logisticsChannelId;
    }

    public void setLogisticsChannelId(Integer logisticsChannelId) {
        this.logisticsChannelId = logisticsChannelId;
    }

    public String getMarketplace() {
        return marketplace;
    }

    public void setMarketplace(String marketplace) {
        this.marketplace = marketplace;
    }

    public String getMarketplaceChinese() {
        return marketplaceChinese;
    }

    public void setMarketplaceChinese(String marketplaceChinese) {
        this.marketplaceChinese = marketplaceChinese;
    }

    public Integer getTimeline() {
        return timeline;
    }

    public void setTimeline(Integer timeline) {
        this.timeline = timeline;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

}

