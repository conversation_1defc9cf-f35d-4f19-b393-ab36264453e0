package com.nsy.api.tms.controller;

import com.nsy.api.tms.domain.TmsPackage;
import com.nsy.api.tms.domain.TmsPackageDetailInfo;
import com.nsy.api.tms.domain.TmsPackageLogInfo;
import com.nsy.api.tms.domain.TmsPackageStatusCount;
import com.nsy.api.tms.request.PackageTagRequest;
import com.nsy.api.tms.request.PakcageUpdateSolutionRequest;
import com.nsy.api.tms.request.PakcageUpdateStatusRequest;
import com.nsy.api.tms.request.TmsPackageListRequest;
import com.nsy.api.tms.request.TmsPackageLogListRequest;
import com.nsy.api.tms.request.TmsPackagePannelRequest;
import com.nsy.api.tms.response.PageResponse;
import com.nsy.api.tms.service.PackageDetailService;
import com.nsy.api.tms.service.PackageNewService;
import com.nsy.api.tms.service.PackageService;
import com.nsy.api.tms.service.TmsPackageTagService;
import com.nsy.api.tms.service.TmsTimelinessAnalysisService;
import com.nsy.api.tms.service.privilege.AccessControlService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
public class TmsNewPackageController extends BaseController {


    @Autowired
    PackageDetailService packageDetailService;
    @Autowired
    TmsTimelinessAnalysisService timelinessAnalysisService;
    @Autowired
    PackageService packageService;
    @Autowired
    PackageNewService packageNewService;
    @Autowired
    TmsPackageTagService packageTagService;
    @Autowired
    AccessControlService accessControlService;

    @ApiOperation(value = "根据物流单号获取包裹详情", produces = "application/json")
    @GetMapping("/new/tms-package/detail/{logisticsNo}")
    public TmsPackageDetailInfo getPackageDetail(@PathVariable String logisticsNo, @RequestParam(required = false) Integer packageId) {
        return packageDetailService.getPackageDetail(logisticsNo, packageId);
    }

    @ApiOperation(value = "根据包裹id获取包裹详情", produces = "application/json")
    @GetMapping("/new/tms-package/{id}/detail")
    public TmsPackageDetailInfo getPackageDetail(@PathVariable Integer id) {
        return packageDetailService.getPackageDetailById(id);
    }

    @ApiOperation(value = "根据包裹列表", produces = "application/json")
    @PostMapping("/new/tms-package/list")
    public PageResponse<TmsPackage> findPackageList(@RequestBody TmsPackageListRequest request) {
        return packageNewService.findPackageList(request);
    }

    @ApiOperation(value = "更新包裹状态", produces = "application/json")
    @PostMapping("/new/tms-package/update-status")
    public void updateStatus(@RequestBody @Valid PakcageUpdateStatusRequest request) {
        packageNewService.updateStatus(request);
    }

    @ApiOperation(value = "解决方案", produces = "application/json")
    @PostMapping("/new/tms-package/update-solution")
    public void updateSolution(@RequestBody @Valid PakcageUpdateSolutionRequest request) {
        String realName = accessControlService.getRealName();
        packageNewService.updateSolution(request, realName);
    }

    @ApiOperation(value = "根据物流单号获取日志", produces = "application/json")
    @PostMapping("/new/tms-package/log/list")
    public PageResponse<TmsPackageLogInfo> getPackageLog(@RequestBody @Valid TmsPackageLogListRequest request) {
        return packageNewService.getPackageLog(request);
    }

    @ApiOperation(value = "异常面板统计", produces = "application/json")
    @PostMapping("/new/tms-package/panel-info")
    public List<TmsPackageStatusCount> panelInfo(@RequestBody @Valid TmsPackagePannelRequest request, @RequestHeader("location") String location) {
        return packageNewService.panelInfo(request, location);
    }


    @ApiOperation(value = "手动同步trackingMore", produces = "application/json")
    @PostMapping("/new/tms-package/push-tracking-more")
    public void pushTrackingMore(@RequestBody @Valid List<String> logisticsNoList) {
        packageNewService.pushTrackingMoreManual(logisticsNoList);
    }

    @ApiOperation(value = "包裹打标", produces = "application/json")
    @PostMapping("/new/tms-package/package-update-tag")
    public void updateTag(@RequestBody @Valid PackageTagRequest request) {
        String realName = accessControlService.getRealName();
        packageTagService.updateTag(request, realName);
    }

}
