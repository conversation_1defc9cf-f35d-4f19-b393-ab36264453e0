package com.nsy.api.tms.controller;

import com.nsy.api.tms.domain.LogisticsChannelConfig;
import com.nsy.api.tms.domain.TmsConfig;
import com.nsy.api.tms.request.LogisticsChannelConfigRequest;
import com.nsy.api.tms.request.TmsConfigGroupRequest;
import com.nsy.api.tms.request.TmsConfigRequest;
import com.nsy.api.tms.response.base.ApiResponse;
import com.nsy.api.tms.response.base.BaseListResponse;
import com.nsy.api.tms.service.TmsConfigGroupService;
import com.nsy.api.tms.service.TmsConfigService;
import com.nsy.api.tms.service.TmsLogisticsChannelConfigService;
import com.nsy.api.tms.service.external.refactor.FedexUploadLogoAndSignatureNewService;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.inject.Inject;

@RestController
public class TmsConfigController extends BaseController {
    @Inject
    TmsConfigService tmsConfigService;
    @Inject
    TmsConfigGroupService tmsConfigGroupService;
    @Inject
    TmsLogisticsChannelConfigService tmsLogisticsChannelConfigService;
    @Inject
    FedexUploadLogoAndSignatureNewService fedexUploadLogoAndSignatureService;

    @RequestMapping(value = "/config/list", method = RequestMethod.POST)
    public BaseListResponse<TmsConfig> getTmsConfig(@RequestBody TmsConfigRequest request) {
        return tmsConfigService.findConfigByCondition(request);
    }

    @RequestMapping(value = "/config/group/list", method = RequestMethod.GET)
    public BaseListResponse<String> getTmsAllGroup() {
        return tmsConfigGroupService.getAllGroup();
    }

    @RequestMapping(value = "/config-group/add", method = RequestMethod.POST)
    public ApiResponse addConfigGroup(@RequestBody TmsConfigGroupRequest request) {
        return tmsConfigGroupService.addConfigGroup(request);
    }

    @RequestMapping(value = "/config-group/delete/{id}", method = RequestMethod.DELETE)
    public void deleteConfigGroup(@PathVariable Integer id) {
        tmsConfigGroupService.deleteConfigGroup(id);
    }

    @RequestMapping(value = "/config/add", method = RequestMethod.POST)
    public ApiResponse addConfig(@RequestBody TmsConfigRequest request) {
        return tmsConfigService.addConfig(request);
    }

    @RequestMapping(value = "/config/modify", method = RequestMethod.POST)
    public ApiResponse getConfig(@RequestBody TmsConfigRequest request) {
        return tmsConfigService.modifyConfig(request);
    }

    @RequestMapping(value = "/config/delete", method = RequestMethod.POST)
    public ApiResponse getConfig(@RequestBody Integer id) {
        return tmsConfigService.deleteConfig(id);
    }

    @RequestMapping(value = "/config/delete/{id}", method = RequestMethod.DELETE)
    public ApiResponse deleteConfig(@PathVariable Integer id) {
        return tmsConfigService.deleteConfig(id);
    }

    @RequestMapping(value = "/config-channel/list", method = RequestMethod.POST)
    public BaseListResponse<LogisticsChannelConfig> getTmsChannelConfig(@RequestBody LogisticsChannelConfigRequest request) {
        return tmsLogisticsChannelConfigService.findLogisticsChannelConfigByCondition(request);
    }

    @RequestMapping(value = "/config-channel/detail/{id}", method = RequestMethod.GET)
    public LogisticsChannelConfig getTmsChannelConfigDetail(@PathVariable Integer id) {
        return tmsLogisticsChannelConfigService.findLogisticsChannelConfig(id);
    }

    @RequestMapping(value = "/config-channel/update", method = RequestMethod.POST)
    public ApiResponse getChannelConfig(@RequestBody LogisticsChannelConfigRequest request) {
        return tmsLogisticsChannelConfigService.modifyChannelConfig(request);
    }

    @RequestMapping(value = "/config-channel/add", method = RequestMethod.POST)
    public ApiResponse addChannelConfig(@RequestBody LogisticsChannelConfigRequest request) {
        return tmsLogisticsChannelConfigService.addChannelConfig(request);
    }

    @RequestMapping(value = "/upload-logo-signature/{accountId}/{logisticsCompany}", method = RequestMethod.GET)
    public ApiResponse addChannelConfig(@PathVariable String accountId, @PathVariable String logisticsCompany) {
        return fedexUploadLogoAndSignatureService.uploadLogoAndSignature(accountId, logisticsCompany);
    }

}
