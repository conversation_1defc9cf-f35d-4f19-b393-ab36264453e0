package com.nsy.api.tms.controller.freight;

import com.nsy.api.tms.controller.BaseController;
import com.nsy.api.tms.response.freight.HomePageInfoResponse;
import com.nsy.api.tms.service.freight.VpsHomePageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * HXD
 * 2023/9/11
 **/
@Api(tags = "Vps首页面板")
@RestController
public class VpsHomePageController extends BaseController {

    @Autowired
    VpsHomePageService homePageService;

    /**
     * 首页面板数据
     */
    @ApiOperation("vps首页面板-待办事项")
    @GetMapping("/vps-home/info/to-do-list")
    public HomePageInfoResponse toDoListInfo() {
        return this.homePageService.toDoListInfo();
    }
}
