package com.nsy.api.tms.controller;

import com.nsy.api.tms.filter.TenantContext;
import com.nsy.api.tms.request.freight.VpsChannelFreightInsertRequest;
import com.nsy.api.tms.request.upload.UploadInternationalExpressRequest;
import com.nsy.api.tms.request.upload.UploadRequest;
import com.nsy.api.tms.response.upload.UploadResponse;
import com.nsy.api.tms.service.freight.VpsChannelCountryFreightService;
import com.nsy.api.tms.service.upload.UploadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "上传相关接口")
@RestController
public class UploadController extends BaseController {

    @Autowired
    private UploadService uploadService;
    @Autowired
    private VpsChannelCountryFreightService channelCountryFreightService;

    @ApiOperation(value = "上传请求转发", notes = "上传请求转发", produces = "application/json")
    @RequestMapping(value = "/upload/distribution", method = RequestMethod.POST)
    public UploadResponse distribution(@RequestBody UploadRequest request) {
        // TODO: 统一设置location
        TenantContext.setTenant(request.getLocation());
        return uploadService.distribution(request);
    }

    @ApiOperation(value = "tms上传国际物流价格", notes = "tms上传国际物流价格", produces = "application/json")
    @PostMapping(value = "/upload/international-express-price")
    public VpsChannelFreightInsertRequest uploadInternationalExpressPrice(@RequestBody UploadInternationalExpressRequest request) {
        return channelCountryFreightService.uploadInternationalExpressPrice(request);
    }
}
