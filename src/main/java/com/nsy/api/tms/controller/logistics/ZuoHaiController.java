package com.nsy.api.tms.controller.logistics;

import com.nsy.api.tms.controller.BaseController;
import com.nsy.api.tms.logistics.zuohai.ReplenishmentProduct;
import com.nsy.api.tms.logistics.zuohai.StockinOrderProduct;
import com.nsy.api.tms.service.external.refactor.YijingtongService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 左海
 * <AUTHOR>
 * 2024-01-23
 */
@RestController
public class ZuoHaiController extends BaseController {

    @Autowired
    private YijingtongService yijingtongService;

    @ApiOperation(value = "创建左海入库单", produces = "application/json")
    @RequestMapping(value = "/zuo-hai/create-stock-in-order", method = RequestMethod.POST)
    public void createStockInOrder(@RequestBody List<ReplenishmentProduct> request) {
        yijingtongService.createStockInOrder(request);
    }

    @ApiOperation(value = "查询左海入库单", produces = "application/json")
    @RequestMapping(value = "/zuo-hai/stock-in-order/{zuohaiStockInOrderNo}/{spaceName}", method = RequestMethod.GET)
    public List<StockinOrderProduct> createStockInOrder(@PathVariable(value = "zuohaiStockInOrderNo") String zuohaiStockInOrderNo, @PathVariable(value = "spaceName") String spaceName) {
        return yijingtongService.getStockInOrderInfo(zuohaiStockInOrderNo, spaceName);
    }

}
