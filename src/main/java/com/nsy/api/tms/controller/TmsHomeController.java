package com.nsy.api.tms.controller;

import com.nsy.api.tms.domain.TmsTaskCollectInfo;
import com.nsy.api.tms.request.TmsTaskCollectRequest;
import com.nsy.api.tms.service.TmsAlertTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class TmsHomeController {

    @Autowired
    private TmsAlertTaskService tmsAlertTaskService;

    @RequestMapping(value = "/task/statistics", method = RequestMethod.POST)
    public List<TmsTaskCollectInfo> getAlterTask(@RequestBody TmsTaskCollectRequest request) {
        return tmsAlertTaskService.collectTmsTask(request);
    }

    @GetMapping("/task/overDate-taskCount")
    public List<TmsTaskCollectInfo> getOverDateCount() {
        return tmsAlertTaskService.collectOverDate();
    }

}
