package com.nsy.api.tms;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.nsy.api.tms.enumeration.LocationEnum;
import com.nsy.api.tms.filter.TenantContext;
import com.nsy.permission.IgnoreLocationContext;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import org.apache.commons.lang3.BooleanUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Configuration
@MapperScan("com.nsy.api.tms.mapper")
public class MybatisPlusConfig {

    private static final Logger LOGGER = LoggerFactory.getLogger(MybatisPlusConfig.class);
    
    /**
     * 多租户标识
     */
    private static final String SYSTEM_TENANT_ID = "location";


    /**
     * 多租户表，只有以下表开启多租户
     */
    private static final List<String> TARGET_TENANT_TABLE = new ArrayList<>();

    static {
        TARGET_TENANT_TABLE.add("package_bill");
    }


    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();
        mybatisPlusInterceptor.addInnerInterceptor(new TenantLineInnerInterceptor(new MyTenantLineHandler()));
        mybatisPlusInterceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        mybatisPlusInterceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        return mybatisPlusInterceptor;
    }

    protected static final class MyTenantLineHandler implements TenantLineHandler {

        @Override
        public Expression getTenantId() {
            String tenantValue = TenantContext.getTenant();
            return new StringValue(Optional.ofNullable(tenantValue).orElse(LocationEnum.QUANZHOU.name()));
        }

        @Override
        public String getTenantIdColumn() {
            // 对应数据库租户ID的列名，是数据库列名，不是实体类
            return SYSTEM_TENANT_ID;
        }

        @Override
        public boolean ignoreTable(String tableName) {
            if (BooleanUtils.isTrue(IgnoreLocationContext.getIgnoreLocation())) {
                LOGGER.debug("执行IgnoreTenantAspect, 忽略租户拦截查询");
                return true;
            } 
            return !TARGET_TENANT_TABLE.contains(tableName) && !TenantContext.isDisableTenant();
        }
    }
}
