package com.nsy.api.tms.constants;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class TmsCommonConstant {
    public static List<String> allFangYiCate() {
        List<String> fangyiCateOfFourPx = new ArrayList<>(FANGYI_CATE_FOURPX);
        fangyiCateOfFourPx.addAll(FANGYI_CATE);
        return fangyiCateOfFourPx;
    }

    // Edl物流
    public static final List<String> EDL_LOGISTICS = Collections.unmodifiableList(Arrays.asList(LogisticsChannelConstant.EDL_EXPRESS,
            LogisticsChannelConstant.EDL_EXPRESS_W, LogisticsChannelConstant.USPS_US, LogisticsChannelConstant.EDL_HUAMEI,
            LogisticsChannelConstant.EDL_ESUBAO, LogisticsChannelConstant.EDL_WNBAA_XIAOBAO));

    // 菜鸟物流
    public static final List<String> CAI_NIAO_LOGISTICS = Collections.unmodifiableList(Arrays.asList(LogisticsChannelConstant.YUAN_TONG, LogisticsChannelConstant.YUN_DA, LogisticsChannelConstant.EMS,
            LogisticsChannelConstant.SF, LogisticsChannelConstant.YOU_SU, LogisticsChannelConstant.SU_ER,
            LogisticsChannelConstant.KUA_YUE_SU_YUN, LogisticsChannelConstant.ZTO));

    public static final List<String> FANGYI_CATE_FOURPX = Collections.unmodifiableList(Arrays.asList("口罩", "儿童口罩", "布口罩", "成人布口罩", "儿童布口罩", "额温枪", "一次性手套", "消毒液", "洗手液", "湿纸巾", "体温计", "护目镜", "防护服"));

    public static final List<String> FANGYI_CATE = Collections.unmodifiableList(Arrays.asList("加湿雾化器", "手指脉博测量仪", "紫外线灯"));

    // 需要在单证 或者 外部才能获取面单的物流
    public static final List<String> FILTER_LOGISTICS = Collections.unmodifiableList(Arrays.asList("FedEx", "DHL", "UPS 杭州", "UPS 快捷", "速尔快递", "信丰快递", "信丰物流", "中通快运"));

    // 小诸
    public static final String XZ = "XZ";

    // 原飞航
    public static final String YFH = "YFH";

    // 杰瑞达
    public static final String JRD = "JRD";

    // 小诸UPS
    public static final String XZUPS = "XZUPS";

    public static final List<String> FBA_BASE_LOGISTICS = Collections.unmodifiableList(Arrays.asList("海运", "空运", "陆运", "铁运"));

    // 装箱清单代发货数据
    public static final List<String> INSTEAD_SHIP_COMPANY = Collections.unmodifiableList(Arrays.asList(XZ, YFH, JRD, XZUPS));

    // 特殊处理的物流
    public static final List<String> DEAL_LOGISTICS = Collections.unmodifiableList(Arrays.asList("联邮通美国普货专线", "云途特惠普货", "CneExpress",
            "顺丰-欧洲小包", "云途特惠普货(广州)", "4PX递四方(厦门泉州仓)", "CneExpress(厦门泉州仓)", "云途(厦门泉州仓)"));

    // 特殊需要生成账单的物流
    public static final List<String> BILL_LOGISTICS = Collections.unmodifiableList(Arrays.asList("跨越速运", "顺丰", "韵达快递", "信丰物流"));

    // 海外仓平台
    public static final String CANGSOUSOU_SPACE_SKU_MAPPING = "CANGSOUSOU";

    public static final String WINIT_SPACE_SKU_MAPPING = "WINIT";
}
