package com.nsy.api.tms.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * HXD
 * 2022/9/26
 **/

public class TrackingServicesConstant {
    private static final Map<String, String> COURIER_CODE_MAP = new HashMap<>();

//    LogisticsMethodEnum
    static {
        COURIER_CODE_MAP.put("顺丰国际", "sfb2c");
        COURIER_CODE_MAP.put("DHL", "dhl");
        COURIER_CODE_MAP.put("FedEx", "fedex");
        COURIER_CODE_MAP.put("FEDEX", "fedex");
        COURIER_CODE_MAP.put("E邮宝", "epacket");
        COURIER_CODE_MAP.put("EDL", "1dlexpress");
        COURIER_CODE_MAP.put("CNE", "cnexps");
        COURIER_CODE_MAP.put("UPS", "ups");
        COURIER_CODE_MAP.put("TNT", "tnt");
        COURIER_CODE_MAP.put("4PX", "4px");
        COURIER_CODE_MAP.put("AliExpress", "cainiao");
        COURIER_CODE_MAP.put("菜鸟物流", "cainiao");
        COURIER_CODE_MAP.put("PFCExpress", "pfcexpress");
        // 无法找到JYEXPRESS，默认JYEXPRESS
        COURIER_CODE_MAP.put("JYEXPRESS", "JYExpress");
        // JETPLUS，JETPLUS
        COURIER_CODE_MAP.put("JETPLUS", "JETPLUS");
        COURIER_CODE_MAP.put("邮政小包", "china-post");
        COURIER_CODE_MAP.put("云途", "yunexpress");
        COURIER_CODE_MAP.put("义达", "ydhex");
        COURIER_CODE_MAP.put("金麦", "jcex");
        COURIER_CODE_MAP.put("鑫宇隆", "4px");
        COURIER_CODE_MAP.put("佳成", "jcex");
        COURIER_CODE_MAP.put("Seko", "Seko");
        COURIER_CODE_MAP.put("原飞航", "yfhex");
        // 菜鸟单独处理成对应的公司
        COURIER_CODE_MAP.put("优速快递", "uc-express");
        COURIER_CODE_MAP.put("圆通快递", "yto");
        COURIER_CODE_MAP.put("速尔快递", "sure56");
        COURIER_CODE_MAP.put("极兔速递", "jtexpress");
        COURIER_CODE_MAP.put("韵达快递", "yunda");
        COURIER_CODE_MAP.put("顺丰", "sf-express");
        COURIER_CODE_MAP.put("EMS", "china-ems");
        COURIER_CODE_MAP.put("中通快递", "zto");
        COURIER_CODE_MAP.put("跨越速运", "kye");
        COURIER_CODE_MAP.put("燕文", "yanwen");
    }

    public static Map<String, String> getCourierCodeMap() {
        return COURIER_CODE_MAP;
    }
}
