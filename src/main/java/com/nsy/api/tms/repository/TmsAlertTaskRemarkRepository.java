package com.nsy.api.tms.repository;

import com.nsy.api.tms.dao.entity.TmsAlertTaskRemarkEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0
 */
public interface TmsAlertTaskRemarkRepository extends JpaRepository<TmsAlertTaskRemarkEntity, Integer>, JpaSpecificationExecutor<TmsAlertTaskRemarkEntity> {
    List<TmsAlertTaskRemarkEntity> findByTaskIdOrderByCreateDateDesc(Integer taskId);
}
