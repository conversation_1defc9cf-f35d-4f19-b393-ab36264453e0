package com.nsy.api.tms.repository;

import com.nsy.api.tms.dao.entity.TmsConfigEntity;
import com.nsy.api.tms.dao.entity.TmsThirdPartTokenEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public interface TmsThirdPartTokenRepository extends JpaRepository<TmsThirdPartTokenEntity, Integer>, JpaSpecificationExecutor<TmsConfigEntity> {

    List<TmsThirdPartTokenEntity> findByPlatform(String platform);
}
