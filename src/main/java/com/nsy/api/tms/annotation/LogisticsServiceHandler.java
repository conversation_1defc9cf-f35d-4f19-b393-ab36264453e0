package com.nsy.api.tms.annotation;

import com.nsy.api.tms.enumeration.LogisticsMethodEnum;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * User: sjp
 * Date: 2019/12/17
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface LogisticsServiceHandler {
    LogisticsMethodEnum logisticsMethod();
}
