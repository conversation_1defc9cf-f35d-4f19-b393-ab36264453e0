package com.nsy.api.tms.job;

import com.nsy.api.tms.enumeration.TrackType;
import com.nsy.api.tms.job.base.BaseJob;
import com.nsy.api.tms.service.TmsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.inject.Inject;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * 物流追踪--17 track
 */
@Service
public class TmsTrackWithTrackingMoreJob extends BaseJob {

    @Inject
    TmsService tmsService;

    private static final Logger LOGGER = LoggerFactory.getLogger(TmsTrackWithTrackingMoreJob.class);

    @Override
    public void run(Map<String, Object> objects) throws Exception {
        LOGGER.info("TmsTrackWithTrackingMoreJob start");
        tmsService.track(TrackType.TRACKING_MORE);
        LOGGER.info("TmsTrackWithTrackingMoreJob end");
    }
}
