package com.nsy.api.tms.job;

import com.nsy.api.core.apicore.exception.NoneDataException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.dao.entity.TmsAsyncRequestQueueEntity;
import com.nsy.api.tms.job.base.BaseJob;
import com.nsy.api.tms.request.OrderRequest;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.service.LogisticsHelper;
import com.nsy.api.tms.service.TmsAsyncRequestQueueService;
import com.nsy.api.tms.service.external.BaseLogisticsService;
import com.nsy.api.tms.utils.JsonMapper;
import org.springframework.stereotype.Service;

import javax.inject.Inject;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class AsyncGenerateOrderJob extends BaseJob {

    @Inject
    TmsAsyncRequestQueueService asyncRequestQueueService;

    @Inject
    LogisticsHelper logisticsHelper;

    @Override
    public void run(Map<String, Object> objects) throws Exception {
        List<TmsAsyncRequestQueueEntity> asyncRequestQueueEntityList = asyncRequestQueueService.findToProcessQueue();
        if (asyncRequestQueueEntityList.isEmpty()) {
            throw new NoneDataException();
        }
        asyncRequestQueueEntityList.stream().forEach((entity) -> {
            try {
                OrderRequest orderRequest = JsonMapper.fromJson(entity.getOrderRequestContent(), OrderRequest.class);
                Map<String, String> configMap = JsonMapper.fromJson(entity.getConfigMap(), Map.class);
                String requestXml = entity.getRequestContent();
                entity.setRetryCount(entity.getRetryCount() + 1);
                BaseLogisticsService logisticsService = logisticsHelper.getLogisticsServiceByLogisticsMethod(orderRequest.getOrderInfo().getLogisticsMethod(),
                        orderRequest.getOrderInfo().getLogisticsChannelCode());
                GenerateOrderResponse orderResponse = logisticsService.doRequest(orderRequest, configMap, requestXml);
                updateAsyncRequestQueue(entity, orderResponse);
            } catch (Exception exception) {
                errorCallback(entity, exception.getMessage());
            }
        });
    }

    private void errorCallback(TmsAsyncRequestQueueEntity entity, String exceptionMsg) {
        entity.setResponseContent(exceptionMsg);
        entity.setStatus(-1);
        asyncRequestQueueService.persist(entity);
    }

    private void updateAsyncRequestQueue(TmsAsyncRequestQueueEntity entity, GenerateOrderResponse orderResponse) {
        entity.setResponseContent(JsonMapper.toJson(orderResponse));
        if (Objects.nonNull(orderResponse.getSuccessEntity()) && StringUtils.hasText(orderResponse.getSuccessEntity().getLogisticsNo())) {
            entity.setStatus(1);
            entity.setLogisticsNo(orderResponse.getSuccessEntity().getLogisticsNo());
        } else {
            entity.setStatus(-1);
        }
        entity.setResponseContent(JsonMapper.toJson(orderResponse));
        asyncRequestQueueService.persist(entity);
    }
}
