package com.nsy.api.tms.job;

import com.nsy.api.tms.job.base.BaseJob;
import com.nsy.api.tms.service.TmsRequestLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class TmsClearUpRequestLogJob extends BaseJob {
    private static final Logger LOGGER = LoggerFactory.getLogger(TmsClearUpRequestLogJob.class);

    @Autowired
    private TmsRequestLogService tmsRequestLogService;
    @Override
    public void run(Map<String, Object> objects) throws Exception {
        LOGGER.info("TmsClearUpRequestLogJob begin");
        int result = tmsRequestLogService.clearUpRequestLog();
        LOGGER.info("删除log记录条数：{}", result);
        LOGGER.info("TmsClearUpRequestLogJob end");
    }
}
