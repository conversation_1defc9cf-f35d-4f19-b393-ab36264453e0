package com.nsy.api.tms.job;

import com.nsy.api.tms.job.base.BaseJob;
import com.nsy.api.tms.service.TmsTimelinessAnalysisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.inject.Inject;
import java.util.Map;

/**
 * @author: HUANG Tao
 * @date: 2020/2/27
 */
@Service
public class TmsTimelinessAnalysisJob extends BaseJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(TmsTimelinessAnalysisJob.class);

    @Inject
    TmsTimelinessAnalysisService analysisService;

    @Override
    public void run(Map<String, Object> objects) throws Exception {
        LOGGER.info("TmsTimelinessAnalysisJob begin");
        analysisService.saveTimelinessAnalysisList(objects);
        LOGGER.info("TmsTimelinessAnalysisJob end");
    }
}
