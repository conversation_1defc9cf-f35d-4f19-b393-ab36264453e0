package com.nsy.api.tms.job.definition;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * <AUTHOR> 8/27/2015
 */
@XmlRootElement(name = "job")
@XmlAccessorType(XmlAccessType.FIELD)
public class JobDefinition implements Serializable {
    private static final long serialVersionUID = 7812252778206310883L;
    @XmlAttribute(name = "name")
    private String jobName;
    @XmlElement(name = "type")
    private String jobType;
    @XmlElement(name = "secondly")
    private SecondlyDefinition secondly;
    @XmlElement(name = "minutely")
    private MinutelyDefinition minutely;
    @XmlElement(name = "hourly")
    private HourlyDefinition hourly;
    @XmlElement(name = "cron")
    private CronDefinition cron;
    @XmlElement(name = "email-from")
    private String emailFrom;
    @XmlElement(name = "email-to")
    private String emailTo;
    @XmlElement(name = "description")
    private String description;
    @XmlElement(name = "param1")
    private String param1;
    @XmlElement(name = "param2")
    private String param2;

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getJobType() {
        return jobType;
    }

    public void setJobType(String jobType) {
        this.jobType = jobType;
    }

    public SecondlyDefinition getSecondly() {
        return secondly;
    }

    public void setSecondly(SecondlyDefinition secondly) {
        this.secondly = secondly;
    }

    public MinutelyDefinition getMinutely() {
        return minutely;
    }

    public void setMinutely(MinutelyDefinition minutely) {
        this.minutely = minutely;
    }

    public HourlyDefinition getHourly() {
        return hourly;
    }

    public void setHourly(HourlyDefinition hourly) {
        this.hourly = hourly;
    }

    public CronDefinition getCron() {
        return cron;
    }

    public void setCron(CronDefinition cron) {
        this.cron = cron;
    }

    public String getEmailFrom() {
        return emailFrom;
    }

    public void setEmailFrom(String emailFrom) {
        this.emailFrom = emailFrom;
    }

    public String getEmailTo() {
        return emailTo;
    }

    public void setEmailTo(String emailTo) {
        this.emailTo = emailTo;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getParam1() {
        return param1;
    }

    public void setParam1(String param1) {
        this.param1 = param1;
    }

    public String getParam2() {
        return param2;
    }

    public void setParam2(String param2) {
        this.param2 = param2;
    }
}
