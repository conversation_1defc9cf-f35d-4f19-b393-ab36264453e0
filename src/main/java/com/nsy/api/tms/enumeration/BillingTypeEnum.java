package com.nsy.api.tms.enumeration;

import com.nsy.api.core.apicore.util.StringUtils;

import java.util.Arrays;

/**
 * 产品说  渠道的计费方式 和 国家的计费方式不一样，所以拆开成两个枚举
 * <AUTHOR>
 * 2023-11-21
 */
public enum BillingTypeEnum {
    INITIAL_ADDITIONAL_WEIGHT("首重续重"),
    UNIT_PRICE_WEIGHT("单价*重量"),
    RANGE_WEIGHT("固定费用");

    String desc;

    BillingTypeEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
    public static String getDescByName(String name) {
        BillingTypeEnum valuationMethodEnum = Arrays.stream(BillingTypeEnum.values())
                .filter((e) -> e.name().equals(name))
                .findFirst()
                .orElse(null);
        return valuationMethodEnum == null ? "" : valuationMethodEnum.getDesc();
    }

    public static String getNameByDesc(String desc) {
        if (!StringUtils.hasText(desc)) {
            return "";
        }
        BillingTypeEnum valuationMethodEnum = Arrays.stream(BillingTypeEnum.values())
                .filter((e) -> e.getDesc().equals(desc))
                .findFirst()
                .orElse(null);
        return valuationMethodEnum == null ? "" : valuationMethodEnum.name();
    }
}
