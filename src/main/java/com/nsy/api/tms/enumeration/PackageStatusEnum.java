package com.nsy.api.tms.enumeration;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public enum PackageStatusEnum {
    CREATED("创建"),
    SHIPPED("发货"),
    INFO_RECEIVED("待上网"),
    TAKEN("已收"),
    TRANSIT("在途"),
    DELIVERED("妥投"),
    EXPIRED("运输过久"),
    EXCEPTION("可能异常"),
    PROCESSED("已处理"),
    LOST("丢件"),
    CANCEL("运单取消"),
    DELIVERED_FAILED("投递失败"),
    PENDING_PICKUP("到达待取"),
    EXCEPTION_RESOLVE("异常已处理"),
    CLAIM("索赔"),
    RETURN("退件"),
    DESTROY("销毁"),
    INVESTIGATE("开查"),
    UNABLE_CLAIM("无法理赔");

    private String desc;


    public static final List<String> NEED_TRACK_STATUS_LIST = Arrays.asList(SHIPPED.getDesc(), TAKEN.getDesc(),
        TRANSIT.getDesc(),
        PENDING_PICKUP.getDesc());

    public static final List<String> EXCEPTION_STATUS_LIST = Arrays.asList(EXCEPTION.getDesc(), EXPIRED.getDesc(),
            DELIVERED_FAILED.getDesc(),
            PENDING_PICKUP.getDesc());

    // 处理结果
    public static final List<String> ORDER_DEAL_LIST = Arrays.asList(PROCESSED.getDesc(), EXCEPTION_RESOLVE.getDesc(),
            CLAIM.getDesc(), DESTROY.getDesc(), INVESTIGATE.getDesc(), UNABLE_CLAIM.getDesc());


    public static String[] getPackageStatusArray() {
        List<String> packageStatusList = new ArrayList<>();
        for (PackageStatusEnum packageStatusEnum : PackageStatusEnum.values()) {
            packageStatusList.add(packageStatusEnum.getDesc());
        }
        return packageStatusList.toArray(new String[packageStatusList.size()]);
    }

    public String getDesc() {
        return desc;
    }

    PackageStatusEnum(String desc) {
        this.desc = desc;
    }

    public static String resolve(String name) {
        return Arrays.asList(PackageStatusEnum.values()).stream()
            .filter((v) -> v.getDesc().equals(name))
            .findFirst()
            .map(p -> p.getDesc())
            .orElse(null);
    }
}
