package com.nsy.api.tms.enumeration;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/8/25 10:38
 */
public enum NoticeTargetTypeEnum {

    LOGISTIC("LOGISTIC", "物流供应商");

    private final String code;
    private final String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    NoticeTargetTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static String getDescByCode(String code) {
        return Arrays.stream(values()).filter(item -> item.getCode().equals(code)).map(NoticeTargetTypeEnum::getValue).findFirst().orElse("");
    }
}
