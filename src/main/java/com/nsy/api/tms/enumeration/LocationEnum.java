package com.nsy.api.tms.enumeration;

import java.util.Arrays;

public enum LocationEnum {
    QUANZHOU("泉州"), GUANGZHOU("广州"), XIAMEN("厦门"),
    TAILI("泰利"),
    WEIYUE("伟跃"),
    MISI("宓思");

    String name;
    LocationEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
    public static String getNameBy(String location) {
        return Arrays.stream(values())
                .filter(s -> s.name().equalsIgnoreCase(location))
                .findAny()
                .map(LocationEnum::getName)
                .orElse(QUANZHOU.getName());
    }
}
