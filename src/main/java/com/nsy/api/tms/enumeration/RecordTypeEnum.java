package com.nsy.api.tms.enumeration;

import cn.hutool.core.date.DateException;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.nsy.api.core.apicore.exception.BusinessServiceException;

import java.util.Arrays;
import java.util.Date;
import java.util.stream.Collectors;

//日期类型
public enum RecordTypeEnum {
    MONTHLY("按月"), DAILY("按日");

    String desc;

    RecordTypeEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByName(String name) {
        return Arrays.stream(values())
                .filter(s -> s.name().equalsIgnoreCase(name))
                .findAny()
                .map(RecordTypeEnum::getDesc)
                .orElse("");
    }

    public static RecordTypeEnum getEnumByDesc(String desc) {
        return Arrays.stream(values())
                .filter(s -> s.getDesc().equalsIgnoreCase(desc))
                .findAny()
                .orElseThrow(() -> new BusinessServiceException(StrUtil.format("{}不存在，必须是[{}]其中一个", desc, Arrays.stream(values()).map(RecordTypeEnum::getDesc).collect(Collectors.joining("、")))));
    }

    public static void validEnumByName(String name) {
        Arrays.stream(values()).filter(s -> s.name().equalsIgnoreCase(name)).findAny()
                .orElseThrow(() -> new BusinessServiceException(StrUtil.format("{}不存在，必须是[{}]其中一个", name,
                        Arrays.stream(values()).map(RecordTypeEnum::name).collect(Collectors.joining("、")))));
    }

    /**
     * 展示在页面时需要
     * 通过日期记录类型，转化成对应的日期格式
     * isTable
     * true  图表      例2023-01-06 则每日的  要展示成1日 ；每月的 展示成1月
     * false 不是图表  例2023-01-06 则每日的  要展示成2023-01-06 ；每月的 展示成2023-01
     */
    public static String formatDate(String name, Date date, Boolean isTable) {
        if (date == null || name == null) {
            return "";
        }
        if (MONTHLY.name().equalsIgnoreCase(name)) {
            if (isTable) {
                int month = DateUtil.month(date) + 1;
                return month + "月";
            } else {
                return DateUtil.format(date, DatePattern.NORM_MONTH_PATTERN);
            }
        }
        if (DAILY.name().equalsIgnoreCase(name)) {
            if (isTable) {
                return DateUtil.dayOfMonth(date) + "日";
            } else {
                return DateUtil.format(date, DatePattern.NORM_DATE_PATTERN);
            }
        }
        return "";
    }

    /**
     * 前端查询/新增时，用此方法进行日期转化， 如前端2023.01 则会变成2023-01-01 00:00:00
     *
     * <AUTHOR>
     * 2023-02-02
     */
    public static DateTime formatDateIn(String formatStr, String recordType) {
        String target = formatStr;
        if (RecordTypeEnum.MONTHLY.name().equalsIgnoreCase(recordType)) {
            // 每月的 格式要特殊处理
            target = StrUtil.cleanBlank(StrUtil.sub(target.replaceAll("-", "").replaceAll("\\.", "")
                    .replaceAll(":", "").replaceAll("/", ""), 0, 6)) + "01000000";
        }
        DateTime parseDate;
        try {
            parseDate = DateUtil.parse(target);
        } catch (DateException e) {
            throw new BusinessServiceException(formatStr + "日期格式不正确, 请填写标准化的时间格式", e);
        }
        if (parseDate == null) throw new BusinessServiceException(formatStr + "日期格式不正确, 请填写标准化的时间格式");
        if (RecordTypeEnum.MONTHLY.name().equalsIgnoreCase(recordType)) {
            return DateUtil.beginOfMonth(parseDate);
        } else if (RecordTypeEnum.DAILY.name().equalsIgnoreCase(recordType)) {
            return DateUtil.beginOfDay(parseDate);
        }
        throw new BusinessServiceException("未找到日期记录类型");
    }
}
