package com.nsy.api.tms.enumeration;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public enum LogisticsTypeEnum {
    INTERNATIONAL_EXPRESS("国际快递"), INTERNATIONAL_PACKAGE("物流小包"), FREIGHT_FORWARDER("货代"), DOMESTIC_EXPRESS("国内快递");

    private String name;

    LogisticsTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }


    public static String resolve(String name) {
        return Arrays.stream(LogisticsTypeEnum.values())
                .filter((v) -> v.getName().equals(name))
                .findFirst()
                .map(logisticsTypeEnum -> logisticsTypeEnum.getName())
                .orElse(null);
    }

    public static String getDescByName(String name) {
        return Arrays.stream(LogisticsTypeEnum.values())
                .filter((v) -> v.name().equals(name))
                .findFirst()
                .map(LogisticsTypeEnum::getName)
                .orElse(name);
    }

}
