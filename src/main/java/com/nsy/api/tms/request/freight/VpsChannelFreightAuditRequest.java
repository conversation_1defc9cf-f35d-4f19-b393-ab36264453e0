package com.nsy.api.tms.request.freight;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-09-04 17:05:25
 */
@ApiModel(value = "VpsChannelFreightAuditRequest", description = "审核request")
public class VpsChannelFreightAuditRequest {
    @ApiModelProperty("id")
    private List<Integer> idList;

    @ApiModelProperty("reviewMark")
    private String reviewMark;

    @ApiModelProperty("status")
    private String status;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<Integer> getIdList() {
        return idList;
    }

    public void setIdList(List<Integer> idList) {
        this.idList = idList;
    }

    public String getReviewMark() {
        return reviewMark;
    }

    public void setReviewMark(String reviewMark) {
        this.reviewMark = reviewMark;
    }
}

