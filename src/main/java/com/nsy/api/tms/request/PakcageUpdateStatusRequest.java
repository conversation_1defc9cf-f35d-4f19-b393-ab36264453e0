package com.nsy.api.tms.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@ApiModel(value = "PakcageUpdateStatusRequest", description = "更新包裹状态")
public class PakcageUpdateStatusRequest {

    @Size(min = 1, message = "logisticsNos不能为空")
    @NotNull
    @ApiModelProperty(value = "物流单号", name = "logisticsNos", required = true)
    private List<String> logisticsNos;

    @NotBlank(message = "状态不能为空")
    private String status;

    public List<String> getLogisticsNos() {
        return logisticsNos;
    }

    public void setLogisticsNos(List<String> logisticsNos) {
        this.logisticsNos = logisticsNos;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
