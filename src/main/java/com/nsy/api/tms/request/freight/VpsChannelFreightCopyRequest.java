package com.nsy.api.tms.request.freight;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2023-09-04 17:05:25
 */
@ApiModel(value = "VpsChannelFreightCopyRequest", description = "复制request")
public class VpsChannelFreightCopyRequest {
    @ApiModelProperty("id")
    @NotNull(message = "id必填")
    private Integer id;

    @ApiModelProperty("targetLogisticsCompany")
    @NotNull(message = "物流公司id必填")
    private Integer targetLogisticsCompanyId;

    @ApiModelProperty("targetLogisticsChannelId")
    @NotNull(message = "渠道id必填")
    private Integer targetLogisticsChannelId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getTargetLogisticsCompanyId() {
        return targetLogisticsCompanyId;
    }

    public void setTargetLogisticsCompanyId(Integer targetLogisticsCompanyId) {
        this.targetLogisticsCompanyId = targetLogisticsCompanyId;
    }

    public Integer getTargetLogisticsChannelId() {
        return targetLogisticsChannelId;
    }

    public void setTargetLogisticsChannelId(Integer targetLogisticsChannelId) {
        this.targetLogisticsChannelId = targetLogisticsChannelId;
    }
}

