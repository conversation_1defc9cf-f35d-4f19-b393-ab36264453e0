package com.nsy.api.tms.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@ApiModel(value = "TmsLogisticsAccountListRequest", description = "物流账号列表查询request")
public class TmsLogisticsAccountListRequest extends BaseListRequest implements Serializable {

    private static final long serialVersionUID = 3368604889522787190L;

    @ApiModelProperty(value = "物流公司名称", name = "logisticsCompany")
    private String logisticsCompany;

    @ApiModelProperty(value = "物流账号", name = "logisticsAccount")
    private String logisticsAccount;

    @NotNull
    @ApiModelProperty(value = "地区(仓库系统当前地区)", name = "location")
    private String location;

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getLogisticsAccount() {
        return logisticsAccount;
    }

    public void setLogisticsAccount(String logisticsAccount) {
        this.logisticsAccount = logisticsAccount;
    }
}
