package com.nsy.api.tms.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/29 16:29
 */
@ApiModel(value = "LogisticsCompanyOperateIdRequest", description = "物流公司操作id集合")
public class LogisticsCompanyOperateIdRequest {

    @ApiModelProperty(value = "id集合", name = "idList")
    @NotNull(message = "id必填")
    private List<Integer> idList;

    public List<Integer> getIdList() {
        return idList;
    }

    public void setIdList(List<Integer> idList) {
        this.idList = idList;
    }
}
