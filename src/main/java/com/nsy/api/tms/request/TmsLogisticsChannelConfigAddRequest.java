package com.nsy.api.tms.request;

import com.nsy.api.tms.controller.channel.request.ChannelTimelineConfigInsertRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@ApiModel(value = "TmsLogisticsChannelConfigAddRequest", description = "渠道新增修改request")
public class TmsLogisticsChannelConfigAddRequest {

    @ApiModelProperty(value = "地区(仓库系统当前地区)", name = "location")
    private String location;

    @ApiModelProperty(value = "物流渠道名称", name = "logisticsChannelName")
    private String logisticsChannelName;

    @ApiModelProperty(value = "渠道编码", name = "logistics_channel_code")
    private String logisticsChannelCode;

    @ApiModelProperty(value = "物流公司", name = "logistics_company")
    private String logisticsCompany;

    @ApiModelProperty(value = "描述", name = "description")
    private String description;

    @ApiModelProperty(value = "是否启用:1--启用； 0--未启用", name = "status")
    private Integer status;

    @ApiModelProperty(value = "默认匹配最小重量", name = "defaultMatchMinWeight")
    private BigDecimal defaultMatchMinWeight;

    @ApiModelProperty(value = "默认匹配最大重量", name = "defaultMatchMaxWeight")
    private BigDecimal defaultMatchMaxWeight;

    @ApiModelProperty(value = "限重", name = "limitWeight")
    private BigDecimal limitWeight;

    @ApiModelProperty(value = "申报价值上限", name = "max_declared_value")
    private BigDecimal maxDeclaredValue;

    @ApiModelProperty(value = "配置", name = "keyGroup")
    private String keyGroup;

    @ApiModelProperty(value = "计费方式", name = "billingType")
    private String billingType;

    @ApiModelProperty(value = "物流类型", name = "channelType")
    private String channelType;

    @ApiModelProperty(value = "对接方法", name = "logisticsMethod")
    private String logisticsMethod;

    private Integer businessEnable;

    private String expressType;

    public String getLogisticsMethod() {
        return logisticsMethod;
    }

    public void setLogisticsMethod(String logisticsMethod) {
        this.logisticsMethod = logisticsMethod;
    }

    private List<ChannelTimelineConfigInsertRequest> timelineConfigList = new ArrayList<>();

    public List<ChannelTimelineConfigInsertRequest> getTimelineConfigList() {
        return timelineConfigList;
    }

    public void setTimelineConfigList(List<ChannelTimelineConfigInsertRequest> timelineConfigList) {
        this.timelineConfigList = timelineConfigList;
    }

    public String getExpressType() {
        return expressType;
    }

    public void setExpressType(String expressType) {
        this.expressType = expressType;
    }

    public Integer getBusinessEnable() {
        return businessEnable;
    }

    public void setBusinessEnable(Integer businessEnable) {
        this.businessEnable = businessEnable;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getBillingType() {
        return billingType;
    }

    public void setBillingType(String billingType) {
        this.billingType = billingType;
    }

    public String getKeyGroup() {
        return keyGroup;
    }

    public void setKeyGroup(String keyGroup) {
        this.keyGroup = keyGroup;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLogisticsChannelName() {
        return logisticsChannelName;
    }

    public void setLogisticsChannelName(String logisticsChannelName) {
        this.logisticsChannelName = logisticsChannelName;
    }

    public String getLogisticsChannelCode() {
        return logisticsChannelCode;
    }

    public void setLogisticsChannelCode(String logisticsChannelCode) {
        this.logisticsChannelCode = logisticsChannelCode;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }


    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public BigDecimal getDefaultMatchMinWeight() {
        return defaultMatchMinWeight;
    }

    public void setDefaultMatchMinWeight(BigDecimal defaultMatchMinWeight) {
        this.defaultMatchMinWeight = defaultMatchMinWeight;
    }

    public BigDecimal getDefaultMatchMaxWeight() {
        return defaultMatchMaxWeight;
    }

    public void setDefaultMatchMaxWeight(BigDecimal defaultMatchMaxWeight) {
        this.defaultMatchMaxWeight = defaultMatchMaxWeight;
    }

    public BigDecimal getMaxDeclaredValue() {
        return maxDeclaredValue;
    }

    public void setMaxDeclaredValue(BigDecimal maxDeclaredValue) {
        this.maxDeclaredValue = maxDeclaredValue;
    }

    public BigDecimal getLimitWeight() {
        return limitWeight;
    }

    public void setLimitWeight(BigDecimal limitWeight) {
        this.limitWeight = limitWeight;
    }
}
