package com.nsy.api.tms.request;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class TmsAlertHandleRequest {
    //主键
    private Integer id;

    //处理状态
    private String status;

    //是否索赔 0-不索赔 1-索赔
    private Integer isClaim;

    //备注
    private String remark;

    //索赔金额
    // private BigDecimal amount;

    //是否退件
    private String isReturn;

    // 异常任务类型 0-未收 1-异常 2-疑似丢件
    public Integer taskType;

    private String communicationResult;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date estimatedFinishTime;

    //妥投时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryDate;
    //改派单号
    private String newLogisticsNo;

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public String getIsReturn() {
        return isReturn;
    }

    public void setIsReturn(String isReturn) {
        this.isReturn = isReturn;
    }

    /*   public BigDecimal getAmount() {
               return amount;
           }

           public void setAmount(BigDecimal amount) {
               this.amount = amount;
           }
       */
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getIsClaim() {
        return isClaim;
    }

    public void setIsClaim(Integer isClaim) {
        this.isClaim = isClaim;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCommunicationResult() {
        return communicationResult;
    }

    public void setCommunicationResult(String communicationResult) {
        this.communicationResult = communicationResult;
    }

    public Date getEstimatedFinishTime() {
        return estimatedFinishTime;
    }

    public void setEstimatedFinishTime(Date estimatedFinishTime) {
        this.estimatedFinishTime = estimatedFinishTime;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getNewLogisticsNo() {
        return newLogisticsNo;
    }

    public void setNewLogisticsNo(String newLogisticsNo) {
        this.newLogisticsNo = newLogisticsNo;
    }
}
