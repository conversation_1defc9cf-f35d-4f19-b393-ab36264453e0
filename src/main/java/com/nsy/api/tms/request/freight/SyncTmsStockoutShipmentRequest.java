package com.nsy.api.tms.request.freight;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.tms.domain.StockoutShipmentItemModel;
import com.nsy.api.tms.domain.StockoutShipmentModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 装箱清单
 *
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "SyncTmsStockoutShipmentRequest", description = "装箱清单同步tmsRequest")
public class SyncTmsStockoutShipmentRequest {

    @ApiModelProperty(value = "stockoutShipment", name = "装箱清单对象")
    private StockoutShipmentModel stockoutShipment;

    @ApiModelProperty(value = "stockoutShipmentItemList", name = "装箱清单明细")
    private List<StockoutShipmentItemModel> stockoutShipmentItemList = new ArrayList<>();

    private Integer storeId;

    private String storeName;

    private String businessType;

    private String postCode;

    private String countryCode;

    /**
     * 订单付款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderPayTime;

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public Date getOrderPayTime() {
        return orderPayTime;
    }

    public void setOrderPayTime(Date orderPayTime) {
        this.orderPayTime = orderPayTime;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public StockoutShipmentModel getStockoutShipment() {
        return this.stockoutShipment;
    }

    public void setStockoutShipment(StockoutShipmentModel stockoutShipment) {
        this.stockoutShipment = stockoutShipment;
    }

    public List<StockoutShipmentItemModel> getStockoutShipmentItemList() {
        return stockoutShipmentItemList;
    }

    public void setStockoutShipmentItemList(List<StockoutShipmentItemModel> stockoutShipmentItemList) {
        this.stockoutShipmentItemList = stockoutShipmentItemList;
    }
}
