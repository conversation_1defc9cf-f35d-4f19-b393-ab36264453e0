package com.nsy.api.tms.request.freight;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.tms.request.BaseListRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-09-14 14:54:12
 */
@ApiModel(value = "com.nsy.api.tms.request.freight.PackageBillPageRequest", description = "列表查询request")
public class PackageBillPageRequest extends BaseListRequest {

    @ApiModelProperty("ids")
    private List<Integer> ids = new ArrayList<>();

    /**
     * 物流公司id
     */
    @ApiModelProperty("物流公司id")
    private Integer logisticsCompanyId;

    /**
     * 渠道id
     */
    @ApiModelProperty("渠道id")
    private List<Integer> channelId;

    /**
     * 账号
     */
    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("订单号")
    private List<String> orderNos;

    @ApiModelProperty("物流单号")
    private List<String> logisticsNos = new ArrayList<>();

    /**
     * 状态(待核对INIT、运价/费率异常EXCEPTION、核对通过PASS、核对不通过REJECT)
     */
    @ApiModelProperty("状态(待核对INIT、运价/费率异常EXCEPTION、核对通过PASS、核对不通过REJECT)")
    private List<String> statusList;

    /**
     * 差异类型(1待差异判断，2无差异，3重量差异，4运费差异，5重量运费差异)
     */
    @ApiModelProperty("差异类型(1待差异判断，2无差异，3重量差异，4运费差异，5重量运费差异)")
    private List<Integer> differenceTypeList;

    @ApiModelProperty("店铺id")
    private List<Integer> storeIdList;

    @ApiModelProperty("财务推送状态(0-未推送,1-已推送,2-已二次推送)")
    private Integer financialPushStatus;

    @ApiModelProperty("核对结果(1核对通过，0核对不通过)")
    private Integer reviewResult;

    /**
     * 账单名称
     */
    @ApiModelProperty("账单名称")
    private String billName;



    /**
     * 部门
     */
    @ApiModelProperty("部门")
    private String businessType;


    /**
     * 对账日期
     */
    @ApiModelProperty("对账日期开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkDateBegin;

    /**
     * 对账日期
     */
    @ApiModelProperty("对账日期结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkDateEnd;

    /**
     * 对账日期
     */
    @ApiModelProperty("业务日期开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date businessDateBegin;

    /**
     * 对账日期
     */
    @ApiModelProperty("业务日期结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date businessDateEnd;

    /**
     * 发货时间
     */
    @ApiModelProperty("发货时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryDateBegin;


    /**
     * 发货时间
     */
    @ApiModelProperty("发货时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryDateEnd;

    private boolean verify = false;

    public Integer getFinancialPushStatus() {
        return financialPushStatus;
    }

    public void setFinancialPushStatus(Integer financialPushStatus) {
        this.financialPushStatus = financialPushStatus;
    }

    public Integer getReviewResult() {
        return reviewResult;
    }

    public void setReviewResult(Integer reviewResult) {
        this.reviewResult = reviewResult;
    }

    public List<String> getLogisticsNos() {
        return logisticsNos;
    }

    public void setLogisticsNos(List<String> logisticsNos) {
        this.logisticsNos = logisticsNos;
    }

    public List<Integer> getStoreIdList() {
        return storeIdList;
    }

    public void setStoreIdList(List<Integer> storeIdList) {
        this.storeIdList = storeIdList;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public boolean isVerify() {
        return verify;
    }

    public void setVerify(boolean verify) {
        this.verify = verify;
    }

    public Date getBusinessDateBegin() {
        return businessDateBegin;
    }

    public void setBusinessDateBegin(Date businessDateBegin) {
        this.businessDateBegin = businessDateBegin;
    }

    public Date getBusinessDateEnd() {
        return businessDateEnd;
    }

    public void setBusinessDateEnd(Date businessDateEnd) {
        this.businessDateEnd = businessDateEnd;
    }

    public List<String> getOrderNos() {
        return orderNos;
    }

    public void setOrderNos(List<String> orderNos) {
        this.orderNos = orderNos;
    }

    public List<Integer> getIds() {
        return ids;
    }

    public void setIds(List<Integer> ids) {
        this.ids = ids;
    }

    public Integer getLogisticsCompanyId() {
        return logisticsCompanyId;
    }

    public void setLogisticsCompanyId(Integer logisticsCompanyId) {
        this.logisticsCompanyId = logisticsCompanyId;
    }

    public List<Integer> getChannelId() {
        return channelId;
    }

    public void setChannelId(List<Integer> channelId) {
        this.channelId = channelId;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public List<String> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<String> statusList) {
        this.statusList = statusList;
    }

    public List<Integer> getDifferenceTypeList() {
        return differenceTypeList;
    }

    public void setDifferenceTypeList(List<Integer> differenceTypeList) {
        this.differenceTypeList = differenceTypeList;
    }

    public String getBillName() {
        return billName;
    }

    public void setBillName(String billName) {
        this.billName = billName;
    }

    public Date getCheckDateBegin() {
        return checkDateBegin;
    }

    public void setCheckDateBegin(Date checkDateBegin) {
        this.checkDateBegin = checkDateBegin;
    }

    public Date getCheckDateEnd() {
        return checkDateEnd;
    }

    public void setCheckDateEnd(Date checkDateEnd) {
        this.checkDateEnd = checkDateEnd;
    }

    public Date getDeliveryDateBegin() {
        return deliveryDateBegin;
    }

    public void setDeliveryDateBegin(Date deliveryDateBegin) {
        this.deliveryDateBegin = deliveryDateBegin;
    }

    public Date getDeliveryDateEnd() {
        return deliveryDateEnd;
    }

    public void setDeliveryDateEnd(Date deliveryDateEnd) {
        this.deliveryDateEnd = deliveryDateEnd;
    }
}

