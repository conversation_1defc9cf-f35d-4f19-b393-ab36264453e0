package com.nsy.api.tms.request.freight;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-09-04 17:05:25
 */
@ApiModel(value = "com.nsy.api.tms.request.freight.VpsChannelCountryFreightUpdateRequest", description = "更新request")
public class VpsChannelCountryFreightUpdateRequest {
    @ApiModelProperty("")
    private Integer id;

    /**
     * 公司id
     */
    @ApiModelProperty("公司id")
    private Integer companyId;

    /**
     * 渠道id
     */
    @ApiModelProperty("渠道id")
    private Integer channelFreightId;

    /**
     * 国家code
     */
    @ApiModelProperty("国家code")
    private Integer countryCode;

    /**
     * 折扣系数
     */
    @ApiModelProperty("折扣系数")
    private BigDecimal discountFactor;

    /**
     * 重量价格类型
     */
    @ApiModelProperty("重量价格类型")
    private String weightBillType;

    /**
     * 起始重量
     */
    @ApiModelProperty("起始重量")
    private BigDecimal startWeight;

    /**
     * 终止重量
     */
    @ApiModelProperty("终止重量")
    private BigDecimal endWeight;

    /**
     * 首重
     */
    @ApiModelProperty("首重")
    private BigDecimal initWeight;

    /**
     * 首重价格
     */
    @ApiModelProperty("首重价格")
    private BigDecimal intiFreight;

    /**
     * 续重
     */
    @ApiModelProperty("续重")
    private BigDecimal extraWeight;

    /**
     * 续重价格
     */
    @ApiModelProperty("续重价格")
    private BigDecimal extraFreight;

    /**
     * 固定费用
     */
    @ApiModelProperty("固定费用")
    private BigDecimal fixedFreight;

    /**
     * 运费单价
     */
    @ApiModelProperty("运费单价")
    private BigDecimal unitFreight;

    /**
     * 挂号费
     */
    @ApiModelProperty("挂号费")
    private BigDecimal registrationFreight;

    /**
     * 附加费
     */
    @ApiModelProperty("附加费")
    private BigDecimal otherFreight;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 更新者
     */
    @ApiModelProperty("更新者")
    private String updateBy;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private Long version;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public Integer getChannelFreightId() {
        return channelFreightId;
    }

    public void setChannelFreightId(Integer channelFreightId) {
        this.channelFreightId = channelFreightId;
    }

    public Integer getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(Integer countryCode) {
        this.countryCode = countryCode;
    }

    public BigDecimal getDiscountFactor() {
        return discountFactor;
    }

    public void setDiscountFactor(BigDecimal discountFactor) {
        this.discountFactor = discountFactor;
    }

    public String getWeightBillType() {
        return weightBillType;
    }

    public void setWeightBillType(String weightBillType) {
        this.weightBillType = weightBillType;
    }

    public BigDecimal getStartWeight() {
        return startWeight;
    }

    public void setStartWeight(BigDecimal startWeight) {
        this.startWeight = startWeight;
    }

    public BigDecimal getEndWeight() {
        return endWeight;
    }

    public void setEndWeight(BigDecimal endWeight) {
        this.endWeight = endWeight;
    }

    public BigDecimal getInitWeight() {
        return initWeight;
    }

    public void setInitWeight(BigDecimal initWeight) {
        this.initWeight = initWeight;
    }

    public BigDecimal getIntiFreight() {
        return intiFreight;
    }

    public void setIntiFreight(BigDecimal intiFreight) {
        this.intiFreight = intiFreight;
    }

    public BigDecimal getExtraWeight() {
        return extraWeight;
    }

    public void setExtraWeight(BigDecimal extraWeight) {
        this.extraWeight = extraWeight;
    }

    public BigDecimal getExtraFreight() {
        return extraFreight;
    }

    public void setExtraFreight(BigDecimal extraFreight) {
        this.extraFreight = extraFreight;
    }

    public BigDecimal getFixedFreight() {
        return fixedFreight;
    }

    public void setFixedFreight(BigDecimal fixedFreight) {
        this.fixedFreight = fixedFreight;
    }

    public BigDecimal getUnitFreight() {
        return unitFreight;
    }

    public void setUnitFreight(BigDecimal unitFreight) {
        this.unitFreight = unitFreight;
    }

    public BigDecimal getRegistrationFreight() {
        return registrationFreight;
    }

    public void setRegistrationFreight(BigDecimal registrationFreight) {
        this.registrationFreight = registrationFreight;
    }

    public BigDecimal getOtherFreight() {
        return otherFreight;
    }

    public void setOtherFreight(BigDecimal otherFreight) {
        this.otherFreight = otherFreight;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

}

