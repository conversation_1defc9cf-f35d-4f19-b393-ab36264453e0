package com.nsy.api.tms.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-03-22 15:59:32
 */
@ApiModel(value = "ChannelForwarderPriceRecordInsertRequest", description = "新增request")
public class ChannelForwarderPriceRecordInsertRequest {
    /**
     * 物流公司
     */
    @ApiModelProperty("物流公司")
    @NotBlank(message = "物流公司不能为空")
    private String logisticsCompany;

    /**
     * 物流方式(AIR空运；SEA海运)
     */
    @ApiModelProperty("物流方式(AIR空运；SEA海运)")
    @NotBlank(message = "物流方式(AIR空运；SEA海运)不能为空")
    private String freightMode;

    /**
     * 日期
     */
    @ApiModelProperty("日期")
    @NotNull(message = "日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date priceDate;

    /**
     * 目的地
     */
    @ApiModelProperty("目的地")
    @NotBlank(message = "物流公司不能为空")
    private String destination;

    /**
     * 金额
     */
    @ApiModelProperty("金额")
    @NotNull(message = "金额不能为空")
    private BigDecimal price;

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getFreightMode() {
        return freightMode;
    }

    public void setFreightMode(String freightMode) {
        this.freightMode = freightMode;
    }

    public Date getPriceDate() {
        return priceDate;
    }

    public void setPriceDate(Date priceDate) {
        this.priceDate = priceDate;
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }
}

