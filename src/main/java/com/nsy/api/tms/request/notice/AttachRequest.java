package com.nsy.api.tms.request.notice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-08-05 15:40:50
 */
@ApiModel(value = "AttachRequest", description = "公告管理附件请求")
public class AttachRequest implements Serializable {

    private static final long serialVersionUID = -6938760582623486310L;
    @ApiModelProperty(value = "公告id", name = "noticeMessageId")
    private Integer noticeMessageId;

    @ApiModelProperty(value = "附件名称", name = "originName")
    private String originName;

    @ApiModelProperty(value = "附件类型", name = "attachType")
    private String attachType;

    @ApiModelProperty(value = "附件目录", name = "name")
    private String name;

    @ApiModelProperty(value = "附件访问路径", name = "url")
    private String url;

    public Integer getNoticeMessageId() {
        return noticeMessageId;
    }

    public void setNoticeMessageId(Integer noticeMessageId) {
        this.noticeMessageId = noticeMessageId;
    }

    public String getOriginName() {
        return originName;
    }

    public void setOriginName(String originName) {
        this.originName = originName;
    }

    public String getAttachType() {
        return attachType;
    }

    public void setAttachType(String attachType) {
        this.attachType = attachType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}

