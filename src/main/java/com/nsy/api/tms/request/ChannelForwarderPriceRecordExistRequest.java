package com.nsy.api.tms.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-03-22 15:59:31
 */
@ApiModel(value = "ChannelForwarderPriceRecordExistRequest", description = "查询是否存在")
public class ChannelForwarderPriceRecordExistRequest {

    /**
     * 物流公司
     */
    @ApiModelProperty("物流公司")
    @NotBlank
    private String logisticsCompany;


    /**
     * 目的地
     */
    @ApiModelProperty("目的地")
    @NotBlank
    private String destination;

    /**
     * 日期
     */
    @ApiModelProperty("运价日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull
    private Date priceDate;

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public Date getPriceDate() {
        return priceDate;
    }

    public void setPriceDate(Date priceDate) {
        this.priceDate = priceDate;
    }
}

