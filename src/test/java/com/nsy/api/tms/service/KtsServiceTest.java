package com.nsy.api.tms.service;

import com.nsy.api.tms.SpringServiceTest;
import com.nsy.api.tms.dao.entity.TmsConfigEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsChannelConfigEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.domain.Address;
import com.nsy.api.tms.domain.OrderInfo;
import com.nsy.api.tms.domain.OrderItemInfo;
import com.nsy.api.tms.repository.PackageRepository;
import com.nsy.api.tms.repository.TmsConfigRepository;
import com.nsy.api.tms.repository.TmsLogisticsChannelConfigRepository;
import com.nsy.api.tms.request.OrderRequest;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.utils.Lists;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
@Ignore
public class KtsServiceTest extends SpringServiceTest {

    @Inject
    TmsService tmsService;

    @Inject
    TmsLogisticsChannelConfigService channelConfigService;

    @Inject
    TmsConfigRepository tmsConfigRepository;

    @Inject
    PackageRepository packageRepository;

    @Inject
    TmsLogisticsChannelConfigRepository tmsChannelConfigRepository;

    @Before
    public void init() {
        // init sfExpress channel config
        TmsLogisticsChannelConfigEntity tmsLogisticsChannelConfigEntity = new TmsLogisticsChannelConfigEntity();
        tmsLogisticsChannelConfigEntity.setLogisticsChannelCode("SF-KTS-9");
        tmsLogisticsChannelConfigEntity.setLogisticsChannelName("顺丰国际小包平邮");
        tmsLogisticsChannelConfigEntity.setLogisticsMethod("顺丰国际");
        tmsLogisticsChannelConfigEntity.setKeyGroup("顺丰国际KTS");
        tmsLogisticsChannelConfigEntity.setStatus(1);
        tmsLogisticsChannelConfigEntity.setExpressType("9");
        channelConfigService.save(tmsLogisticsChannelConfigEntity);

        // init sfExpress authenticate config
        TmsConfigEntity tmsConfigEntity1 = new TmsConfigEntity();
        tmsConfigEntity1.setKeyGroup("顺丰国际KTS");
        tmsConfigEntity1.setKey("sf_kts_clientCode");
        tmsConfigEntity1.setValue("erptest");

        TmsConfigEntity tmsConfigEntity2 = new TmsConfigEntity();
        tmsConfigEntity2.setKeyGroup("顺丰国际KTS");
        tmsConfigEntity2.setKey("sf_kts_checkCode");
        tmsConfigEntity2.setValue("78BE1BCAAED1EE08D344F894FBB296D3");
        tmsConfigRepository.saveAll(Lists.newArrayList(tmsConfigEntity1, tmsConfigEntity2));
    }

    @After
    public void clearAllData() {
        tmsChannelConfigRepository.deleteAll();
        tmsConfigRepository.deleteAll();
    }

    @Test
    @Ignore
    public void generateOrder() {
        OrderRequest request = new OrderRequest();
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setTid("TEST5c29088139b7ac221d4d5749");
        orderInfo.setLogisticsChannelCode("SF-KTS-9");
        orderInfo.setLogisticsMethod("顺丰国际");
        orderInfo.setLength(1.00);
        orderInfo.setWidth(1.00);
        orderInfo.setHeight(1.00);
        orderInfo.setWeight(0.370);
        orderInfo.setCustomsValueAmount(8.80);
        orderInfo.setOrderItemInfoList(setItemList());
        orderInfo.setSender(setSender());
        orderInfo.setReceiver(setReciver());

        request.setOrderInfo(orderInfo);
        GenerateOrderResponse orderResponse = tmsService.generateOrder(request);
        // 测试创建物流单成功情况
        if (orderResponse.getSuccessEntity() != null) {
            List<TmsPackageEntity> packageEntityList = packageRepository.findByTidAndDeleteFlag(orderInfo.getTid(), 0);
            Assert.assertNotNull(packageEntityList.get(0));
            Assert.assertNotNull(packageEntityList.get(0).getLogisticsNo());
        } else { // 测试创建物流单失败情况
            Assert.assertNotNull(orderResponse.getError().getMessage());
        }
    }
//
//    @Test
//    public void test

    private List<OrderItemInfo> setItemList() {
        List<OrderItemInfo> itemInfoList = new ArrayList<>();
        OrderItemInfo item = new OrderItemInfo();
        item.setCnName("连衣裙");
        item.setEnName("Women Dress");
        item.setCount(1);
        item.setWeight(0.370);
        item.setCustomsPrice(2.88);
        item.setUnitPrice(2.88);
        item.setHsCode("6112410000");
        itemInfoList.add(item);

        OrderItemInfo item1 = new OrderItemInfo();
        item1.setCnName("连衣裙");
        item1.setEnName("Women Dress");
        item1.setCount(1);
        item1.setWeight(0.370);
        item1.setCustomsPrice(2.88);
        item1.setUnitPrice(2.88);
        item1.setHsCode("6112410000");
        itemInfoList.add(item1);
        return itemInfoList;
    }

    private Address setSender() {
        Address sender = new Address();
        sender.setName("Cherry");
        sender.setPostCode("362001");
        sender.setMobile("13798229216");
        sender.setPhone("13798229216");
        sender.setCountry("CN");
        sender.setProvince("fujiansheng");
        sender.setCity("quanzhoushi");
        sender.setCounty("fengzequ");
        sender.setCompany("meihuoshiyingfushi");
        sender.setStreet("NXK GC N3P SPB DONGHAI BINCHENG");
        return sender;
    }

    private Address setReciver() {
        Address receiver = new Address();
        receiver.setName("Jutta Strauss");
        receiver.setPostCode("1180");
        receiver.setPhone("+43 664 2425126");
        receiver.setMobile("+43 664 2425126");
        receiver.setCountry("AT");
        receiver.setProvince("AT");
        receiver.setCity("Wien");
        receiver.setStreet("Starkfriedgasse 25");
        receiver.setCompany("Jutta Strauss");
        receiver.setEmail("<EMAIL>");
        return receiver;
    }
}
