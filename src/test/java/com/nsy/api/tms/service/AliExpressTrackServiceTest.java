package com.nsy.api.tms.service;

import com.nsy.api.tms.SpringServiceTest;
import com.nsy.api.tms.dao.entity.TmsConfigEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsChannelConfigEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.repository.TmsConfigRepository;
import com.nsy.api.tms.service.external.AliExpressTrackService;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.inject.Inject;

/**
 * @Author: Woods Lee
 * @Date: 2019/3/19 16:05
 */
@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
@Ignore
public class AliExpressTrackServiceTest extends SpringServiceTest {
    @Inject
    AliExpressTrackService aliExpressTrackService;

    @Inject
    TmsLogisticsChannelConfigService channelConfigService;

    @Inject
    TmsConfigRepository tmsConfigRepository;

    @Before
    public void init() {
        // init AliExpress channel config
        TmsLogisticsChannelConfigEntity tmsLogisticsChannelConfigEntity = new TmsLogisticsChannelConfigEntity();
        tmsLogisticsChannelConfigEntity.setLogisticsChannelCode("AliExpress");
        tmsLogisticsChannelConfigEntity.setLogisticsMethod("AliExpress");
        tmsLogisticsChannelConfigEntity.setKeyGroup("AliExpress");
        tmsLogisticsChannelConfigEntity.setStatus(1);
        channelConfigService.save(tmsLogisticsChannelConfigEntity);
        // init AliExpress authenticate config
        TmsConfigEntity tmsConfigEntity = new TmsConfigEntity();
        tmsConfigEntity.setKeyGroup("AliExpress_Track");
        tmsConfigEntity.setKey("aliexpress_sessionKey");
        tmsConfigEntity.setValue("50002701509bLDvdiwEro15dcf2dei9EAp7qzwFmhwLXjszoE0mOQiLO3SzCznGKpXb");
        tmsConfigRepository.save(tmsConfigEntity);
    }

    @Test
    @Ignore
    public void doTrack() {
        TmsPackageEntity tmsPackageEntity = new TmsPackageEntity();
        tmsPackageEntity.setLogisticsChannelCode("AliExpress");
        tmsPackageEntity.setLogisticsNo("RU346721648HK"); //测试数据，物流单号
        tmsPackageEntity.setStatus("创建");
        aliExpressTrackService.doTrack(tmsPackageEntity);
    }
}
