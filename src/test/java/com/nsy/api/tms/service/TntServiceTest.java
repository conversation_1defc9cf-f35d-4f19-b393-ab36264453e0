package com.nsy.api.tms.service;

import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.SpringServiceTest;
import com.nsy.api.tms.dao.entity.TmsConfigEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsChannelConfigEntity;
import com.nsy.api.tms.domain.Address;
import com.nsy.api.tms.domain.OrderInfo;
import com.nsy.api.tms.domain.OrderItemInfo;
import com.nsy.api.tms.repository.TmsConfigRepository;
import com.nsy.api.tms.repository.TmsLogisticsChannelConfigRepository;
import com.nsy.api.tms.request.OrderRequest;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.service.external.TntService;
import com.nsy.api.tms.utils.Lists;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: Woods Lee
 * @Date: 2019/9/23 10:41
 */
@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class TntServiceTest extends SpringServiceTest {
    @Inject
    TntService tntService;

    @Inject
    TmsLogisticsChannelConfigRepository channelConfigRepository;

    @Inject
    TmsConfigRepository tmsConfigRepository;

    @Value("${tnt.ship.url}")
    String serverUrl;

    @Before
    public void init() {
        // init tnt authenticate config
        TmsConfigEntity tmsConfigEntity1 = new TmsConfigEntity();
        tmsConfigEntity1.setKeyGroup("TNT");
        tmsConfigEntity1.setKey("tnt_userName");
        tmsConfigEntity1.setValue("SHIYINt");

        TmsConfigEntity tmsConfigEntity2 = new TmsConfigEntity();
        tmsConfigEntity2.setKeyGroup("TNT");
        tmsConfigEntity2.setKey("tnt_password");
        tmsConfigEntity2.setValue("China123t");

        tmsConfigRepository.saveAll(Lists.newArrayList(tmsConfigEntity1, tmsConfigEntity2));
    }

    private TmsLogisticsChannelConfigEntity mockLogisticsChannelConfigEntity() {
        // init tnt channel config
        TmsLogisticsChannelConfigEntity tmsLogisticsChannelConfigEntity = new TmsLogisticsChannelConfigEntity();
        tmsLogisticsChannelConfigEntity.setLogisticsMethod("TNT");
        tmsLogisticsChannelConfigEntity.setKeyGroup("TNT");
        tmsLogisticsChannelConfigEntity.setLogisticsChannelCode("TNT");
        tmsLogisticsChannelConfigEntity.setLogisticsChannelName("TNT");
        tmsLogisticsChannelConfigEntity.setStatus(1);
        channelConfigRepository.save(tmsLogisticsChannelConfigEntity);
        return tmsLogisticsChannelConfigEntity;
    }

    @After
    public void clearAllData() {
        channelConfigRepository.deleteAll();
        tmsConfigRepository.deleteAll();
    }

    @Test
    @Ignore
    public void generateOrder() {
        OrderRequest request = getOrderRequest();
        GenerateOrderResponse generateOrderResponse = tntService.syncGenerateOrder(request, mockLogisticsChannelConfigEntity());
        if (generateOrderResponse.getSuccessEntity() != null) {
            Assert.assertTrue(StringUtils.hasText(generateOrderResponse.getSuccessEntity().getLogisticsNo()));
            //  Assert.assertTrue(StringUtils.hasText(generateOrderResponse.getSuccessEntity().getLabelUrl()));
            System.out.println("物流单号:" + generateOrderResponse.getSuccessEntity().getLogisticsNo());
            System.out.println("面单url:" + generateOrderResponse.getSuccessEntity().getLabelUrl());
        } else if (generateOrderResponse.getError() != null) {
            Assert.assertNotNull(generateOrderResponse.getError().getMessage());
            System.out.println(generateOrderResponse.getError().getMessage());
        }
    }

    private OrderRequest getOrderRequest() {
        OrderRequest request = new OrderRequest();
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setTid("123");
        orderInfo.setLogisticsChannelCode("TNT");
        List<OrderItemInfo> orderItemList = new ArrayList<>();
        OrderItemInfo orderItemInfo = new OrderItemInfo();
        orderItemInfo.setCount(1);
        orderItemInfo.setCnName("衣服");
        orderItemInfo.setEnName("clothing");
        orderItemInfo.setUnitPrice(1.3);
        orderItemInfo.setWeight(0.3);
        orderItemList.add(orderItemInfo);
        orderInfo.setOrderItemInfoList(orderItemList);
        Address sender = new Address();
        setSenderInfo(sender, orderInfo);
        Address receiver = new Address();
        setReceiveInfo(receiver, orderInfo);
        orderInfo.setLogisticsMethod("TNT");
        orderInfo.setPlatform("B2B");
        orderInfo.setLength(2D);
        orderInfo.setWidth(1D);
        orderInfo.setHeight(1D);
        orderInfo.setWeight(10D);
        orderInfo.setStoreId(1111);
        request.setOrderInfo(orderInfo);
        return request;
    }

    private void setReceiveInfo(Address receiver, OrderInfo orderInfo) {
        receiver.setCompany("aha maher");
        receiver.setName("yunxi");
        receiver.setPhone("*********");
        receiver.setCity("cairo");
        receiver.setStreet("130 osman ebn afan st, al");
        receiver.setCounty("EG");
        receiver.setProvince("GA");
        //receiver.setProvince("cairo");
        //receiver.setPostCode("11435");
        receiver.setCountry("EG");
        orderInfo.setReceiver(receiver);
        //package
        orderInfo.setWeight(0.5);
    }

    private void setSenderInfo(Address sender, OrderInfo orderInfo) {
        //shipper
        sender.setCompany("shiying");
        sender.setName("woods");
        sender.setPhone("13010101010");
        sender.setCity("quanzhou");
        sender.setCounty("donghai");
        sender.setStreet("RenMingNanLu");
        sender.setCountry("CN");
        sender.setPostCode("362001");
        sender.setCountry("CN");
        orderInfo.setSender(sender);
    }

}
