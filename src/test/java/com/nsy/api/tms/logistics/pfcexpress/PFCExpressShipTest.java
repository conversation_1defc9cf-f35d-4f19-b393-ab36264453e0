package com.nsy.api.tms.logistics.pfcexpress;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.logistics.pfcexpress.request.ship.PFCLabelResponse;
import com.nsy.api.tms.logistics.pfcexpress.response.track.PFCExpressTrackResponse;
import com.nsy.api.tms.utils.EncodeUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: Woods Lee
 * @Date: 2019/3/1 15:25
 */
@Ignore
public class PFCExpressShipTest {

    @Test
    public void testGetChannel() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("secretkey", "39f55000-0a7d-4308-a2a9-29b25bbfd0d480000");
        String response = new RestTemplate().postForObject("http://*************/webservice/APIWebService.asmx/getChannel", params, String.class);
        System.out.println(response);
    }

    @Test
    public void testGetCountry() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("secretkey", "39f55000-0a7d-4308-a2a9-29b25bbfd0d480000");
        String response = new RestTemplate().postForObject("http://*************/webservice/APIWebService.asmx/getCountry", params, String.class);
        System.out.println(response);
    }

    @Test
    public void testGetWarehouse() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("secretkey", "39f55000-0a7d-4308-a2a9-29b25bbfd0d480000");
        String response = new RestTemplate().postForObject("http://180.167.5.74/webservice/APIWebService.asmx/GetWarehouse", params, String.class);
        System.out.println(response);
    }

    @Test
    public void testTrack() throws IOException {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("Secretkey", "39f55000-0a7d-4308-a2a9-29b25bbfd0d480000");
        params.add("Orderid", "R800001811290046");
        String response = new RestTemplate().postForObject("http://*************/webservice/APIWebService.asmx/getOrder_Track", params, String.class);
        String s = response.replaceFirst("\\s*<[^<>]+>\\s*", "");
        String substring = s.substring(s.indexOf('['), s.indexOf(']') + 1);
        System.out.println("s= " + substring);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.enable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
        objectMapper.enable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        List<PFCExpressTrackResponse> replys = objectMapper.readValue(substring, new TypeReference<List<PFCExpressTrackResponse>>() {
        });
        List<PFCExpressTrackResponse> collect = replys.stream().filter(item ->
            StringUtils.hasText(item.getDetailDesc()) || item.getOccurTime() != null
        ).sorted(Comparator.comparing(PFCExpressTrackResponse::getOccurTime)).collect(Collectors.toList());
        collect.forEach(System.out::println);
        //System.out.println(response);
    }

    @Test
    public void testShip() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("Secretkey", "39f55000-0a7d-4308-a2a9-29b25bbfd0d480000");
        params.add("Strorderinfo", "Style:2;GFF_CustomerID:80000;GFF_ReceiveSendAddressID:;ConsigneeName:lisa;Country:84;Base_ChannelInfoID:USPS;State:Paris;City:Paris;OrderStatus:3;Address1:144 rue de rennes, 5eme etage – CODE 6335A – NOM –COTTIN AMEER;Address2:时颖;CsRefNo:;Zipcode:75006;Contact:+33643052323;CusRemark:;TrackingNo:;");
        params.add("Strorderproduct", "MaterialRefNo:,MaterialQuantity:1,Price:1.3,Weight:0.3,EnName:clothing,WarehouseID:,ProducingArea:CN,CnName:衣服;;");
        params.add("Stradd", "");
        String response = new RestTemplate().postForObject("http://*************/webservice/APIWebService.asmx/InsertUpdateOrder", params, String.class);
        System.out.println(response);
    }

    @Test
    public void testPrintUrl() throws IOException {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("Secretkey", "39f55000-0a7d-4308-a2a9-29b25bbfd0d480000");
        //R800001811290046
        params.add("OrderNo", "4209023092612927005229000000000033");
        String responseXml = new RestTemplate().postForObject("http://180.167.5.74/webservice/APIWebService.asmx/GetLablesUrl", params, String.class);
        String response = responseXml.substring(responseXml.indexOf('{'), responseXml.indexOf('}') + 1);
        PFCLabelResponse pfcLabelResponse = new ObjectMapper().readValue(response, PFCLabelResponse.class);
        System.out.println(pfcLabelResponse.getSuccess());
        System.out.println(responseXml);
    }

    @Test
    public void testUtf() throws UnsupportedEncodingException {
        String s = EncodeUtils.unicodeToCn("\\u5355\\u53F7\\u4E0D\\u5B58\\u5728");
        System.out.println(s);
    }

    @Test
    public void testSplit() {
        String str = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n"
            + "<string xmlns=\"http://tempuri.org/\">订单保存并提交成功!-R800001903040014-</string>";
        str = str.replaceFirst("\\s*<[^<>]+>\\s*", "");
        String[] split = str.split("\\-");
        System.out.println(split[1]);
    }

    @Test
    public void testSplitStr() {
        String str = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n"
            + "<string xmlns=\"http://tempuri.org/\">{\"success\":\"true\",\"error\":\"\",\"LablesUrl\":\"http://*************/log/DHL_7479442504.pdf\",\"TrackingNo\":\"74890983235172353440\",\"OrderNo\":\"R800001811290046\"}</string>";
        String substring = str.substring(str.indexOf('{'), str.indexOf('}') + 1);
        System.out.println(substring);
        String str1 = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n"
            + "<string xmlns=\"http://tempuri.org/\">[{\"DetailDesc\":\"DRESSER, WI,Out for delivery,Out for delivery with the U.S. Postal Service\",\"OccurTime\":\"2018/11/6 7:50:00\"},{\"DetailDesc\":\"DRESSER, WI,Delivered,Package delivered by U.S. Postal Service to addressee\",\"OccurTime\":\"2018/11/6 10:36:00\"},{\"DetailDesc\":\"OSCEOLA, WI,At U.S. Postal Service facility,Accepted by U.S. Postal Service - Tracking ID 9274890983235172353440\",\"OccurTime\":\"2018/11/5 13:42:00\"},{\"DetailDesc\":\"OSCEOLA, WI,In transit,In transit to U.S. Postal Service\",\"OccurTime\":\"2018/11/5 13:33:21\"},{\"DetailDesc\":\"SAINT PAUL, MN,Departed FedEx location\",\"OccurTime\":\"2018/11/3 3:15:00\"},{\"DetailDesc\":\"SAINT PAUL, MN,Arrived at FedEx location\",\"OccurTime\":\"2018/11/3 1:20:00\"},{\"DetailDesc\":\"SAINT PAUL, MN,Arrived at FedEx location\",\"OccurTime\":\"2018/11/2 11:12:00\"},{\"DetailDesc\":\"LATIMER, IA,In transit\",\"OccurTime\":\"2018/11/1 7:49:10\"},{\"DetailDesc\":\",Shipment information sent to U.S. Postal Service\",\"OccurTime\":\"2018/11/1 23:35:00\"},{\"DetailDesc\":\"FEDEX SMARTPOST OSSEO, MN,Departed FedEx location\",\"OccurTime\":\"2018/11/1 22:08:15\"},{\"DetailDesc\":\"FEDEX SMARTPOST OSSEO, MN,Arrived at FedEx location\",\"OccurTime\":\"2018/11/1 18:38:47\"},{\"DetailDesc\":\"FORT BRIDGER, WY,In transit\",\"OccurTime\":\"2018/10/31 6:30:31\"},{\"DetailDesc\":\"MILFORD, NE,In transit\",\"OccurTime\":\"2018/10/31 19:31:36\"},{\"DetailDesc\":\"LEEDS, UT,In transit\",\"OccurTime\":\"2018/10/30 18:23:47\"},{\"DetailDesc\":\"\",\"OccurTime\":\"\"},{\"DetailDesc\":\"\\u4EA4\\u8FD0\\u53D1\\u5F80\\u76EE\\u7684\\u5730(Shipment sent to destination)\",\"OccurTime\":\"2018/11/29 18:00:06\"},{\"DetailDesc\":\"\\u6536\\u53D6\\u5305\\u88F9(Shipment picked up)\",\"OccurTime\":\"2018/11/29 17:45:24\"}]</string>\n";
        String substring1 = str1.substring(str1.indexOf('['), str1.indexOf(']') + 1);
        System.out.println(substring1);
    }

}
