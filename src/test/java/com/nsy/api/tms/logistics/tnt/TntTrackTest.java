package com.nsy.api.tms.logistics.tnt;

import com.nsy.api.tms.logistics.tnt.request.track.Complete;
import com.nsy.api.tms.logistics.tnt.request.track.LevelOfDetail;
import com.nsy.api.tms.logistics.tnt.request.track.POD;
import com.nsy.api.tms.logistics.tnt.request.track.SearchCriteria;
import com.nsy.api.tms.logistics.tnt.request.track.TNTTrackRequest;
import com.nsy.api.tms.logistics.tnt.response.track.TrackSuccessResponse;
import com.nsy.api.tms.utils.JaxbObjectAndXmlUtil;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.junit.Ignore;
import org.junit.Test;

/**
 * @Author: <PERSON> Lee
 * @Date: 2019/1/22 20:11
 */
public class TntTrackTest {
    @Test
    @Ignore
    public void testTrack() throws Exception {
        TNTTrackRequest tntTrackRequest = new TNTTrackRequest();
        SearchCriteria searchCriteria = new SearchCriteria();
        searchCriteria.setConsignmentNumber("66017653");
        searchCriteria.setMarketType("INTERNATIONAL");
        searchCriteria.setOriginCountry("CN");

        tntTrackRequest.setSearchCriteria(searchCriteria);
        LevelOfDetail levelOfDetail = new LevelOfDetail();
        Complete complete = new Complete();
        complete.setDestinationAddress("false");
        complete.setOriginAddress("false");
        complete.setTrackPackage("false");
        complete.setShipment("false");

        levelOfDetail.setComplete(complete);
        POD pod = new POD();
        pod.setFormat("URL");

        levelOfDetail.setPod(pod);
        tntTrackRequest.setLevelOfDetail(levelOfDetail);
        tntTrackRequest.setLocale("en_US");
        tntTrackRequest.setVersion("3.1");
        String s = JaxbObjectAndXmlUtil.object2Xml(tntTrackRequest);
        System.out.println(s);

        // Post xml request to ExpressLabel
        String url =
                "https://express.tnt.com/expressconnect/track.do";
        PostMethod post = new PostMethod(url);
        // Create the authentication element
        String userPassword = "CNJJ1QCP01" + ":" + "cnjj1cit";
        byte[] encoding = java.util.Base64.getEncoder().encode(userPassword.getBytes());

        System.out.println("Basic " + new String(encoding));
        //此处Basic后面有个空格
        post.addRequestHeader("Authorization", "Basic " + new String(encoding));
        // Create a HttpClient to do the transfer
        post.setParameter("xml_in", s);
        HttpClient httpClient = new HttpClient();
        httpClient.executeMethod(post);
        // Get the response as bytes or a stream for parsing the xml response
        System.out.println(post.getResponseBodyAsString());
        TrackSuccessResponse response = JaxbObjectAndXmlUtil.xml2Object(post.getResponseBodyAsString(), TrackSuccessResponse.class);
        System.out.println(response.getConsignment().getAccess());
        System.out.println(response.getConsignment());
        // System.out.println(response.getConsignment().getStatusData().get(0).getDepotName());
        System.out.println(response.getConsignment().getSummaryCode());

    }
}
